package src

type Es2Param struct {
	SmdpOid                       string `description:"Smdp Oid" default:"" readonly:"true"`
	KmsApiKey                     string `description:"KMS API Key" readonly:"true"`
	KmsApiSecret                  string `description:"KMS API Secret" encryptBase64:"true" readonly:"true"`
	KmsApiUrl                     string `description:"KMS API Url" readonly:"true"`
	KmsApiBackupUrl               string `description:"KMS API Url（备用）" readonly:"true"`
	DefaultRootSmdsDomain         string `description:"DP+指定的默认ROOT SM-DS地址" default:"" readonly:"false"`
	AppleUniversalLinkUrl         string `description:"Apple universal link"  default:"https://esimsetup.apple.com/esim_qrcode_provisioning?carddata=" readonly:"true"`
	DownloadOrderCycleLimitCount  int64  `description:"DownloadOrder数据每次迁移数量" default:"1000" readonly:"true"`
	DownloadOrderCycleExpiredTime int64  `description:"DownloadOrder过期多少天迁移" default:"10" readonly:"true"`
	DownloadOrderCycleStateTime   int64  `description:"DownloadOrder删除状态多少天迁移" default:"30" readonly:"true"`
}

func (ep *Es2Param) ClientParamDescription() string {
	return "ES2+ Params"
}
