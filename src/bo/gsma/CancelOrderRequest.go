package gsma

import "esim.common/src/bo"

type CancelOrderRequest struct {
	bo.RspRequest

	/**
	 * "pattern": "^[0-9]{19}[0-9F]?$",
	 * "description": "ICCID as described in section 5.2.1"
	 */
	Iccid string `json:"iccid,omitempty"`

	/**
	 * "pattern": "^[0-9]{32}$",
	 * "description": "EID as described in section 4.3.1"
	 */
	Eid string `json:"eid,omitempty"`

	/**
	 * "description": "as defined in section {5.3.2}"
	 */
	MatchingId string `json:"matchingId,omitempty"`

	/**
	 * "description": "as defined in section {5.3.4}"
	 * 取值为：Available|Unavailable
	 */
	FinalProfileStatusIndicator string `json:"finalProfileStatusIndicator"`
}
