package gsma

import "esim.common/src/bo"

// DownloadOrderRequest
/**
 * At least one of iccid and profileType SHALL be present. If only profileType is provided, the
 * SM-DP+ SHALL select a Profile of that Profile Type from its inventory.
 */
type DownloadOrderRequest struct {
	bo.RspRequest
	/**
	 * "pattern": "^[0-9]{32}$",
	 * "description": "EID as desc in SGP.02"
	 * Optional
	 */
	Eid string `json:"eid,omitempty"`

	/**
	 * "pattern": "^[0-9]{19}[0-9F]?$",
	 * "description": "ICCID as described in section 5.2.1"
	 */
	Iccid string `json:"iccid,omitempty"`

	/**
	 * "description": "content free information defined by the Operator"
	 */
	ProfileType string `json:"profileType,omitempty"`

	/**
	 * The Confirmation Code is provided by the Operator to the SM-
	 * DP+ and the End User during the Profile download initiation procedure
	 * format: sha256(confirmationCode), hex string
	 */
	ConfirmationCode string `json:"confirmationCode,omitempty"`
}
