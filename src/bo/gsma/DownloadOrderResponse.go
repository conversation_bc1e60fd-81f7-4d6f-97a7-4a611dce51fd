package gsma

import (
	"esim.common/src/bo"
	"k/app"
)

// DownloadOrderResponse
/**
 * "required": ["iccid"]
 * Identification of the Profile to download and install
 * in the eUICC. If ICCID was provided as an input
 * data, the returned value SHALL be the same. If not
 * provided as an input data the returned value
 * SHALL be one of the values available in the SM-
 * DP+ inventory and corresponding to the Profile
 * Type.
 */
type DownloadOrderResponse struct {
	bo.RspResponse

	/**
	 * "pattern": "^[0-9]{19}[0-9F]?$",
	 * "description": "ICCID as described in section 5.2.1"
	 */
	Iccid string `json:"iccid,omitempty"`
}

func NewDownloadOrderResponse() *DownloadOrderResponse {
	return &DownloadOrderResponse{RspResponse: bo.RspResponse{
		Header: &bo.RspResponseHeader{
			FunctionExecutionStatus: bo.NewFunctionExecutionStatusWithSuccess(),
		},
	}}
}

func (dor *DownloadOrderResponse) ProfileTypeUnknown(response *app.Response, typeName string) {
	dor.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	dor.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileType,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedUnknown,
		SubjectIdentifier: typeName,
		Message:           "Indicates that the Profile Type identified by this Profile Type is unknown to the SM-DP+.",
	}
	response.WriteJson(dor)
}

func (dor *DownloadOrderResponse) ProfileTypeUnAvailable(response *app.Response, typeName string) {
	dor.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	dor.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileType,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedUnavailable,
		SubjectIdentifier: typeName,
		Message:           "No more Profile available for the requested Profile Type.",
	}
	response.WriteJson(dor)
}

func (dor *DownloadOrderResponse) ProfileTypeRefused(response *app.Response, typeName string) {
	dor.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	dor.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileType,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedRefused,
		SubjectIdentifier: typeName,
		Message:           "Indicates that the Profile Type identified by this Profile Type is not aligned with the Profile Type of Profile identified by the ICCID.",
	}
	response.WriteJson(dor)
}

func (dor *DownloadOrderResponse) ProfileTypeNotAllowed(response *app.Response, typeName string) {
	dor.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	dor.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileType,
		ReasonCode:        bo.ReasonAccessErrorInvalid,
		SubjectIdentifier: typeName,
		Message:           "Indicates that the function caller is not allowed to perform this function on the Profile Type.",
	}
	response.WriteJson(dor)
}

func (dor *DownloadOrderResponse) ProfileIccidUnknown(response *app.Response, iccid string) {
	dor.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	dor.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileIccid,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedUnknown,
		SubjectIdentifier: iccid,
		Message:           "Indicates that the Profile, identified by this ICCID is unknown to the SM-DP+.",
	}
	response.WriteJson(dor)
}

func (dor *DownloadOrderResponse) ProfileIccidNotAllowed(response *app.Response, iccid string) {
	dor.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	dor.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileIccid,
		ReasonCode:        bo.ReasonAccessErrorInvalid,
		SubjectIdentifier: iccid,
		Message:           "Indicates that the function caller is not allowed to perform this function on the target Profile.",
	}
	response.WriteJson(dor)
}

func (dor *DownloadOrderResponse) ProfileIccidAlreadyInUse(response *app.Response, iccid string) {
	dor.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	dor.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileIccid,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedAlreadyInUse,
		SubjectIdentifier: iccid,
		Message:           "Indicates that the Profile identified by the provided ICCID is not available.",
	}
	response.WriteJson(dor)
}
