package gsma

import (
	"esim.common/src/bo"
	"k/app"
)

// ConfirmOrderResponse
/**
 * "required": []
 */
type ConfirmOrderResponse struct {
	bo.RspResponse

	/**
	 * "pattern": "^[0-9]{32}$",
	 * "description": "EID as desc in SGP.02"
	* Identification of the targeted eUICC. EID SHALL be returned if bound to this order.
	*/
	Eid string `json:"eid,omitempty"`

	/**
	 * "description": "as defined in section {5.3.2}"
	The MatchingID as defined in section (0).
	*/
	MatchingId string `json:"matchingId,omitempty"`

	/**
	 * "description": "as defined in section {5.3.2}"
	The SM-DP+ address to be used for this specific download order. This SHALL be a valid fully-qualified domain name that can be resolved by a public DNS server.
	*/
	SmdpAddress string `json:"smdpAddress,omitempty"`
}

func NewConfirmOrderResponse() *ConfirmOrderResponse {
	return &ConfirmOrderResponse{RspResponse: bo.RspResponse{
		Header: &bo.RspResponseHeader{
			FunctionExecutionStatus: bo.NewFunctionExecutionStatusWithSuccess(),
		},
	}}
}

func (cor *ConfirmOrderResponse) EidMandatoryElementMissing(response *app.Response) {
	cor.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	cor.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode: bo.SubjectEid,
		ReasonCode:  bo.ReasonFormatErrorMandatoryElementMissing,
		Message:     "Indicates that the EID is missing in the context of this order (SM-DS address provided or MatchingID value has zero-length).",
	}
	response.WriteJson(cor)
}

func (cor *ConfirmOrderResponse) SmdsExecutionError(response *app.Response, err string) {
	cor.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	cor.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectSmds,
		ReasonCode:        bo.ReasonProcessingErrorExecutionError,
		SubjectIdentifier: err,
		Message:           "The cascade SM-DS registration has failed. SM-DS has raised an error.",
	}
	response.WriteJson(cor)
}

func (cor *ConfirmOrderResponse) SmdsInaccessible(response *app.Response, typeName string) {
	cor.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	cor.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectSmds,
		ReasonCode:        bo.ReasonTransportErrorInaccessible,
		SubjectIdentifier: typeName,
		Message:           "Indicates that the smdsAddress is invalid or not reachable.",
	}
	response.WriteJson(cor)
}

func (cor *ConfirmOrderResponse) MatchingIdInvalid(response *app.Response) {
	cor.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	cor.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode: bo.SubjectMatchingId,
		ReasonCode:  bo.ReasonFormatErrorInvalid,
		Message:     "Matching ID provided by the Operator is not valid.",
	}
	response.WriteJson(cor)
}

func (cor *ConfirmOrderResponse) MatchingIdAlreadyInUse(response *app.Response) {
	cor.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	cor.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode: bo.SubjectMatchingId,
		ReasonCode:  bo.ReasonConditionsNotSatisfiedAlreadyInUse,
		Message:     "Conflicting MatchingID value.",
	}
	response.WriteJson(cor)
}

func (cor *ConfirmOrderResponse) ProfileIccidUnknown(response *app.Response, iccid string) {
	cor.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	cor.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileIccid,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedUnknown,
		SubjectIdentifier: iccid,
		Message:           "Indicates that the Profile, identified by this ICCID is unknown to the SM-DP+.",
	}
	response.WriteJson(cor)
}

func (cor *ConfirmOrderResponse) ProfileIccidNotAllowed(response *app.Response, iccid string) {
	cor.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	cor.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileIccid,
		ReasonCode:        bo.ReasonAccessErrorInvalid,
		SubjectIdentifier: iccid,
		Message:           "Indicates that the function caller is not allowed to perform this function on the target Profile.",
	}
	response.WriteJson(cor)
}

func (cor *ConfirmOrderResponse) EidInvalidAssociation(response *app.Response) {
	cor.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	cor.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode: bo.SubjectEid,
		ReasonCode:  bo.ReasonConditionsNotSatisfiedInvalidAssociation,
		Message:     "Indicates that a different EID is already associated with this ICCID.",
	}
	response.WriteJson(cor)
}
