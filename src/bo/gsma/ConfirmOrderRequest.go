package gsma

import "esim.common/src/bo"

// ConfirmOrderRequest
/**
"required": ["iccid", "releaseFlag"]
*/
type ConfirmOrderRequest struct {
	bo.RspRequest
	/**
	 * "pattern": "^[0-9]{32}$",
	 * "description": "EID as desc in SGP.02"
	 * Optional
	 */
	Eid string `json:"eid,omitempty"`

	/**
	 * "pattern": "^[0-9]{19}[0-9F]?$",
	 * "description": "ICCID as described in section 5.2.1"
	 */
	Iccid string `json:"iccid,omitempty"`

	/**
	 * "description": "as defined in section {5.3.2}"
	 */
	MatchingId string `json:"matchingId,omitempty"`

	/**
	 * The Confirmation Code is provided by the Operator to the SM-
	 * DP+ and the End User during the Profile download initiation procedure
	 * format: sha256(confirmationCode), hex string
	 */
	ConfirmationCode string `json:"confirmationCode,omitempty"`

	/**
	 * "description": "SM-DS to register the event as defined in section {5.3.2}"
	 */
	SmdsAddress string `json:"smdsAddress,omitempty"`

	/**
	 * "description": "#SupportedFromV3.0.0# Root SM-DS addresses as defined in section 5.3.2"
	 */
	RootSmdsAddress string `json:"rootSmdsAddress,omitempty"`

	/**
	 * "description": "as defined in section {5.3.2}"
	 */
	ReleaseFlag bool `json:"releaseFlag,omitempty"`
}
