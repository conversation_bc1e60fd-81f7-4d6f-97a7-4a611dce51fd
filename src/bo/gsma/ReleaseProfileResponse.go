package gsma

import (
	"esim.common/src/bo"
	"k/app"
)

// ReleaseProfileResponse
/**
 * "required": []
 */
type ReleaseProfileResponse struct {
	bo.RspResponse
}

func NewReleaseProfileResponse() *ReleaseProfileResponse {
	return &ReleaseProfileResponse{RspResponse: bo.RspResponse{
		Header: &bo.RspResponseHeader{
			FunctionExecutionStatus: bo.NewFunctionExecutionStatusWithSuccess(),
		},
	}}
}

func (rpr *ReleaseProfileResponse) SmdsExecutionError(response *app.Response, err string) {
	rpr.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	rpr.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectSmds,
		ReasonCode:        bo.ReasonProcessingErrorExecutionError,
		SubjectIdentifier: err,
		Message:           "The cascade SM-DS registration has failed. SM-DS has raised an error.",
	}
	response.WriteJson(rpr)
}

func (rpr *ReleaseProfileResponse) SmdsInaccessible(response *app.Response, typeName string) {
	rpr.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	rpr.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectSmds,
		ReasonCode:        bo.ReasonTransportErrorInaccessible,
		SubjectIdentifier: typeName,
		Message:           "Indicates that the smdsAddress is invalid or not reachable.",
	}
	response.WriteJson(rpr)
}

func (rpr *ReleaseProfileResponse) ProfileIccidUnknown(response *app.Response, iccid string) {
	rpr.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	rpr.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileIccid,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedUnknown,
		SubjectIdentifier: iccid,
		Message:           "Indicates that the Profile, identified by this ICCID is unknown to the SM-DP+.",
	}
	response.WriteJson(rpr)
}

func (rpr *ReleaseProfileResponse) ProfileIccidInvalidTransition(response *app.Response, iccid string) {
	rpr.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	rpr.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileIccid,
		ReasonCode:        bo.ReasonConditionsInvalidTransition,
		SubjectIdentifier: iccid,
		Message:           "Indicates that the target Profile cannot be released.",
	}
	response.WriteJson(rpr)
}
