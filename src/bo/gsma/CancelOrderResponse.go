package gsma

import (
	"esim.common/src/bo"
	"k/app"
)

// CancelOrderResponse
/**
 */
type CancelOrderResponse struct {
	bo.RspResponse
}

func NewCancelOrderResponse() *CancelOrderResponse {
	return &CancelOrderResponse{RspResponse: bo.RspResponse{
		Header: &bo.RspResponseHeader{
			FunctionExecutionStatus: bo.NewFunctionExecutionStatusWithSuccess(),
		},
	}}
}
func (cor *CancelOrderResponse) ProfileIccidUnknown(response *app.Response) {
	cor.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	cor.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode: bo.SubjectProfileIccid,
		ReasonCode:  bo.ReasonConditionsNotSatisfiedUnknown,
		Message:     "Indicates that the Profile, identified by this ICCID is unknown to the SM-DP+.",
	}
	response.WriteJson(cor)
}

func (cor *CancelOrderResponse) ProfileIccidNotAllowed(response *app.Response, iccid string) {
	cor.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	cor.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileIccid,
		ReasonCode:        bo.ReasonAccessErrorInvalid,
		SubjectIdentifier: iccid,
		Message:           "Indicates that the function caller is not allowed to perform this function on the target Profile.",
	}
	response.WriteJson(cor)
}

func (cor *CancelOrderResponse) ProfileIccidAlreadyInUse(response *app.Response, iccid string) {
	cor.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	cor.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileIccid,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedAlreadyInUse,
		SubjectIdentifier: iccid,
		Message:           "Indicates that the Profile identified by the provided ICCID is not available.",
	}
	response.WriteJson(cor)
}

func (cor *CancelOrderResponse) ProfileIccidMandatoryElementMissing(response *app.Response) {
	cor.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	cor.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode: bo.SubjectProfileIccid,
		ReasonCode:  bo.ReasonFormatErrorMandatoryElementMissing,
		Message:     "Indicates that the ICCID or the finalProfileStatusIndicator is missing in the context of cancelling a Profile download order.",
	}
	response.WriteJson(cor)
}

func (cor *CancelOrderResponse) ProfileIccidInvalidAssociation(response *app.Response, iccid string) {
	cor.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	cor.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileIccid,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedInvalidAssociation,
		SubjectIdentifier: iccid,
		Message:           "Indicates that a different MatchingID is associated with this ICCID or RPM order.",
	}
	response.WriteJson(cor)
}

func (cor *CancelOrderResponse) EidMandatoryElementMissing(response *app.Response) {
	cor.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	cor.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode: bo.SubjectEid,
		ReasonCode:  bo.ReasonFormatErrorMandatoryElementMissing,
		Message:     "Indicates that the EID is missing in the context of this order.",
	}
	response.WriteJson(cor)
}

func (cor *CancelOrderResponse) EidUnknown(response *app.Response) {
	cor.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	cor.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode: bo.SubjectEid,
		ReasonCode:  bo.ReasonConditionsNotSatisfiedUnknown,
		Message:     "Indicates that the eUICC, identified by this EID is unknown to the SM-DP+.",
	}
	response.WriteJson(cor)
}

func (cor *CancelOrderResponse) EidInvalidAssociation(response *app.Response, iccid string) {
	cor.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	cor.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectEid,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedInvalidAssociation,
		SubjectIdentifier: iccid,
		Message:           "Indicates that the RPM Package is associated with different EID.",
	}
	response.WriteJson(cor)
}

func (cor *CancelOrderResponse) MatchingIdMandatoryElementMissing(response *app.Response, iccid string) {
	cor.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	cor.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectMatchingId,
		ReasonCode:        bo.ReasonFormatErrorMandatoryElementMissing,
		SubjectIdentifier: iccid,
		Message:           "Indicates that the MatchingID is missing in the context of this order.",
	}
	response.WriteJson(cor)
}

func (cor *CancelOrderResponse) MatchingIdInvalidAssociation(response *app.Response) {
	cor.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	cor.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode: bo.SubjectMatchingId,
		ReasonCode:  bo.ReasonConditionsNotSatisfiedInvalidAssociation,
		Message:     "Indicates that a different MatchingID is associated with this ICCID or RPM order.",
	}
	response.WriteJson(cor)
}

func (cor *CancelOrderResponse) RpmPackageAlreadyInUse(response *app.Response, iccid string) {
	cor.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	cor.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectLpa,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedAlreadyInUse,
		SubjectIdentifier: iccid,
		Message:           "The RPM Package is already downloaded.",
	}
	response.WriteJson(cor)
}
