package order

import "esim.common/src/bo"

type GenerateByProfileMetadataRequest struct {
	bo.RspRequest
	Iccid               string   `json:"iccid"`
	Imsi                string   `json:"imsi"`
	EncAesKey           string   `json:"encAesKey"`
	ProfileType         string   `json:"profileType"`
	Ki                  string   `json:"ki"`
	Opc                 string   `json:"opc"`
	Pin1                string   `json:"pin1"`
	Pin2                string   `json:"pin2"`
	Puk1                string   `json:"puk1"`
	Puk2                string   `json:"puk2"`
	Adm1                string   `json:"adm1"`
	Msisdn              string   `json:"msisdn"`
	Impi                string   `json:"impi"`
	ImpuList            []string `json:"impuList"`
	C9                  string   `json:"c9"`
	Ara                 string   `json:"ara"`
	Eid                 string   `json:"eid"`
	SmdsAddress         string   `json:"smdsAddress"`
	MatchingId          string   `json:"matchingId"`
	ConfirmationCode    string   `json:"confirmationCode"`
	ServiceProviderName string   `json:"serviceProviderName"`
}
