package order

import (
	"esim.common/src/bo"
	"k/app"
)

type CancelPreOccupyResponse struct {
	bo.RspResponse
}

func NewCancelPreOccupyResponse() *CancelPreOccupyResponse {
	return &CancelPreOccupyResponse{
		RspResponse: bo.RspResponse{
			Header: &bo.RspResponseHeader{
				FunctionExecutionStatus: bo.NewFunctionExecutionStatusWithSuccess(),
			},
		}}
}

func (bpp *CancelPreOccupyResponse) CancelPreOccupyMatchingIdsInvalid(response *app.Response) {
	bpp.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	bpp.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileMetaData,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedUnknown,
		SubjectIdentifier: bo.SubjectProfileMetaData,
		Message:           "The MatchingIds cannot be null and should be between 1 and 200.",
	}
	response.WriteJson(bpp)
}
