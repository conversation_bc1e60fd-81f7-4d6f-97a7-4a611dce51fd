package order

import (
	"esim.es2/src/bo/private"
	"time"
)

type Metadata struct {
	Iccid            string   `json:"iccid"`
	Imsi             string   `json:"imsi"`
	EncAesKey        string   `json:"encAesKey"`
	ProfileType      string   `json:"profileType"`
	Ki               string   `json:"ki"`
	Opc              string   `json:"opc"`
	Pin1             string   `json:"pin1"`
	Pin2             string   `json:"pin2"`
	Puk1             string   `json:"puk1"`
	Puk2             string   `json:"puk2"`
	Adm1             string   `json:"adm1"`
	Msisdn           string   `json:"msisdn"`
	Impi             string   `json:"impi"`
	ImpuList         []string `json:"impuList"`
	C9               string   `json:"c9"`
	Ara              string   `json:"ara"`
	Eid              string   `json:"eid"`
	SmdsAddress      string   `json:"smdsAddress"`
	MatchingId       string   `json:"matchingId"`
	ConfirmationCode string   `json:"confirmationCode"`
}

type UppParamItem struct {
	ParamKeyValueList []*private.ParamKeyValue `json:"paramKeyValueList"`
	ProfileType       string                   `json:"profileType"`
	Eid               string                   `json:"eid"`
	SmdsAddress       string                   `json:"smdsAddress"`
	MatchingId        string                   `json:"matchingId"`
	ConfirmationCode  string                   `json:"confirmationCode"`
}

type GenerateDataInfo struct {
	Iccid            string `json:"iccid"`
	Eid              string `json:"eid"`
	SmdsAddress      string `json:"smdsAddress"`
	MatchingId       string `json:"matchingId"`
	ConfirmationCode string `json:"confirmationCode"`
}

type PageParam struct {
	PageNum  int32 `json:"pageNum"`
	PageSize int32 `json:"pageSize"`
}

type OrderInfo struct {
	Iccid           string `json:"iccid"`
	OrderId         int64  `json:"orderId"`
	Msisdn          string `json:"msisdn"`
	MatchingId      string `json:"matchingId"`
	Eid             string `json:"eid"`
	ProfileTypeName string `json:"profileTypeName"`
	OrderStatus     string `json:"orderStatus"`
	OrderType       string `json:"orderType"`
	UpdateTime      string `json:"updateTime"`
}

type BatchTaskInfoResp struct {
	BatchTaskId int64     `json:"batchTaskId"`
	TaskName    string    `json:"taskName"`
	TaskType    string    `json:"taskType"`
	Status      string    `json:"status"`
	Total       int32     `json:"total"`
	SucceedNum  int32     `json:"succeedNum"`
	FailedNum   int32     `json:"failedNum"`
	CreateTime  time.Time `json:"createTime"`
	EndTime     time.Time `json:"endTime"`
}

type BatchGenerateDetail struct {
	Eid            string `json:"eid"`
	Iccid          string `json:"iccid"`
	ActivationCode string `json:"activationCode"`
	ErrorReason    string `json:"errorReason"`
	Success        bool   `json:"success"`
}

type PageParamResp struct {
	Total    int32 `json:"total"`
	PageNum  int32 `json:"pageNum"`
	PageSize int32 `json:"pageSize"`
}
