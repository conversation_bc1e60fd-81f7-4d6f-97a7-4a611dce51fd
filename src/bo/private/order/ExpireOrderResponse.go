package order

import (
	"esim.common/src/bo"
	"k/app"
)

type ExpireOrderResponse struct {
	bo.RspResponse
}

func NewExpireOrderResponse() *ExpireOrderResponse {
	return &ExpireOrderResponse{RspResponse: bo.RspResponse{
		Header: &bo.RspResponseHeader{
			FunctionExecutionStatus: bo.NewFunctionExecutionStatusWithSuccess(),
		},
	}}
}

func (bpp *ExpireOrderResponse) ExpireOrderICCIDInvalid(response *app.Response) {
	bpp.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	bpp.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileMetaData,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedUnknown,
		SubjectIdentifier: bo.SubjectProfileMetaData,
		Message:           "ICCID cannot be null.",
	}
	response.WriteJson(bpp)
}

func (bpp *ExpireOrderResponse) ExpireOrderFinalProfileStatusIndicatorInvalid(response *app.Response) {
	bpp.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	bpp.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileMetaData,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedUnknown,
		SubjectIdentifier: bo.SubjectProfileMetaData,
		Message:           "finalProfileStatusIndicator cannot be null and must including(Available or Unavailable).",
	}
	response.WriteJson(bpp)
}

func (bpp *ExpireOrderResponse) ExpireOrderICCIDNotExist(response *app.Response) {
	bpp.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	bpp.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileMetaData,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedUnknown,
		SubjectIdentifier: bo.SubjectProfileMetaData,
		Message:           "ICCID must exist!",
	}
	response.WriteJson(bpp)
}

func (bpp *ExpireOrderResponse) ExpireOrderICCIDInvalidStatus(response *app.Response) {
	bpp.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	bpp.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileMetaData,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedUnknown,
		SubjectIdentifier: bo.SubjectProfileMetaData,
		Message:           "ICCID has expired or cancelled!",
	}
	response.WriteJson(bpp)
}
