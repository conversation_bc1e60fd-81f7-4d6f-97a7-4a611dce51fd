package order

import "esim.common/src/bo"

type GenerateByProfileMetadataResponse struct {
	bo.RspResponse
	Iccid          string `json:"iccid"`
	Eid            string `json:"eid"`
	ActivationCode string `json:"activationCode"`
}

func NewGenerateByProfileMetadataResponse() *GenerateByProfileMetadataResponse {
	return &GenerateByProfileMetadataResponse{
		RspResponse: bo.RspResponse{
			Header: &bo.RspResponseHeader{
				FunctionExecutionStatus: bo.NewFunctionExecutionStatusWithSuccess(),
			},
		}}
}
