package order

import (
	"esim.common/src/bo"
	"k/app"
)

type ResetOrderResponse struct {
	bo.RspResponse
}

func NewResetOrderResponse() *ResetOrderResponse {
	return &ResetOrderResponse{RspResponse: bo.RspResponse{
		Header: &bo.RspResponseHeader{
			FunctionExecutionStatus: bo.NewFunctionExecutionStatusWithSuccess(),
		},
	}}
}

func (rop *ResetOrderResponse) ResetOrderICCIDInvalid(response *app.Response) {
	rop.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	rop.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileMetaData,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedUnknown,
		SubjectIdentifier: bo.SubjectProfileMetaData,
		Message:           "ICCID cannot be null.",
	}
	response.WriteJson(rop)
}

func (rop *ResetOrderResponse) ResetOrderICCIDNotExist(response *app.Response) {
	rop.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	rop.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileMetaData,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedUnknown,
		SubjectIdentifier: bo.SubjectProfileMetaData,
		Message:           "ICCID must exist!",
	}
	response.WriteJson(rop)
}
