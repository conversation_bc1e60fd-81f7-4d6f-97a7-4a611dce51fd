package order

import (
	"esim.common/src/bo"
	"k/app"
)

type AppleUniversalLinkResponse struct {
	bo.RspResponse
	Link string `json:"link"`
}

func NewAppleUniversalLinkResponse() *AppleUniversalLinkResponse {
	return &AppleUniversalLinkResponse{RspResponse: bo.RspResponse{
		Header: &bo.RspResponseHeader{
			FunctionExecutionStatus: bo.NewFunctionExecutionStatusWithSuccess(),
		},
	}}
}

func (rop *AppleUniversalLinkResponse) AppleUniversalLinkICCIDInvalid(response *app.Response) {
	rop.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	rop.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileMetaData,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedUnknown,
		SubjectIdentifier: bo.SubjectProfileMetaData,
		Message:           "ICCID cannot be null.",
	}
	response.WriteJson(rop)
}

func (rop *AppleUniversalLinkResponse) AppleUniversalLinkICCIDNotExist(response *app.Response) {
	rop.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	rop.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileMetaData,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedUnknown,
		SubjectIdentifier: bo.SubjectProfileMetaData,
		Message:           "ICCID must exist!",
	}
	response.WriteJson(rop)
}
