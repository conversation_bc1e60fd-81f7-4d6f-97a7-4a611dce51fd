package order

import (
	"esim.common/src/bo"
	"k/app"
)

type SwapEIDResponse struct {
	bo.RspResponse
}

func NewSwapEIDResponse() *SwapEIDResponse {
	return &SwapEIDResponse{RspResponse: bo.RspResponse{
		Header: &bo.RspResponseHeader{
			FunctionExecutionStatus: bo.NewFunctionExecutionStatusWithSuccess(),
		},
	}}
}

func (bpp *SwapEIDResponse) SwapTargetEidInvalid(response *app.Response) {
	bpp.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	bpp.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileMetaData,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedUnknown,
		SubjectIdentifier: bo.SubjectProfileMetaData,
		Message:           "TargetEid device EID  cannot be null.",
	}
	response.WriteJson(bpp)
}

func (bpp *SwapEIDResponse) SwapOriginalEidNotExist(response *app.Response) {
	bpp.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	bpp.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileMetaData,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedUnknown,
		SubjectIdentifier: bo.SubjectProfileMetaData,
		Message:           "Original device EID does not exist!",
	}
	response.WriteJson(bpp)
}
