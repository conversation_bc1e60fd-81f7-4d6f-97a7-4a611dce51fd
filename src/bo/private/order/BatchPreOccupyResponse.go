package order

import (
	"esim.common/src/bo"
	"k/app"
)

type BatchPreOccupyResponse struct {
	bo.RspResponse
	AcList []string `json:"acList"`
}

func NewBatchPreOccupyResponse() *BatchPreOccupyResponse {
	return &BatchPreOccupyResponse{
		RspResponse: bo.RspResponse{
			Header: &bo.RspResponseHeader{
				FunctionExecutionStatus: bo.NewFunctionExecutionStatusWithSuccess(),
			},
		}}
}

func (bpp *BatchPreOccupyResponse) BatchPreOccupyQuantityInvalid(response *app.Response) {
	bpp.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	bpp.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileMetaData,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedUnknown,
		SubjectIdentifier: bo.SubjectProfileMetaData,
		Message:           "The quantity cannot be null and should be between 1 and 200.",
	}
	response.WriteJson(bpp)
}
