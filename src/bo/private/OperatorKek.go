package private

import "esim.common/src/entity/hub/sys"

/*

 */

type OperatorKek struct {
	KekId            int64  `json:"kekId"`
	Category         int    `json:"category"`
	TransportKeyName string `json:"transportKeyName"`
	DataKeyName      string `json:"dataKeyName"`
}

func GetOperatorKek(operator *sys.Operator) *OperatorKek {
	return &OperatorKek{
		KekId:            operator.UpdateTime,
		Category:         operator.UpdateBy,
		TransportKeyName: operator.CreateName,
		DataKeyName:      operator.UpdateName,
	}
}
