package profileType

import (
	"esim.common/src/bo"
	"esim.es2/src/bl/manager"
)

type UpdateProfileTemplateRequest struct {
	bo.RspRequest
	ProfileTypeName      string                            `json:"profileTypeName,omitempty"`
	HplmnRatList         []*manager.PlmnRat                `json:"hplmnRatList,omitempty"`
	FplmnList            []*manager.PlmnParam              `json:"fplmnList,omitempty"`
	OplmnRatList         []*manager.PlmnRat                `json:"oplmnRatList,omitempty"`
	PlmnRatList          []*manager.PlmnRat                `json:"plmnRatList,omitempty"`
	Rplmn                string                            `json:"rplmn,omitempty"`
	SmspParam            []*manager.SmspParam              `json:"smspParam,omitempty"`
	Pnn                  string                            `json:"pnn,omitempty"`
	Sume                 string                            `json:"sume,omitempty"`
	EccParam             *manager.EccParam                 `json:"eccParam,omitempty"`
	AdnParamList         []*manager.AdnParam               `json:"adnParamList,omitempty"`
	SdnParamList         []*manager.AdnParam               `json:"sdnParamList,omitempty"`
	MnoParam             *manager.MnoParam                 `json:"mnoParam,omitempty"`
	UsimRFMParam         *manager.RfmParam                 `json:"usimRFMParam,omitempty"`
	UiccRFMParam         *manager.RfmParam                 `json:"uiccRFMParam,omitempty"`
	IsimParam            *manager.IsimParam                `json:"isimParam,omitempty"`
	Rotation             string                            `json:"rotation,omitempty"`
	Pin1                 string                            `json:"pin1,omitempty"`
	Pin2                 string                            `json:"pin2,omitempty"`
	Adm1                 string                            `json:"adm1,omitempty"`
	Adm2                 string                            `json:"adm2,omitempty"`
	Puk1                 string                            `json:"puk1,omitempty"`
	Puk2                 string                            `json:"puk2,omitempty"`
	AppletTemplate1      *manager.AppletTemplate           `json:"appletTemplate1,omitempty"`
	Gid1                 string                            `json:"gid1,omitempty"`
	Gid2                 string                            `json:"gid2,omitempty"`
	FifthGenerationParam *manager.FifthGenerationParam     `json:"fifthGenerationParam,omitempty"`
	Eap                  string                            `json:"eap,omitempty"`
	EuiccMandatoryAIDs   []*manager.EuiccMandatoryAIDParam `json:"euiccMandatoryAIDs,omitempty"`
	Snpn                 string                            `json:"snpn,omitempty"`
	Prose5g              string                            `json:"prose5G,omitempty"`
}
