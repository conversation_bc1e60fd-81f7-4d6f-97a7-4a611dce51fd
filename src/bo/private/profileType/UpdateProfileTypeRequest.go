package profileType

import "esim.common/src/bo"

type UpdateProfileTypeRequest struct {
	bo.RspRequest
	NotificationRequest
	Name                                         string `json:"name,omitempty"`
	CcRetryLimitExceededNotificationSwitch       bool   `json:"ccRetryLimitExceededNotificationSwitch,omitempty"`
	DownloadRetryLimitExceededNotificationSwitch bool   `json:"downloadRetryLimitExceededNotificationSwitch,omitempty"`
	MaxDownloadAttempts                          int    `json:"maxDownloadAttempts,omitempty"`
	MaxCCAttempts                                int    `json:"maxCCAttempts,omitempty"`
	MaxDownloadSuccessTimes                      int    `json:"maxDownloadSuccessTimes,omitempty"`
	MncLen                                       int8   `json:"mncLen,omitempty"`
	ServiceProviderName                          string `json:"serviceProviderName,omitempty"`
	Prp                                          int    `json:"prp,omitempty"`
	Ara                                          string `json:"ara,omitempty"`
	EcallIndication                              bool   `json:"ecallIndication,omitempty"`
	FallbackAllowed                              bool   `json:"fallbackAllowed,omitempty"`
}

func (r *UpdateProfileTypeRequest) GetNotificationString() string {
	return r.NotificationRequest.convertingNotificationConfiguration()
}
