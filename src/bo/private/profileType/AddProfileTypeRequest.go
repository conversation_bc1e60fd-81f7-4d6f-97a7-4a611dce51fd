package profileType

import (
	"esim.common/src/bo"
	"esim.es2/src/bl/manager"
	"strings"
)

type AddProfileTypeRequest struct {
	bo.RspRequest
	NotificationRequest
	Name                                         string                   `json:"name,omitempty"`
	CcRetryLimitExceededNotificationSwitch       bool                     `json:"ccRetryLimitExceededNotificationSwitch,omitempty"`
	DownloadRetryLimitExceededNotificationSwitch bool                     `json:"downloadRetryLimitExceededNotificationSwitch,omitempty"`
	MaxDownloadAttempts                          int                      `json:"maxDownloadAttempts,omitempty"`
	MaxCCAttempts                                int                      `json:"maxCCAttempts,omitempty"`
	MaxDownloadSuccessTimes                      int                      `json:"maxDownloadSuccessTimes,omitempty"`
	ProfilePolicyRules                           string                   `json:"profilePolicyRules,omitempty"`
	ProfileClass                                 int                      `json:"profileClass,omitempty"`
	MncLen                                       int8                     `json:"mncLen,omitempty"`
	ServiceProviderName                          string                   `json:"serviceProviderName,omitempty"`
	Prp                                          int                      `json:"prp,omitempty"`
	Ara                                          string                   `json:"ara,omitempty"`
	EcallIndication                              bool                     `json:"ecallIndication,omitempty"`
	FallbackAllowed                              bool                     `json:"fallbackAllowed,omitempty"`
	ProfileTemplate                              *manager.ProfileTemplate `json:"profileTemplate,omitempty"`
	IsDefault                                    int32                    `json:"isDefault,omitempty"`
	ReDownloadPolicy                             int                      `json:"reDownloadPolicy,omitempty"`
	DeviceChangeAttemptMaxCount                  int                      `json:"deviceChangeAttemptMaxCount,omitempty"`
	DeviceChangeSupportRecovery                  bool                     `json:"deviceChangeSupportRecovery,omitempty"`
	DeviceChangeRecoveryValidityPeriodMinute     int                      `json:"deviceChangeRecoveryValidityPeriodMinute,omitempty"`
	SupportEuiccFieldTest                        bool                     `json:"supportEuiccFieldTest,omitempty"`
	SupportDeviceChange                          bool                     `json:"supportDeviceChange,omitempty"`
	IconType                                     int8                     `json:"iconType,omitempty"`
	PppModel                                     int32                    `json:"pppModel,omitempty"`
	Icon                                         string                   `json:"icon,omitempty"`
	ServiceProviderMessage                       string                   `json:"serviceProviderMessage,omitempty"`
}

func (r *AddProfileTypeRequest) GetNotificationString() string {
	return r.NotificationRequest.convertingNotificationConfiguration()
}

type NotificationRequest struct {
	InstallNotificationSwitch              bool `json:"installNotificationSwitch,omitempty"`
	EnableNotificationSwitch               bool `json:"enableNotificationSwitch,omitempty"`
	DisableNotificationSwitch              bool `json:"disableNotificationSwitch,omitempty"`
	DeleteNotificationSwitch               bool `json:"deleteNotificationSwitch,omitempty"`
	RpmEnableNotificationSwitch            bool `json:"rpmEnableNotificationSwitch,omitempty"`
	RpmDisableNotificationSwitch           bool `json:"rpmDisableNotificationSwitch,omitempty"`
	RpmDeleteNotificationSwitch            bool `json:"rpmDeleteNotificationSwitch,omitempty"`
	RpmLoadPackageResultNotificationSwitch bool `json:"rpmLoadPackageResultNotificationSwitch,omitempty"`
}

type NotificationRequestInterface interface {
	GetNotificationString() string
}

func (r *NotificationRequest) convertingNotificationConfiguration() string {
	boolSlice := []bool{
		r.InstallNotificationSwitch,
		r.EnableNotificationSwitch,
		r.DisableNotificationSwitch,
		r.DeleteNotificationSwitch,
		r.RpmEnableNotificationSwitch,
		r.RpmDisableNotificationSwitch,
		r.RpmDeleteNotificationSwitch,
		r.RpmLoadPackageResultNotificationSwitch,
	}
	strSlice := make([]string, len(boolSlice))
	for i, b := range boolSlice {
		if b {
			strSlice[i] = "1"
		} else {
			strSlice[i] = "0"
		}
	}
	return strings.Join(strSlice, "")
}
