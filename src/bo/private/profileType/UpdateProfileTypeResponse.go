package profileType

import (
	"esim.common/src/bo"
	"k/app"
)

type UpdateProfileTypeResponse struct {
	bo.RspResponse
	ProfileTypeName     string `json:"profileTypeName"`
	ProfileTemplateName string `json:"profileTemplateName"`
}

func NewProfileTypeUpdateResponse() *UpdateProfileTypeResponse {
	return &UpdateProfileTypeResponse{RspResponse: bo.RspResponse{
		Header: &bo.RspResponseHeader{
			FunctionExecutionStatus: bo.NewFunctionExecutionStatusWithSuccess(),
		},
	}}
}

func (bpp *UpdateProfileTypeResponse) ProfileTypeNotExistsResponse(response *app.Response, profileType string) {
	bpp.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	bpp.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileType,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedUnknown,
		SubjectIdentifier: profileType,
		Message:           "Indicates that the Profile Type is not exists in the SM-DP+.",
	}
	response.WriteJson(bpp)
}
