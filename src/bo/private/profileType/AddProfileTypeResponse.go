package profileType

import (
	"esim.common/src/bo"
	"k/app"
)

type AddProfileTypeResponse struct {
	bo.RspResponse
	ProfileTypeName string `json:"profileTypeName"`
}

func NewProfileTypeAddResponse() *AddProfileTypeResponse {
	return &AddProfileTypeResponse{RspResponse: bo.RspResponse{
		Header: &bo.RspResponseHeader{
			FunctionExecutionStatus: bo.NewFunctionExecutionStatusWithSuccess(),
		},
	}}
}

func (bpp *AddProfileTypeResponse) ProfileTypeAlreadyExistsResponse(response *app.Response, profileType string) {
	bpp.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	bpp.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileType,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedUnknown,
		SubjectIdentifier: profileType,
		Message:           "Indicates that the Profile Type is already exists in the SM-DP+.",
	}
	response.WriteJson(bpp)
}

func (bpp *AddProfileTypeResponse) ProfileTypeUppContentMissResponse(response *app.Response, profileType string) {
	bpp.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	bpp.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileType,
		ReasonCode:        bo.ReasonFormatErrorConditionElementMissing,
		SubjectIdentifier: profileType,
		Message:           "Indicates that the Profile Type Upp Content is Missing.",
	}
	response.WriteJson(bpp)
}
