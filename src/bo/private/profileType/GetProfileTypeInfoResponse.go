package profileType

import (
	"esim.common/src/bo"
	"k/app"
)

type GetProfileTypeInfoResponse struct {
	bo.RspResponse
	ProfileTypeName        string   `json:"profileTypeName"`
	DefaultType            bool     `json:"defaultType"`
	Ppr                    string   `json:"ppr"`
	ProfileClass           string   `json:"profileClass"`
	MaxDownloadTime        int      `json:"maxDownloadTime"`
	MaxCCTime              int      `json:"maxCCTime"`
	MaxDownloadSuccessTime int32    `json:"maxDownloadSuccessTime"`
	NotificationEvent      []string `json:"notificationEvent"`
	EcallIndication        bool     `json:"ecallIndication"`
	FallbackAllowed        bool     `json:"fallbackAllowed"`
	CreatedTime            string   `json:"createdTime"`
	LastModified           string   `json:"lastModified"`
	Prp                    int      `json:"prp"`
	Group                  string   `json:"group"`
	Operator               string   `json:"operator"`
}

func NewProfileTypeInfoResponse() *GetProfileTypeInfoResponse {
	return &GetProfileTypeInfoResponse{RspResponse: bo.RspResponse{
		Header: &bo.RspResponseHeader{
			FunctionExecutionStatus: bo.NewFunctionExecutionStatusWithSuccess(),
		},
	}}
}

func (bpp *GetProfileTypeInfoResponse) ProfileTypeNotExistsResponse(response *app.Response, profileType string) {
	bpp.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	bpp.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileType,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedUnknown,
		SubjectIdentifier: profileType,
		Message:           "Indicates that the Profile Type is not exists in the SM-DP+.",
	}
	response.WriteJson(bpp)
}
