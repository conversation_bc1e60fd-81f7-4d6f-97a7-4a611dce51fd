package profileType

import (
	"esim.common/src/bo"
	"esim.es2/src/bl/manager"
)

type AddProfileTypeByUppRequest struct {
	bo.RspRequest
	NotificationRequest
	Name                                         string                      `json:"name,omitempty"`
	CcRetryLimitExceededNotificationSwitch       bool                        `json:"ccRetryLimitExceededNotificationSwitch,omitempty"`
	DownloadRetryLimitExceededNotificationSwitch bool                        `json:"downloadRetryLimitExceededNotificationSwitch,omitempty"`
	MaxDownloadAttempts                          int                         `json:"maxDownloadAttempts,omitempty"`
	MaxCCAttempts                                int                         `json:"maxCCAttempts,omitempty"`
	MaxDownloadSuccessTimes                      int                         `json:"maxDownloadSuccessTimes,omitempty"`
	ProfilePolicyRules                           string                      `json:"profilePolicyRules,omitempty"`
	ProfileClass                                 int                         `json:"profileClass,omitempty"`
	MncLen                                       int8                        `json:"mncLen,omitempty"`
	ServiceProviderName                          string                      `json:"serviceProviderName,omitempty"`
	Prp                                          int32                       `json:"prp,omitempty"`
	Ara                                          string                      `json:"ara,omitempty"`
	UppProfileTemplate                           *manager.UppProfileTemplate `json:"uppProfileTemplate,omitempty"`
	IsDefault                                    int32                       `json:"isDefault,omitempty"`
	ReDownloadPolicy                             int                         `json:"reDownloadPolicy,omitempty"`
	DeviceChangeAttemptMaxCount                  int                         `json:"deviceChangeAttemptMaxCount,omitempty"`
	DeviceChangeSupportRecovery                  bool                        `json:"deviceChangeSupportRecovery,omitempty"`
	DeviceChangeRecoveryValidityPeriodMinute     int                         `json:"deviceChangeRecoveryValidityPeriodMinute,omitempty"`
	SupportEuiccFieldTest                        bool                        `json:"supportEuiccFieldTest,omitempty"`
	SupportDeviceChange                          bool                        `json:"supportDeviceChange,omitempty"`
	IconType                                     int8                        `json:"iconType,omitempty"`
	PppModel                                     int32                       `json:"pppModel,omitempty"`
	Icon                                         string                      `json:"icon,omitempty"`
	ServiceProviderMessage                       string                      `json:"serviceProviderMessage,omitempty"`
}

func (r *AddProfileTypeByUppRequest) GetNotificationString() string {
	return r.NotificationRequest.convertingNotificationConfiguration()
}
