package device

type Deviceinfo struct {
	Imei       string `json:"imei"`
	Tac        string `json:"tac"`
	DeviceName string `json:"deviceName"`
	Brand      string `json:"brand"`
	DeviceType string `json:"deviceType"`
}

type DeviceOrder struct {
	Iccid                  string   `json:"iccid"`
	State                  string   `json:"state"`
	CreatedTime            string   `json:"createdTime"`
	LastModified           string   `json:"lastModified"`
	MnoId                  int32    `json:"mnoId"`
	MccMnc                 string   `json:"mccmnc"`
	ProfileTypeName        string   `json:"profileTypeName"`
	ProfileClass           string   `json:"profileClass"`
	AssociatedEid          string   `json:"associatedEid"`
	ActivationCode         string   `json:"activationCode"`
	SmdpOid                string   `json:"smdpOid"`
	SmdpAddress            string   `json:"smdpAddress"`
	MaxDownloadTime        int      `json:"maxDownloadTime"`
	MaxCCTime              int      `json:"maxCCTime"`
	MaxDownloadSuccessTime int      `json:"maxDownloadSuccessTime"`
	Ppr                    string   `json:"ppr"`
	NotificationEvent      []string `json:"notificationEvent"`
	ReDownloadRule         string   `json:"reDownloadRule"`
}
