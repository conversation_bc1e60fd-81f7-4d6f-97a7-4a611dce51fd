package device

import (
	"esim.common/src/bo"
	"k/app"
)

type GetDeviceInfoResponse struct {
	bo.RspResponse
	Orders []*DeviceOrder `json:"orders"`
	Info   *Deviceinfo    `json:"info"`
	Eid    string         `json:"eid"`
	Block  bool           `json:"block"`
}

func NewGetDeviceInfoResponse() *GetDeviceInfoResponse {
	return &GetDeviceInfoResponse{RspResponse: bo.RspResponse{
		Header: &bo.RspResponseHeader{
			FunctionExecutionStatus: bo.NewFunctionExecutionStatusWithSuccess(),
		},
	}}
}

func (bpp *GetDeviceInfoResponse) DeviceInfoEIDInvalid(response *app.Response) {
	bpp.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	bpp.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileMetaData,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedUnknown,
		SubjectIdentifier: bo.SubjectProfileMetaData,
		Message:           "EID cannot be null.",
	}
	response.WriteJson(bpp)
}
func (bpp *GetDeviceInfoResponse) DeviceInfoNotExist(response *app.Response) {
	bpp.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	bpp.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileMetaData,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedUnknown,
		SubjectIdentifier: bo.SubjectProfileMetaData,
		Message:           "The device identified by eid has not exist!",
	}
	response.WriteJson(bpp)
}
