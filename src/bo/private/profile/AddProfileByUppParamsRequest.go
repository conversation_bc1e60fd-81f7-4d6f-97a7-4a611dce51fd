package profile

import (
	"esim.common/src/bo"
	"esim.es2/src/bo/private"
)

type AddProfileByUppParamsRequest struct {
	bo.RspRequest
	ParamKeyValueList []*private.ParamKeyValue `json:"paramKeyValueList,omitempty"`
	K4Name            string                   `json:"k4Name,omitempty"`
	ProfileType       string                   `json:"profileType,omitempty"`
	EncAesKey         string                   `json:"encAesKey,omitempty"`
}
