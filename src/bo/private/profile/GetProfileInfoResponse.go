package profile

import (
	"esim.common/src/bo"
	"k/app"
)

type GetProfileInfoResponse struct {
	bo.RspResponse
	Iccid                  string   `json:"iccid"`
	State                  string   `json:"state"`
	CreatedTime            string   `json:"createdTime"`
	LastModified           string   `json:"lastModified"`
	MnoId                  int32    `json:"mnoId"`
	MccMnc                 string   `json:"mccmnc"`
	ProfileTypeName        string   `json:"profileTypeName"`
	ProfileClass           string   `json:"profileClass"`
	Prp                    int      `json:"prp"`
	AssociatedEid          string   `json:"associatedEid"`
	SmdpOid                string   `json:"smdpOid"`
	ActivationCode         string   `json:"activationCode"`
	SmdpAddress            string   `json:"smdpAddress"`
	MaxDownloadTime        int      `json:"maxDownloadTime"`
	MaxCCTime              int      `json:"maxCCTime"`
	MaxDownloadSuccessTime int      `json:"maxDownloadSuccessTime"`
	Ppr                    string   `json:"ppr"`
	NotificationEvent      []string `json:"notificationEvent"`
	Group                  string   `json:"group"`
	Operator               string   `json:"operator"`
	Msisdn                 string   `json:"msisdn"`
	ServiceProviderName    string   `json:"serviceProviderName"`
	Ara                    string   `json:"ara"`
	Pin1                   string   `json:"pin1"`
	Pin2                   string   `json:"pin2"`
	Puk1                   string   `json:"puk1"`
	Puk2                   string   `json:"puk2"`
}

func NewProfileInfoResponse() *GetProfileInfoResponse {
	return &GetProfileInfoResponse{RspResponse: bo.RspResponse{
		Header: &bo.RspResponseHeader{
			FunctionExecutionStatus: bo.NewFunctionExecutionStatusWithSuccess(),
		},
	}}
}

func (bpp *GetProfileInfoResponse) ProfileNotExist(response *app.Response, iccid string) {
	bpp.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	bpp.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfile,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedUnknown,
		SubjectIdentifier: iccid,
		Message:           "Indicates that the Profile is not exist to the SM-DP+.",
	}
	response.WriteJson(bpp)
}
