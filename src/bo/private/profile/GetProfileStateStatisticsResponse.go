package profile

import (
	"esim.common/src/bo"
	"k/app"
)

type GetProfileStateStatisticsResponse struct {
	bo.RspResponse
	ProfileType string                    `json:"profileType"`
	Total       *int64                    `json:"total"`
	Statistics  []*ProfileStateStatistics `json:"statistics"`
}

type ProfileStateStatistics struct {
	State string `json:"state"`
	Count int64  `json:"count"`
}

func NewGetProfileStateStatisticsResponse() *GetProfileStateStatisticsResponse {
	return &GetProfileStateStatisticsResponse{
		RspResponse: bo.RspResponse{
			Header: &bo.RspResponseHeader{
				FunctionExecutionStatus: bo.NewFunctionExecutionStatusWithSuccess(),
			},
		}}
}

func (bpp *GetProfileStateStatisticsResponse) ProfileTypeNotInvalid(response *app.Response, profileType string) {
	bpp.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	bpp.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileType,
		ReasonCode:        bo.ReasonFormatErrorInvalid,
		SubjectIdentifier: profileType,
		Message:           "Indicates that the Profile Type is invalid in the SM-DP+.",
	}
	response.WriteJson(bpp)
}
