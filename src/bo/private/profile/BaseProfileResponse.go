package profile

import (
	"esim.common/src/bo"
	"k/app"
)

type BaseProfileResponse struct {
	bo.RspResponse
	Iccid string `json:"iccid"`
}

func NewBaseProfileResponse() *BaseProfileResponse {
	return &BaseProfileResponse{RspResponse: bo.RspResponse{
		Header: &bo.RspResponseHeader{
			FunctionExecutionStatus: bo.NewFunctionExecutionStatusWithSuccess(),
		},
	}}
}

func (bpp *BaseProfileResponse) DecryptUppFormatError(response *app.Response, iccid string) {
	bpp.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	bpp.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileMetaData,
		ReasonCode:        bo.ReasonFormatErrorMandatoryElementMissing,
		SubjectIdentifier: iccid,
		Message:           "Indicates that the Profile Upp format is error.",
	}
	response.WriteJson(bpp)
}

func (bpp *BaseProfileResponse) IccidFormatError(response *app.Response, iccid string) {
	bpp.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	bpp.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileIccid,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedRefused,
		SubjectIdentifier: iccid,
		Message:           "Indicates that the Iccid format is error.",
	}
	response.WriteJson(bpp)
}

func (bpp *BaseProfileResponse) DecryptImsiKiOpcError(response *app.Response, keyName string) {
	bpp.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	bpp.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileMetaData,
		ReasonCode:        bo.ReasonFormatErrorMandatoryElementMissing,
		SubjectIdentifier: keyName,
		Message:           "Indicates that the Imsi/Ki/Opc format is error.",
	}
	response.WriteJson(bpp)
}

func (bpp *BaseProfileResponse) ProfileAlreadyExist(response *app.Response, iccid string) {
	bpp.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	bpp.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfile,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedRefused,
		SubjectIdentifier: iccid,
		Message:           "Indicates that the Profile is already exist in the SM-DP+.",
	}
	response.WriteJson(bpp)
}

func (bpp *BaseProfileResponse) ProfileNotExist(response *app.Response, iccid string) {
	bpp.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	bpp.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfile,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedUnknown,
		SubjectIdentifier: iccid,
		Message:           "Indicates that the Profile is not exist.",
	}
	response.WriteJson(bpp)
}

func (bpp *GetProfileStateStatisticsResponse) ProfileNotExist(response *app.Response, iccid string) {
	bpp.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	bpp.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfile,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedUnknown,
		SubjectIdentifier: iccid,
		Message:           "Indicates that the Profile is not exist.",
	}
	response.WriteJson(bpp)
}

func (bpp *BaseProfileResponse) ProfileTypeNotExist(response *app.Response, profileType string) {
	bpp.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	bpp.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileType,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedUnknown,
		SubjectIdentifier: profileType,
		Message:           "Indicates that the Profile Type is not exist.",
	}
	response.WriteJson(bpp)
}

func (bpp *BaseProfileResponse) ProfileUppNotExist(response *app.Response, profileType string) {
	bpp.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	bpp.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfileMetaData,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedUnknown,
		SubjectIdentifier: profileType,
		Message:           "Indicates that the Profile Upp is not exist.",
	}
	response.WriteJson(bpp)
}

func (bpp *BaseProfileResponse) ProfileStateNotAvailable(response *app.Response, iccid string) {
	bpp.Header.FunctionExecutionStatus.Status = bo.ExecutionStatusFail
	bpp.Header.FunctionExecutionStatus.StatusCodeData = &bo.StatusCode{
		SubjectCode:       bo.SubjectProfile,
		ReasonCode:        bo.ReasonConditionsNotSatisfiedUnknown,
		SubjectIdentifier: iccid,
		Message:           "Indicates that the Profile State is not available.",
	}
	response.WriteJson(bpp)
}
