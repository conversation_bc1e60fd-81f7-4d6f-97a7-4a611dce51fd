package profile

import "esim.common/src/bo"

type AddProfileByMetaRequest struct {
	bo.RspRequest
	Iccid               string   `json:"iccid,omitempty"`
	Imsi                string   `json:"imsi,omitempty"`
	Ki                  string   `json:"ki,omitempty"`
	Opc                 string   `json:"opc,omitempty"`
	Pin1                string   `json:"pin1,omitempty"`
	Pin2                string   `json:"pin2,omitempty"`
	Puk1                string   `json:"puk1,omitempty"`
	Puk2                string   `json:"puk2,omitempty"`
	Adm1                string   `json:"adm1,omitempty"`
	Msisdn              string   `json:"msisdn,omitempty"`
	Name                string   `json:"name,omitempty"`
	Impi                string   `json:"impi,omitempty"`
	ImpuList            []string `json:"impuList,omitempty"`
	C9                  string   `json:"c9,omitempty"`
	Ara                 string   `json:"ara,omitempty"`
	ProfileType         string   `json:"profileType,omitempty"`
	EncAesKey           string   `json:"encAesKey,omitempty"`
	ServiceProviderName string   `json:"serviceProviderName,omitempty"`
}
