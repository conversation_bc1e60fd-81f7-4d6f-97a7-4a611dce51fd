package src

import (
	"k/app"
)

type Es2Version struct {
}

func (v Es2Version) Version() *app.Version {
	return &app.Version{
		Major: 1,
		Minor: 0,
		Build: 4,
		Code:  "ES2+",
		Date:  "20250717",
	}
}

func (v Es2Version) Update(from string) *app.VersionUpdate {
	switch from {
	case "1.0.3":
		return &app.VersionUpdate{
			To: "1.0.4", Date: "20250717",
			Description: []string{"GenerateByProfileMetadata新增参数serviceProviderName"},
		}
	case "1.0.2":
		return &app.VersionUpdate{
			To:          "1.0.3",
			Date:        "20250710",
			Description: []string{"ES2+优化ProfileInfo查询性能"},
		}
	case "1.0.1":
		return &app.VersionUpdate{
			To:          "1.0.2",
			Date:        "20250417",
			Description: []string{"ES2+新增PreOccupy系列接口"},
		}
	case "1.0.0":
		return &app.VersionUpdate{
			To:          "1.0.1",
			Date:        "20250317",
			Description: []string{"ES2+ Private API Update"},
		}

	case "":
		return &app.VersionUpdate{
			To:          "1.0.0",
			Description: []string{"初始版本"},
		}
	}
	return nil
}
