package ui

import (
	"esim.es2/src/bl/handler"
	"k/app"
)

type ProfileEvent struct {
}

func (pe *ProfileEvent) AddByUpp(_ app.IsInstanceEvent, ph *handler.ProfileHandler, request *app.Request, response *app.Response) {
	ph.AddProfileByUpp(request, response)
}

func (pe *ProfileEvent) AddByMeta(_ app.IsInstanceEvent, ph *handler.ProfileHandler, request *app.Request, response *app.Response) {
	ph.AddProfileByMeta(request, response)
}

func (pe *ProfileEvent) AddByUppParams(_ app.IsInstanceEvent, ph *handler.ProfileHandler, request *app.Request, response *app.Response) {
	ph.AddProfileByUppParams(request, response)
}

func (pe *ProfileEvent) UpdateByUppParams(_ app.IsInstanceEvent, ph *handler.ProfileHandler, request *app.Request, response *app.Response) {
	ph.UpdateByUppParams(request, response)
}

func (pe *ProfileEvent) Delete(_ app.IsInstanceEvent, ph *handler.ProfileHandler, request *app.Request, response *app.Response) {
	ph.DeleteProfile(request, response)
}

func (pe *ProfileEvent) DeleteProfile(_ app.IsInstanceEvent, ph *handler.ProfileHandler, request *app.Request, response *app.Response) {
	ph.DeleteProfile(request, response)
}

func (pe *ProfileEvent) Info(_ app.IsInstanceEvent, ph *handler.ProfileHandler, request *app.Request, response *app.Response) {
	ph.GetProfileInfo(request, response)
}

func (pe *ProfileEvent) UpdateParam(_ app.IsInstanceEvent, ph *handler.ProfileHandler, request *app.Request, response *app.Response) {
	ph.UpdateProfileParam(request, response)
}

func (pe *ProfileEvent) GetStateStatistics(_ app.IsInstanceEvent, ph *handler.ProfileHandler, request *app.Request, response *app.Response) {
	ph.GetProfileStateStatistics(request, response)
}

func (pe *ProfileEvent) ReGenerateUpp(_ app.IsInstanceEvent, ph *handler.ProfileHandler, request *app.Request, response *app.Response) {
	ph.ReGenerateUpp(request, response)
}

func (pe *ProfileEvent) GetProfileStateStatistics(_ app.IsInstanceEvent, ph *handler.ProfileHandler, request *app.Request, response *app.Response) {
	ph.GetProfileStateStatistics(request, response)
}
