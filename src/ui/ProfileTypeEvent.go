package ui

import (
	"esim.es2/src/bl/handler"
	"k/app"
)

type ProfileTypeEvent struct {
}

func (pte *ProfileTypeEvent) List(_ app.IsInstanceEvent, pth *handler.ProfileTypeHandler, request *app.Request, response *app.Response) {
	pth.ListProfileTypes(request, response)
}

func (pte *ProfileTypeEvent) Info(_ app.IsInstanceEvent, pth *handler.ProfileTypeHandler, request *app.Request, response *app.Response) {
	pth.GetProfileTypeInfo(request, response)
}

func (pte *ProfileTypeEvent) Add(_ app.IsInstanceEvent, pth *handler.ProfileTypeHandler, request *app.Request, response *app.Response) {
	pth.AddProfileType(request, response)
}

func (pte *ProfileTypeEvent) Update(_ app.IsInstanceEvent, pth *handler.ProfileTypeHandler, request *app.Request, response *app.Response) {
	pth.UpdateProfileType(request, response)
}

func (pte *ProfileTypeEvent) UppAdd(_ app.IsInstanceEvent, pth *handler.ProfileTypeHandler, request *app.Request, response *app.Response) {
	pth.AddProfileTypeByUPP(request, response)
}

func (pte *ProfileTypeEvent) UppUpdate(_ app.IsInstanceEvent, pth *handler.ProfileTypeHandler, request *app.Request, response *app.Response) {
	pth.UpdateProfileTemplateByUPP(request, response)
}

func (pte *ProfileTypeEvent) TemplateUpdate(_ app.IsInstanceEvent, pth *handler.ProfileTypeHandler, request *app.Request, response *app.Response) {
	pth.UpdateProfileTemplate(request, response)
}
