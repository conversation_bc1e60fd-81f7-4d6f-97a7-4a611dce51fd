package ui

import (
	"esim.es2/src/bl/handler"
	"k/app"
)

type GsmaEvent struct {
}

func (ge *GsmaEvent) DownloadOrder(_ app.IsInstanceEvent, gh *handler.GsmaHandler, request *app.Request, response *app.Response) {
	gh.DownloadOrder(request, response)
}

func (ge *GsmaEvent) ConfirmOrder(_ app.IsInstanceEvent, gh *handler.GsmaHandler, request *app.Request, response *app.Response) {
	gh.ConfirmOrder(request, response)
}

func (ge *GsmaEvent) CancelOrder(_ app.IsInstanceEvent, gh *handler.GsmaHandler, request *app.Request, response *app.Response) {
	gh.CancelOrder(request, response)
}

func (ge *GsmaEvent) ReleaseProfile(_ app.IsInstanceEvent, gh *handler.GsmaHandler, request *app.Request, response *app.Response) {
	gh.ReleaseProfile(request, response)
}
