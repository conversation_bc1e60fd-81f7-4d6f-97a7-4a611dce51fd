package ui

import (
	"esim.es2/src/bl/handler"
	"k/app"
)

type OrderEvent struct {
}

func (ov *OrderEvent) Swap(_ app.IsInstanceEvent, oh *handler.OrderHandler, request *app.Request, response *app.Response) {
	oh.SwapEID(request, response)
}

func (ov *OrderEvent) GenerateByProfileMetadata(_ app.IsInstanceEvent, oh *handler.OrderHandler, request *app.Request, response *app.Response) {
	oh.GenerateByProfileMetadata(request, response)
}

func (ov *OrderEvent) BatchGenerateByProfileMetadata(_ app.IsInstanceEvent, oh *handler.OrderHandler, request *app.Request, response *app.Response) {
	oh.BatchGenerateByProfileMetadata(request, response)
}

func (ov *OrderEvent) BatchGenerateByProfileUppParams(_ app.IsInstanceEvent, oh *handler.OrderHandler, request *app.Request, response *app.Response) {
	oh.BatchGenerateByProfileUppParams(request, response)
}

func (ov *OrderEvent) BatchGenerate(_ app.IsInstanceEvent, oh *handler.OrderHandler, request *app.Request, response *app.Response) {
	oh.BatchGenerate(request, response)
}

func (ov *OrderEvent) BatchTaskInfo(_ app.IsInstanceEvent, oh *handler.OrderHandler, request *app.Request, response *app.Response) {
	oh.BatchTaskInfo(request, response)
}

func (ov *OrderEvent) Expire(_ app.IsInstanceEvent, oh *handler.OrderHandler, request *app.Request, response *app.Response) {
	oh.ExpireOrder(request, response)
}

func (ov *OrderEvent) ResetOrder(_ app.IsInstanceEvent, oh *handler.OrderHandler, request *app.Request, response *app.Response) {
	oh.ResetOrderDownloadCount(request, response)
}

func (ov *OrderEvent) UpdateProfileParamAndGenerateAC(_ app.IsInstanceEvent, oh *handler.OrderHandler, request *app.Request, response *app.Response) {
	oh.UpdateProfileParamAndGenerateAC(request, response)
}

func (ov *OrderEvent) GetAppleUniversalLink(_ app.IsInstanceEvent, oh *handler.OrderHandler, request *app.Request, response *app.Response) {
	oh.GetAppleUniversalLink(request, response)
}

func (ov *OrderEvent) GetOrderInfo(_ app.IsInstanceEvent, oh *handler.OrderHandler, request *app.Request, response *app.Response) {
	oh.GetOrderInfo(request, response)
}

func (ov *OrderEvent) Info(_ app.IsInstanceEvent, oh *handler.OrderHandler, request *app.Request, response *app.Response) {
	oh.GetOrderInfo(request, response)
}

func (ov *OrderEvent) BatchPreOccupy(_ app.IsInstanceEvent, oh *handler.OrderHandler, request *app.Request, response *app.Response) {
	oh.BatchPreOccupy(request, response)
}

func (ov *OrderEvent) CancelPreOccupy(_ app.IsInstanceEvent, oh *handler.OrderHandler, request *app.Request, response *app.Response) {
	oh.CancelPreOccupy(request, response)
}
