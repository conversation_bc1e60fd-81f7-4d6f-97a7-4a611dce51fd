package src_test

import (
	"fmt"
	"k/asn"
	"strconv"
	"testing"
)

func TestBitOperate(t *testing.T) {
	bServer := byte(1 << 0)
	fmt.Println(strconv.FormatInt(int64(bServer), 2))
	bN := byte(0xc0)
	fmt.Println(asn.ByteToHex(bN | byte(bServer)))
}

func TestB4(t *testing.T) {
	b4 := asn.NewTlvByHexTag("b4", nil)
	b4_a7 := b4.FindChildByTagHex("a7")
	if b4_a7 == nil {
		b4_a7 = asn.NewTlvByHexTag("a7", nil)
		b4.Append(b4_a7)
	}
	b4_a7_83 := b4_a7.FindChildByTagHex("83")
	if b4_a7_83 == nil {
		b4_a7_83 = asn.NewTlvByHexTag("83", nil)
		b4_a7.Append(b4_a7_83)
	}
	b4Af := b4.FindChildByTagHex("af")
	if b4Af == nil {
		b4Af = asn.NewTlvByHexTag("af", nil)
		b4.Append(b4Af)
	}
	b4Af83 := b4Af.FindChildByTagHex("83")
	if b4Af83 == nil {
		b4Af83 = asn.NewTlvByHexTag("83", nil)
		b4Af.Append(b4Af83)
	}
	fmt.Println(b4)
}
