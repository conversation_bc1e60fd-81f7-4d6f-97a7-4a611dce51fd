package manager

import (
	"esim.common/src/bo"
	"esim.common/src/entity/dp/mno"
	"esim.common/src/entity/dp/rsp"
	"esim.common/src/entity/hub/sys"
	"k/app"
)

type GenerateACManager struct {
	A *app.App
}

func (gam *GenerateACManager) DownloadOrder(entity *mno.Profile, eid string, confirmationCode string, response *app.Response, operator *sys.Operator) *rsp.DownloadOrder {
	defer func() {
		if err := recover(); err != nil {
			bo.NewInternalExceptionResponse(response, "DownloadOrder Exception")
		}
	}()
	return nil
}

func (gam *GenerateACManager) ConfirmOrder(entity *mno.Profile, downloadOrder *rsp.DownloadOrder, matchingId string, confirmationCode string, smdsAddress string, operatorKek sys.OperatorKek, response *app.Response, operator *sys.Operator) bool {
	defer func() {
		if err := recover(); err != nil {
			bo.NewInternalExceptionResponse(response, "ConfirmOrder Exception")
		}
	}()
	return false
}

func (gam *GenerateACManager) ReleaseProfile(entity *mno.Profile, downloadOrder *rsp.DownloadOrder, response *app.Response, operator *sys.Operator) bool {
	defer func() {
		if err := recover(); err != nil {
			bo.NewInternalExceptionResponse(response, "ReleaseProfile Exception")
		}
	}()
	return false
}
