package manager

import (
	"esim.common/src/entity/dp/dev"
	"k/app"
	"k/db"
)

type DeviceManager struct {
	A *app.App
}

func (dm *DeviceManager) FindDeviceByEid(eid string) *dev.Device {
	device, err := db.SelectEntity[*dev.Device](dm.A.DA(), "eid=?", eid)
	if err != nil {
		panic(err)
	}
	return device
}

func (dm *DeviceManager) FindBlockByEidOrTac(operatorId int64, eid, tac string) *dev.Block {
	block, err := db.SelectEntity[*dev.Block](dm.A.DA(), "operator_id=? and block_model=1 and ((eid=? and block_category=0) or (tac=? and block_category=1) )", operatorId, eid, tac)
	if err != nil {
		panic(err)
	}
	return block
}
