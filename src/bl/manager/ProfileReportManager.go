package manager

import (
	"bytes"
	"esim.common/src/entity/dp/sum"
	"fmt"
	"k/app"
	"k/db"
	"k/logger"
	"sync/atomic"
	"time"
)

type ProfileReportManager struct {
	A          *app.App
	run        atomic.Bool
	da         *db.DA
	currentDay string
}

type ProfileReport struct {
	TenantId   string `json:"tenantId"`
	LocalState int    `json:"localState"`
	State      int    `json:"state"`
	Mode       int    `json:"mode"`
	TypeId     int64  `json:"typeId"`
	Num        int64  `json:"num"`
}

func (nm *ProfileReportManager) Init() {
	nm.run.Store(false)
	nm.da = nm.A.DA()
	nm.A.RegisterTimeCycleCallback("ProfileReport", func() {
		nm.doProfileReport()
	})
}

func (nm *ProfileReportManager) doProfileReport() {
	if !nm.run.CompareAndSwap(false, true) {
		return
	}
	defer func() {
		if err := recover(); err != nil {
			logger.E("ProfileReport", err)
		}
		nm.run.Store(false)
	}()
	if nm.currentDay == "" {
		nm.currentDay = time.Now().Format("2006-01-02") //time.Now().Format("2006-01-02")
		return
	}
	today := time.Now().Format("2006-01-02")
	if today == nm.currentDay {
		return
	}
	fmt.Println("ProfileReport start")
	//业务
	sql := new(bytes.Buffer)
	sql.WriteString(" SELECT tenant_id ,local_state ,state,type_id,COUNT(1) as num FROM mno_profile_t where")
	sql.WriteString(" create_time <")
	sql.WriteString(fmt.Sprintf("%v", nm.getYesterdayMaxTime()))
	sql.WriteString(" GROUP BY tenant_id,state,local_state,type_id")
	orderReport, err := nm.da.Select(sql.String())
	if err != nil {
		panic(err)
	}
	sumList := db.ParseList[*ProfileReport](orderReport)
	for _, item := range sumList {
		downloadSum := &sum.ProfileReport{
			TenantId:   item.TenantId,
			TypeId:     item.TypeId,
			LocalState: item.LocalState,
			State:      item.State,
			Num:        item.Num,
			DateYmd:    nm.getYesterdayTime(),
			CreateTime: time.Now().UnixMilli(),
			CreateBy:   nm.A.Instance.Id,
		}
		nm.da.InsertEntity(downloadSum, nm.A.Instance.Id)
	}
	nm.currentDay = today
}
func (nm *ProfileReportManager) getYesterdayMaxTime() int64 {
	// 获取当前时间
	now := time.Now()
	// 设置为昨天的最大时间（23:59:59）
	yesterdayMax := time.Date(
		now.Year(),
		now.Month(),
		now.Day(),
		0, 0, 0, 0,
		now.Location(),
	)
	return yesterdayMax.UnixMilli()
}

func (nm *ProfileReportManager) getYesterdayTime() string {
	// 获取当前时间
	now := time.Now()
	// 计算昨天的起始时间（当前时间减去24小时）
	yesterdayStart := now.Add(-24 * time.Hour)
	// 计算昨天的最大时间（昨天起始时间加上23小时59分59秒）
	return yesterdayStart.Format("2006-01-02")
}
