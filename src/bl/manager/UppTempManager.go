package manager

import (
	"esim.common/src/entity/dp/sys"
	"k/app"
	"k/db"
)

type UppTempManager struct {
	A *app.App
}

func (m *UppTempManager) SelectByPrimaryKey(uppId int64) *sys.UppTemplate {
	uppTemp, err := db.SelectEntity[*sys.UppTemplate](m.A.DA(), "upp_id=?", uppId)
	if err != nil {
		panic(err)
	}
	return uppTemp
}

func (m *UppTempManager) SelectByOperatorAndName(operatorId int64, name string) *sys.UppTemplate {
	uppTemp, err := db.SelectEntity[*sys.UppTemplate](m.A.DA(), "operator_id=? and descripton=?", operatorId, name)
	if err != nil {
		panic(err)
	}
	return uppTemp
}

func (m *UppTempManager) SelectByOperator(operatorId int64) *sys.UppTemplate {
	uppTemp, err := db.SelectEntity[*sys.UppTemplate](m.A.DA(), "operator_id=? or operator_id=0 order by operator_id desc,create_time desc", operatorId)
	if err != nil {
		panic(err)
	}
	return uppTemp
}

func (m *UppTempManager) InsertUppTemplate(da *db.DA, temp *sys.UppTemplate) {
	uppId, err := da.InsertEntity(temp, m.A.Instance.Id)
	if err != nil {
		da.Rollback()
		panic(err)
	}
	temp.UppId = int64(uppId)
}

func (m *UppTempManager) Update(da *db.DA, old *sys.UppTemplate) int {
	res, err := da.UpdateEntity(m.A.Instance.Id, old)
	if err != nil {
		da.Rollback()
		panic(err)
	}
	return res
}
