package manager

import (
	"esim.common/src/entity/dp/mno"
	"fmt"
	"k"
	"k/asn"
	"k/char"
	"k/logger"
	"math/big"
	"strconv"
	"strings"
)

func CreateProfileOwner(entity *mno.Profile, profileType *mno.ProfileType, bImsi []byte) {
	if len(strings.TrimSpace(entity.Imsi)) == 0 {
		return
	}
	imsi := string(bImsi)
	entity.Mcc, entity.Mnc = char.GetMccMnc(imsi)
	mncLength := len(entity.Mnc)
	if mncLength < 2 { //如果不存在，就取ProfileType配置的值
		mncLength = int(profileType.MncLength)
		logger.T("CreateProfileOwner", "NotExistMNC", imsi[0:6])
	}
	tlvOperatorId := asn.NewTlvByHexTag("b7", nil)

	mccMnc := imsi[0 : 3+mncLength]
	hexMccMnc := string(mccMnc[1]) + mccMnc[0:1]
	if mncLength == 2 {
		hexMccMnc += "F" + string(mccMnc[2]) + string(mccMnc[4]) + string(mccMnc[3])
	} else {
		hexMccMnc += string(mccMnc[3]) + string(mccMnc[2]) + string(mccMnc[5]) + string(mccMnc[4])
	}
	tlvOperatorId.Append(asn.NewTlvByHexTag("80", asn.DerHexValue(hexMccMnc)))
	if len(profileType.Gid1) > 0 {
		tlvOperatorId.Append(asn.NewTlvByHexTag("81", asn.DerHexValue(profileType.Gid1)))
	}
	if len(strings.TrimSpace(profileType.Gid2)) > 0 {
		tlvOperatorId.Append(asn.NewTlvByHexTag("82", asn.DerHexValue(profileType.Gid2)))
	}
	entity.ProfileOwner = tlvOperatorId.ToDerBase64()
}

func GetIccid(parser *asn.Tlv) string {
	return parser.FindChildByTagHex("a0").FindChildByTagHex("83").GetValueHex()
}

func GetMsisdn(parser *asn.Tlv) string {
	tlvB4 := parser.FindChildByTagHex("b4")
	if tlvB4 == nil {
		return ""
	}
	tlvA7 := tlvB4.FindChildByTagHex("a7")
	if tlvA7 == nil {
		return ""
	}
	tlv83 := tlvA7.FindChildByTagHex("83")
	if tlv83 == nil {
		return ""
	}
	hexMsisdn := tlv83.GetValueHex()
	if len(hexMsisdn) < 24 {
		return ""
	}
	swappedPart := hexMsisdn[24:]
	var originalBuilder strings.Builder
	for i := 0; i < len(swappedPart); i += 2 {
		if i+1 >= len(swappedPart) {
			break
		}
		originalBuilder.WriteByte(swappedPart[i+1])
		originalBuilder.WriteByte(swappedPart[i])
	}
	dNumberOrSsc := originalBuilder.String()[:20]
	msisdn := strings.TrimRight(strings.ToUpper(dNumberOrSsc), "F")
	return msisdn
}

func GetImsi(tlvUpp *asn.Tlv) []byte {
	tlv := tlvUpp.FindChildByTagHex("b3")
	if tlv == nil {
		return nil
	}
	tlv = tlv.FindChildByTagHex("a3")
	if tlv == nil {
		return nil

	}
	tlv = tlv.FindChildByTagHex("83")
	if tlv == nil {
		return nil
	}
	bImsi := tlv.GetValue()
	if len(bImsi) < 1 {
		return nil
	}
	bImsi = asn.BytesSwap(bImsi[1:])
	sImsi := asn.BytesToHex(bImsi)[1:]
	count := len(sImsi)
	if sImsi[count-1] == 'f' {
		sImsi = sImsi[0 : count-1]
	}
	return []byte(sImsi)
}

func GetShortName(parser *asn.Tlv) string {
	b3 := parser.FindChildByTagHex("b3")
	if b3 == nil {
		return ""
	}
	b3Ad := b3.FindChildByTagHex("ad")
	if b3Ad == nil {
		return ""
	}
	b3Ad83 := b3Ad.FindChildByTagHex("83")
	if b3Ad83 == nil {
		return ""
	}
	hexStr := asn.BytesToHex(b3Ad83.GetValue())
	if strings.HasPrefix(hexStr, "00") {
		hexStr = hexStr[2:]
	}
	return string(asn.DerHexValue(removePadding(hexStr)))
}

func removePadding(input string) string {
	index := len(input)
	for index > 0 && strings.EqualFold(string(input[index-1]), "F") {
		index--
	}
	return input[:index]
}

func GetKi(parser *asn.Tlv) []byte {
	a4 := parser.FindChildByTagHex("a4")
	if a4 == nil {
		return nil
	}
	b3A1 := a4.FindChildByTagHex("a1")
	if b3A1 == nil {
		return nil
	}
	b3A1A1 := b3A1.FindChildByTagHex("a1")
	if b3A1A1 == nil {
		return nil
	}
	b3A1A182 := b3A1A1.FindChildByTagHex("82")
	if b3A1A182 == nil {
		return nil
	}
	return asn.DerHexValue(b3A1A182.GetValueHex())
}

func GetOpc(parser *asn.Tlv) []byte {
	a4 := parser.FindChildByTagHex("a4")
	if a4 == nil {
		return nil
	}
	b3A1 := a4.FindChildByTagHex("a1")
	if b3A1 == nil {
		return nil
	}
	b3A1A1 := b3A1.FindChildByTagHex("a1")
	if b3A1A1 == nil {
		return nil
	}
	b3A1A183 := b3A1A1.FindChildByTagHex("83")
	if b3A1A183 == nil {
		return nil
	}
	return asn.DerHexValue(b3A1A183.GetValueHex())
}

func FillProfileHeaders(parser *asn.Tlv, profileType, iccid string) {
	a0 := parser.FindChildByTagHex("a0")
	if len(strings.TrimSpace(profileType)) > 0 {
		a0.FindChildByTagHex("82").SetValue(asn.DerStringValue(profileType))
	}
	if len(strings.TrimSpace(iccid)) > 0 {
		a0.FindChildByTagHex("83").SetValue(asn.DerHexValue(iccid))
	}
}

func FillPEMF(parser *asn.Tlv, iccid, pin1, pin2, adm1 string) {
	b0 := parser.FindChildByTagHex("b0")
	// 根据PIN/PUK/ADM1 判断是否支持配置
	sw := []string{
		hasSupport(pin1, "01"),
		hasSupport(pin2, "02"),
		hasSupport(adm1, "0a"),
	}
	switchHex := strings.Join(sw, "")
	if len(strings.TrimSpace(switchHex)) > 0 {
		//配置对应的keyReference，如果没值则不用替换（使用源模板的配置
		b0.FindChildByTagHex("a2").FindChildByTagHex("a1").FindChildByTagHex("c6").SetValue(asn.DerHexValue(switchHex))
	}
	if len(strings.TrimSpace(iccid)) > 0 {
		b0.FindChildByTagHex("a4").FindChildByTagHex("83").SetValue(asn.DerHexValue(swapIccid(iccid)))
	}
}

func hasSupport(condition string, trueValue string) string {
	if len(strings.TrimSpace(condition)) > 0 {
		return trueValue
	}
	return ""
}

func FillPuk(parser *asn.Tlv, puk1, puk2 string) {
	a3 := parser.FindChildByTagHex("a3")
	if a3 == nil {
		return
	}
	//Riken@20241205：如果模板没有配置PUK，那即算Profile传递过来了，也不会生效，结构以模板为准。
	tlvCodes := a3.FindChildByTagHex("a1")
	if tlvCodes == nil {
		return
	}
	puk := puk1
	var tlvTemp *asn.Tlv
	if len(strings.TrimSpace(puk)) > 0 {
		tlvTemp = tlvCodes.FindFirstTagByHexValue("80", asn.BytesToHex(asn.DerIntegerValue(1)))
		if tlvTemp == nil {
			return
		}
		tlvTemp.Parent().FindChildByTagHex("81").SetValue(checkPukOrPin(puk, "12312300"))
	}
	puk = puk2
	if len(strings.TrimSpace(puk)) > 0 {
		tlvTemp = tlvCodes.FindFirstTagByHexValue("80", asn.BytesToHex(asn.DerIntegerValue(129)))
		if tlvTemp == nil {
			return
		}
		tlvTemp.Parent().FindChildByTagHex("81").SetValue(checkPukOrPin(puk, "12312300"))
	}
}

func createPukCode(puk string, keyReference, maxAttemptCount, leftAttemptCount int, defaultPuk string) *asn.Tlv {
	tlv := asn.NewTlvByHexTag("30", nil)
	tlv.Append(asn.NewTlvByHexTag("80", asn.DerIntegerValue(keyReference)))
	tlv.Append(asn.NewTlvByHexTag("81", checkPukOrPin(puk, defaultPuk)))
	tlv.Append(asn.NewTlvByHexTag("82", asn.DerHexValue(hexPukOrPinAttemptCount(maxAttemptCount, leftAttemptCount, 10))))
	return tlv
}

func checkPukOrPin(code, defaultCode string) []byte {
	if len(strings.TrimSpace(code)) == 0 {
		code = defaultCode
	}
	if len(strings.TrimSpace(code)) == 0 {
		code = "12345678"
	}
	length := len(code)
	if length == 8 {
		return []byte(code)
	}
	if length > 8 {
		return []byte(code[:8])
	}
	codes := asn.ConcatBytes([]byte(code), make([]byte, 8-length))
	for i := length; i < 8; i++ {
		codes[i] = 0xFF
	}
	return codes
}

func hexPukOrPinAttemptCount(maxAttemptCount, leftAttemptCount, defaultCount int) string {
	if defaultCount > 15 {
		defaultCount = 15
	} else if defaultCount < 0 {
		defaultCount = 10
	}
	if maxAttemptCount > 15 {
		maxAttemptCount = 15
	} else if maxAttemptCount < 0 {
		maxAttemptCount = defaultCount
	}
	if leftAttemptCount > 15 {
		leftAttemptCount = 15
	} else if leftAttemptCount < 0 {
		leftAttemptCount = defaultCount
	}
	result := maxAttemptCount*16 + leftAttemptCount
	return fmt.Sprintf("%X", result)
}

func FillPin(parser *asn.Tlv, pin1, pin2, adm1 string) {
	a2 := parser.FindChildByTagHex("a2")
	if a2 == nil {
		return
	}

	tlvPinConfig := a2.FindChildByTagHex("a1").FindChildByTagHex("a0")
	if tlvPinConfig == nil {
		return
	}
	var tlvTemp *asn.Tlv
	if len(strings.TrimSpace(pin1)) > 0 {
		tlvTemp = tlvPinConfig.FindFirstTagByHexValue("80", asn.BytesToHex(asn.DerIntegerValue(1)))
		if tlvTemp == nil {
			return
		}
		tlvTemp.Parent().FindChildByTagHex("81").SetValue(checkPukOrPin(pin1, "00123123"))
	}
	if len(strings.TrimSpace(pin2)) > 0 {
		tlvTemp = tlvPinConfig.FindFirstTagByHexValue("80", asn.BytesToHex(asn.DerIntegerValue(2)))
		if tlvTemp == nil {
			return
		}
		tlvTemp.Parent().FindChildByTagHex("81").SetValue(checkPukOrPin(pin2, "00123123"))
	}
	if len(strings.TrimSpace(adm1)) > 0 {
		tlvTemp = tlvPinConfig.FindFirstTagByHexValue("80", asn.BytesToHex(asn.DerIntegerValue(10)))
		if tlvTemp == nil {
			return
		}
		tlvTemp.Parent().FindChildByTagHex("81").SetValue(checkPukOrPin(adm1, "00123123"))
	}
}

func createPinConfiguration(pin string, pinKeyReference, unblockPukKeyReference int, defaultPin, hexPinAttribute string) *asn.Tlv {
	tlv := asn.NewTlvByHexTag("30", nil)
	tlv.Append(asn.NewTlvByHexTag("80", asn.DerIntegerValue(pinKeyReference)))
	tlv.Append(asn.NewTlvByHexTag("81", checkPukOrPin(pin, defaultPin))) //OCTET STRING (Ascii String)
	if unblockPukKeyReference > 0 {
		tlv.Append(asn.NewTlvByHexTag("82", asn.DerIntegerValue(unblockPukKeyReference)))
	}
	if len(strings.TrimSpace(hexPinAttribute)) > 0 {
		tlv.Append(asn.NewTlvByHexTag("83", asn.DerHexValue(hexPinAttribute)))
	}
	tlv.Append(asn.NewTlvByHexTag("84", asn.DerHexValue(hexPukOrPinAttemptCount(3, 3, 3))))
	return tlv
}

func FillEfPnn(parser *asn.Tlv, spn, serviceProviderName string) {
	b4 := parser.FindChildByTagHex("b4")
	if b4 == nil || len(strings.TrimSpace(spn)) == 0 {
		return
	}
	bf26 := b4.FindChildByTagHex("bf26")
	if bf26 == nil {
		return
	}
	list83 := bf26.FindAllChildByTagHex("83")
	if len(list83) > 0 {
		for _, obj := range list83 {
			bf26.RemoveChild(obj)
		}
	}
	tlvObj := buildNetworkNameTLV("43", spn)
	if len(strings.TrimSpace(serviceProviderName)) > 0 {
		shortTlv := buildNetworkNameTLV("45", serviceProviderName)
		tlvObj = asn.ConcatBytes(tlvObj, shortTlv)
	} else {
		tlvObj = asn.ConcatBytes(tlvObj, []byte{0xff, 0xff})
	}
	tlvObj = asn.ConcatBytes(tlvObj, []byte{0xff, 0xff})
	hexElementSize := 16 * 2
	elementSize := len(tlvObj) / 16
	if len(tlvObj)%16 != 0 {
		elementSize += 1
	}
	total := elementSize * hexElementSize
	allContext := asn.RightPadding(asn.BytesToHex(tlvObj), total, "F")
	for i := 0; i < elementSize; i++ {
		start := i * hexElementSize
		context := allContext[start : start+hexElementSize-1]
		child := asn.NewTlvByHexTag("83", asn.DerHexValue(context))
		bf26.Append(child)
	}
	FillEfOPL(parser)
	EnableOrDisabledUstService(parser, 45, true)
}

func FillEfOPL(parser *asn.Tlv) {
	b4 := parser.FindChildByTagHex("b4")
	if b4 == nil {
		return
	}
	b1 := b4.FindChildByTagHex("b1")
	if b1 == nil {
		return
	}
	b183 := b1.FindChildByTagHex("83")
	if b183 == nil {
		return
	}
	bf27 := b4.FindChildByTagHex("bf27")
	if bf27 == nil {
		//不用自动添加bf27
		return
	}
	list83 := bf27.FindAllChildByTagHex("83")
	if len(list83) > 0 {
		for _, obj := range list83 {
			bf27.RemoveChild(obj)
		}
	}
	plmns := b183.GetValueHex()
	allTac := "0000FFFE" //all tac
	seq := int64(0)
	for i := 0; i < len(plmns); i += 10 {
		seq += 1
		plmn := plmns[i:min(i+6, len(plmns)-1)]
		context := asn.DerHexValue(plmn[0:6] + allTac + asn.LeftPadding(strconv.FormatInt(seq, 16), 2, "0"))
		bf27.Append(asn.NewTlvByHexTag("83", context))
	}
	EnableOrDisabledUstService(parser, 46, true)
}

func buildNetworkNameTLV(hexTag, name string) []byte {
	data, spaceBit := char.CbsCode(name)
	octet2 := []byte{byte(0x80 | spaceBit)}
	return asn.NewTlvByHexTag(hexTag, asn.ConcatBytes(octet2, data)).ToDer()
}

func FillPeUsimImsi(parser *asn.Tlv, pin1, pin2 string, bImsi []byte, serviceProviderName string) {
	tlvUsim := parser.FindChildByTagHex("b3")
	if bImsi != nil && len(bImsi) > 0 {
		imsi := string(bImsi)
		tlvUsim.FindChildByTagHex("a3").FindChildByTagHex("83").SetValue(createEfImsiValue(imsi))
	}
	if len(strings.TrimSpace(serviceProviderName)) > 0 {
		tlvSpn := tlvUsim.FindChildByHexTags("ad", "83")
		if tlvSpn != nil {
			tlvSpn.SetValue(createEfSpn(serviceProviderName))
		}
	}
	tlvUsimPinCodes := tlvUsim.Next()
	if strings.ToLower(tlvUsimPinCodes.TagHex) == "a2" {
		//确定是USIM设置的PIN
		tlvPinConfig := tlvUsimPinCodes.FindChildByTagHex("a1").FindChildByTagHex("a0")
		var tlvTemp *asn.Tlv
		//Riken@20250604: Pin的修改根据模板配置来，如果没有，不做新增
		if len(strings.TrimSpace(pin1)) > 0 {
			tlvTemp = tlvPinConfig.FindFirstTagByHexValue("80", asn.BytesToHex(asn.DerIntegerValue(129)))
			if tlvTemp != nil {
				tlvTemp.Parent().FindChildByTagHex("81").SetValue(checkPukOrPin(pin1, "00123123"))
				//没有对应Reference的PIN
				//tlvPinConfig.Append(createPinConfiguration(pin1, 129, 1, "00123123", ""))
			}
		}
		if len(strings.TrimSpace(pin2)) > 0 {
			tlvTemp = tlvPinConfig.FindFirstTagByHexValue("80", asn.BytesToHex(asn.DerIntegerValue(130)))
			if tlvTemp != nil {
				tlvTemp.Parent().FindChildByTagHex("81").SetValue(checkPukOrPin(pin2, "00123123"))
			}
		}
	}
}

func createEfImsiValue(imsi string) []byte {
	significantBytes := byte(0)
	//TS 24.008 Figure 10.5.4, page 497/499
	//0 even number of identity digits and also when the TMSI/P-TMSI or TMGI and optional MBMS Session Identity is used
	//1 odd number of identity digits
	//IMSI identity even/odd 001==1|9
	count := len(imsi)
	if count%2 == 1 {
		imsi = "9" + imsi
	} else {
		imsi = "1" + imsi + "F"
		//For the IMSI, IMEI and IMEISV this field is coded using BCD coding. If the number
		//of identity digits is even then bits 5 to 8 of the last octet shall be filled with an end
		//mark coded as "1111".
	}
	significantBytes = byte(count/2 + 1)
	return asn.ConcatBytes([]byte{significantBytes & 0xFF}, asn.BytesSwap(asn.DerHexValue(imsi)))
}

func createEfSpn(serviceProviderName string) []byte {
	spn := "00" + asn.BytesToHex([]byte(serviceProviderName))
	if len(spn) > 34 {
		spn = spn[:34]
	} else {
		spn = asn.RightPadding(spn, 34, "F")
	}
	return asn.DerHexValue(spn)
}

func FillKiAndOpc(parser *asn.Tlv, bKi, bOpc []byte) {
	tlvAkaParameter := parser.FindChildByTagHex("a4").FindChildByTagHex("a1").FindChildByTagHex("a1")
	tlvKi := tlvAkaParameter.FindChildByTagHex("82")
	tlvOpc := tlvAkaParameter.FindChildByTagHex("83")
	if len(bKi) < 16 {
		bKi = asn.ConcatBytes(bKi, make([]byte, 16-len(bKi)))
	}
	if len(bOpc) < 16 {
		bOpc = asn.ConcatBytes(bOpc, make([]byte, 16-len(bOpc)))
	}
	tlvKi.SetValue(bKi)
	tlvOpc.SetValue(bOpc)
}

func FillC9(parser *asn.Tlv, c9 string) {
	if len(strings.TrimSpace(c9)) == 0 {
		return
	}
	tlv := parser.FindChildByHexTags("a8", "a2", "30", "c9")
	if tlv == nil {
		return
	}
	tlv.SetValue(asn.DerHexValue(c9))
}

func FillGID(parser *asn.Tlv, gid1, gid2 string) {
	if len(strings.TrimSpace(gid1)) == 0 && len(strings.TrimSpace(gid2)) == 0 {
		return
	}
	b7 := parser.FindChildByTagHex("b7")
	if b7 == nil {
		//用于ProfileMetadata
		b7 = asn.NewTlvByHexTag("b7", nil)
		AddPRBeforeEnd(parser, b7)
	}
	gid := gid1
	if len(strings.TrimSpace(gid)) > 0 {
		b781 := b7.FindChildByTagHex("81")
		if b781 == nil {
			b7.Append(asn.NewTlvByHexTag("81", asn.DerHexValue(gid)))
		} else {
			b781.SetValue(asn.DerHexValue(gid))
		}
	}
	gid = gid2
	if len(strings.TrimSpace(gid)) > 0 {
		b782 := b7.FindChildByTagHex("82")
		if b782 == nil {
			b7.Append(asn.NewTlvByHexTag("82", asn.DerHexValue(gid)))
		} else {
			b782.SetValue(asn.DerHexValue(gid))
		}
	}
}

func FillLoadBlock(parser *asn.Tlv, loadPackageAID, loadPackageObject string) {
	if len(strings.TrimSpace(loadPackageAID)) == 0 && len(strings.TrimSpace(loadPackageObject)) == 0 {
		return
	}
	a8 := parser.FindChildByTagHex("a8")
	//get or build node
	if a8 == nil {
		a8 = asn.NewTlvByHexTag("a8", nil)
		AddPRBeforeEnd(parser, a8)
	}
	a8A1 := a8.FindChildByTagHex("a1")
	if a8A1 == nil {
		a8A1 = asn.NewTlvByHexTag("a1", nil)
		InsertBeforeNextPE(a8, a8A1)
	}
	//loadPackageAID
	a8A14f := a8A1.FindChildByTagHex("4f")
	if a8A14f == nil {
		a8A14f = asn.NewTlvByHexTag("4f", nil)
		a8.Append(a8A14f)
	}
	a8A14f.SetValue(asn.DerHexValue(loadPackageAID))
	//	loadPackageObject
	a8A1C4 := a8A1.FindChildByTagHex("c4")
	if a8A1C4 == nil {
		a8A14f = asn.NewTlvByHexTag("c4", nil)
		a8A1.Append(a8A1C4)
	}
	a8A1C4.SetValue(asn.DerHexValue(loadPackageObject))
}

func AddPRBeforeEnd(parser, pe *asn.Tlv) {
	parser.FindChildByTagHex("aa").InsertBefore(pe)
}

func FillMSISDN(parser *asn.Tlv, msisdn string) {
	tlvB4 := parser.FindChildByTagHex("b4")
	if tlvB4 == nil {
		return
	}
	if len(strings.TrimSpace(msisdn)) == 0 {
		//            不存在msisdn,移除EFMsisdn
		if tlvB4 != nil {
			EnableOrDisabledUstService(parser, 21, false)
			tlvA7 := tlvB4.FindChildByTagHex("a7")
			if tlvA7 != nil {
				tlvB4.RemoveChild(tlvA7)
			}
		}
		return
	}
	tlvA7 := tlvB4.FindChildByTagHex("a7")
	if tlvA7 == nil {
		return
	}
	tlv83 := tlvA7.FindChildByTagHex("83")
	if tlv83 == nil {
		tlv83 = asn.NewTlvByHexTag("83", nil)
		tlvA7.Append(tlv83)
	}
	tlv83.SetValue(buildMsisdnContent(msisdn))
	EnableOrDisabledUstService(parser, 21, true)
}

func buildMsisdnContent(msisdn string) []byte {
	//finalAlp := ""
	//if len(strings.TrimSpace(alpha)) > 0 {
	//	alp := asn.BytesToHex([]byte(alpha))
	//	curLen := len(alp)
	//	maxLength := 0
	//	if curLen >= 482 {
	//		maxLength = 482
	//	} else {
	//		if curLen%2 == 0 {
	//			maxLength = curLen
	//		} else {
	//			maxLength = curLen + 1
	//		}
	//	}
	//	finalAlp = asn.RightPadding(alp[0:maxLength], maxLength, "F")
	//}
	//        X+1 byte
	//bcdLen := fmt.Sprintf("%02X", byte(min(22, dnLen)&0xFF))
	//        X+2 byte TON and NPI ,不包含拨号设置"FF"
	//ponAndNpi := "FF"
	//        X+3-X+12 byte
	dNumberOrSsc := asn.RightPadding(msisdn[0:min(20, len(msisdn))], 20, "F")
	var sb strings.Builder
	for i := 0; i < 20; i += 2 {
		sb.WriteByte(dNumberOrSsc[i+1])
		sb.WriteByte(dNumberOrSsc[i])
	}
	//        X+13 capability/configuration 标识字节。此字节标识 EF CCP1 中包含调用所需的相关功能/配置参数的记录编号。此字节的使用是可选的。如果未使用，则应将其设置为 'FF'
	//cap := "FF"
	////        X+14
	//extDn := ""
	//if dnLen <= 20 {
	//	extDn = "FF"
	//} else {
	//	extDn = asn.RightPadding(msisdn[20:min(22, dnLen)], 2, "F")
	//}
	pre24F := asn.RightPadding("", 24, "F")
	return asn.DerHexValue(pre24F + sb.String())
}

//优选

func FillHPlMNRatList(parser *asn.Tlv, plmnRatList []*PlmnRat) {
	b4 := parser.FindChildByTagHex("b4")
	if plmnRatList == nil || len(plmnRatList) == 0 {
		//            不存在plmnRatList,移除EFHplmn
		EnableOrDisabledUstService(parser, 43, false)
		if b4 == nil {
			return
		}
		b4_b1 := b4.FindChildByTagHex("b1")
		if b4_b1 != nil {
			b4.RemoveChild(b4_b1)
		}
		return
	}
	//	长度最长为100个
	maxLen := 100
	if len(plmnRatList) < maxLen {
		maxLen = len(plmnRatList)
	}
	plmnRatList = plmnRatList[:maxLen]
	if b4 == nil {
		b4 = asn.NewTlvByHexTag("b4", nil)
		AddPRBeforeEnd(parser, b4)
	}
	b4B1 := b4.FindChildByTagHex("b1")
	if b4B1 == nil {
		b4B1 = asn.NewTlvByHexTag("b1", nil)
		InsertBeforeNextPE(b4, b4B1)
	}
	b4B183 := b4B1.FindChildByTagHex("83")
	if b4B183 == nil {
		b4B183 = asn.NewTlvByHexTag("83", nil)
		b4B1.Append(b4B183)
	}
	b4B183.SetValue(asn.DerHexValue(covertPLMNRatToBytes(plmnRatList)))
	//	开启ust服务开关n°43
	EnableOrDisabledUstService(parser, 43, true)
}

// 禁选

func FillFPlMNList(parser *asn.Tlv, plmnList []*PlmnParam) {
	b3 := parser.FindChildByTagHex("b3")
	if plmnList == nil || len(plmnList) == 0 || b3 == nil {
		if b3 == nil {
			return
		}
		b3B3 := b3.FindChildByTagHex("b3")
		if b3B3 != nil {
			b3.RemoveChild(b3B3)
		}
		return
	}
	//	长度最长为100个
	maxLen := 100
	if len(plmnList) < maxLen {
		maxLen = len(plmnList)
	}
	plmnList = plmnList[:maxLen]
	var hexRes strings.Builder
	for _, plmnItem := range plmnList {
		plmnCodes := buildPLMNCodes(plmnItem.Plmn)
		hexRes.WriteString(plmnCodes)
	}

	b3B3 := b3.FindChildByTagHex("b3")
	if b3B3 == nil {
		b3B3 = asn.NewTlvByHexTag("b3", nil)
		InsertBeforeNextPE(b3, b3B3)
	}
	b3B383 := b3B3.FindChildByTagHex("83")
	if b3B383 == nil {
		b3B383 = asn.NewTlvByHexTag("83", nil)
		b3B3.Append(b3B383)
	}
	fPlmns := asn.DerHexValue(asn.RightPadding(hexRes.String(), 24, "F"))
	b3B383.SetValue(fPlmns)
}

func InsertBeforeNextPE(parent *asn.Tlv, current *asn.Tlv) {
	cur := new(big.Int)
	ci := new(big.Int)
	cur.SetString(current.TagHex, 16)
	childCount := parent.GetChildLength()
	for i := 0; i < childCount; i++ {
		child := parent.Child(i)
		ci.SetString(child.TagHex, 16)
		if ci.Cmp(cur) <= 0 {
			continue
		}
		child.InsertBefore(current)
		return
	}
	parent.Append(current)
}

func adjustAkaRotation(tlvUpp *asn.Tlv, imsi string) { //a4->a1->a1->84
	tlv := tlvUpp.FindChildByTagHex("a4")
	if tlv == nil {
		return
	}
	tlv = tlv.FindChildByTagHex("a1")
	if tlv == nil {
		return
	}
	tlv = tlv.FindChildByTagHex("a1")
	if tlv == nil {
		return
	}
	tlvRotation := tlv.FindChildByTagHex("84")
	if tlvRotation != nil { //如果模板有配置，则用模板的，此处不做处理
		return
	}

	//获取IMSI
	if k.CheckString(imsi[0:5], "46000", "46002", "46004", "46007", "46008", "46013") { //中国移动的Rotation非默认值
		tlvRotation = asn.NewTlvByHexTag("84", []byte{0x20, 0x13, 0x2f, 0x49, 0x5b})
		tlv.Append(tlvRotation)
		logger.T("adjustAkaRotation.ChinaMobile", "MCC-MNC", imsi[0:5])
	}
}

func adjustPlmnA1(parser *asn.Tlv, tag string, fileDescriptor, fileId []byte) {
	tlvB4 := parser.FindChildByTagHex("b4")
	if tlvB4 == nil {
		return
	}
	targetTlv := tlvB4.FindChildByTagHex(tag)
	if targetTlv == nil {
		return
	}
	tlvA1 := targetTlv.FindChildByTagHex("a1")
	if tlvA1 != nil {
		targetTlv.RemoveChild(tlvA1)
	}
	tlv83 := targetTlv.FindFirstByTagHex("83")
	if tlv83 == nil {
		return
	}
	tlvA1 = asn.NewTlvByHexTag("a1", nil)
	tlv83.InsertBefore(tlvA1)
	if fileDescriptor != nil {
		tlvA1.Append(asn.NewTlvByHexTag("82", fileDescriptor))
	}
	if fileId != nil {
		tlvA1.Append(asn.NewTlvByHexTag("83", fileId))
	}
	//Linear类型需汇总所有83
	list83 := targetTlv.FindAllChildByTagHex("83")
	targetSize := int64(0)
	if list83 != nil && len(list83) > 0 {
		for _, obj := range list83 {
			targetSize += obj.Length
		}
	}
	tlvA1.Append(asn.NewTlvByHexTag("80", asn.DerInt64Value(targetSize)))
}

func FillOPlMNwACT(parser *asn.Tlv, plmnRatList []*PlmnRat) {
	b4 := parser.FindChildByTagHex("b4")
	if plmnRatList == nil || len(plmnRatList) == 0 || b4 == nil {
		EnableOrDisabledUstService(parser, 42, false)
		if b4 == nil {
			return
		}
		b4B0 := b4.FindChildByTagHex("b0")
		if b4B0 != nil {
			b4.RemoveChild(b4B0)
		}
		return
	}
	//	长度最长为100个
	maxLen := 100
	if len(plmnRatList) < maxLen {
		maxLen = len(plmnRatList)
	}
	plmnRatList = plmnRatList[:maxLen]
	b4B0 := b4.FindChildByTagHex("b0")
	if b4B0 == nil {
		b4B0 = asn.NewTlvByHexTag("b0", nil)
		InsertBeforeNextPE(b4, b4B0)
	}
	b4B083 := b4B0.FindChildByTagHex("83")
	if b4B083 == nil {
		b4B083 = asn.NewTlvByHexTag("83", nil)
		b4B0.Append(b4B083)
	}
	//至少8个plmn-wact
	b4B083.SetValue(asn.DerHexValue(asn.RightPadding(covertPLMNRatToBytes(plmnRatList), 80, "F")))
	//开启ust服务开关n°42
	EnableOrDisabledUstService(parser, 42, true)
}

func FillPlMNRatList(parser *asn.Tlv, plmnRatList []*PlmnRat) {
	b4 := parser.FindChildByTagHex("b4")
	if plmnRatList == nil || len(plmnRatList) == 0 || b4 == nil {
		EnableOrDisabledUstService(parser, 20, false)
		if b4 == nil {
			return
		}
		b4Af := b4.FindChildByTagHex("af")
		if b4Af != nil {
			b4.RemoveChild(b4Af)
		}
		return
	}
	//	长度最长为100个
	maxLen := 100
	if len(plmnRatList) < maxLen {
		maxLen = len(plmnRatList)
	}
	plmnRatList = plmnRatList[:maxLen]
	b4Af := b4.FindChildByTagHex("af")
	if b4Af == nil {
		b4Af = asn.NewTlvByHexTag("af", nil)
		InsertBeforeNextPE(b4, b4Af)
	}
	b4Af83 := b4Af.FindChildByTagHex("83")
	if b4Af83 == nil {
		b4Af83 = asn.NewTlvByHexTag("83", nil)
		b4Af.Append(b4Af83)
	}
	//至少8个plmn-wact
	b4Af83.SetValue(asn.DerHexValue(asn.RightPadding(covertPLMNRatToBytes(plmnRatList), 80, "F")))
	//if b4AfA1 != nil {
	//	b4Af.RemoveChild(b4AfA1)
	//}
	//b4AfA1 = asn.NewTlvByHexTag("a1", nil)
	//b4AfA1.Append(asn.NewTlvByHexTag("83", asn.DerHexValue("6f61")))
	//b4AfA1.Append(asn.NewTlvByHexTag("80", b4Af83.GetLengthBytes()))
	//b4Af83.InsertBefore(b4AfA1)
	//开启ust服务开关n°20
	EnableOrDisabledUstService(parser, 20, true)
}

func FillInstanceList(parser *asn.Tlv, instanceParam []*ApplicationInstanceParam, c9 string) {
	if len(instanceParam) == 0 && len(strings.TrimSpace(c9)) > 0 {
		FillC9(parser, c9)
		return
	}
	if len(instanceParam) > 0 {
		//replace application param
		a8 := parser.FindChildByTagHex("a8")
		a8A2 := asn.NewTlvByHexTag("a2", nil)
		for _, instance := range instanceParam {
			//	build 30 node
			applicationLoadPackageAID := asn.NewTlvByHexTag("4f", asn.DerHexValue(instance.ApplicationLoadPackageAID))
			classAID := asn.NewTlvByHexTag("4f", asn.DerHexValue(instance.ClassAID))
			instanceAID := asn.NewTlvByHexTag("4f", asn.DerHexValue(instance.InstanceAID))
			instanceC9 := asn.NewTlvByHexTag("c9", asn.DerHexValue(c9))
			tlv30 := asn.NewTlvByHexTag("30", nil)
			tlv30.Append(applicationLoadPackageAID)
			tlv30.Append(classAID)
			tlv30.Append(instanceAID)
			tlv30.Append(instanceC9)
			a8A2.Append(tlv30)
		}
		a8.Append(a8A2)
	}
}

func EnableOrDisabledUstService(parser *asn.Tlv, service int, enable bool) {
	if service <= 0 || service > 97 {
		return
	}
	bitLen := 8
	tlvB3 := parser.FindChildByTagHex("b3")
	if tlvB3 == nil {
		tlvB3 = asn.NewTlvByHexTag("b3", nil)
		AddPRBeforeEnd(parser, tlvB3)
	}
	tlvA8 := tlvB3.FindChildByTagHex("a8")
	if tlvA8 == nil {
		tlvA8 = asn.NewTlvByHexTag("a8", nil)
		InsertBeforeNextPE(tlvB3, tlvA8)
	}
	tlvUst := tlvA8.FindChildByTagHex("83")
	if tlvUst == nil {
		tlvUst = asn.NewTlvByHexTag("83", nil)
		tlvA8.Append(tlvUst)
	}
	bUst := tlvUst.GetValue()
	serviceMod := service % bitLen
	serviceGroup := service / bitLen
	if serviceGroup*bitLen < service {
		serviceGroup++
		serviceMod--
	} else { //相等, 0=7
		serviceMod = 7
	}
	if len(bUst) < serviceGroup { //
		bUst = asn.ConcatBytes(bUst, make([]byte, serviceGroup-len(bUst)))
	}
	if enable {
		bService := byte(1 << serviceMod)
		bUst[serviceGroup-1] = bUst[serviceGroup-1] | bService
	} else { //设置为0
		bService := ^(byte(1 << serviceMod))
		bUst[serviceGroup-1] = bUst[serviceGroup-1] & bService
	}
	tlvUst.SetValue(bUst)
}

func covertPLMNRatToBytes(plmnRatList []*PlmnRat) string {
	var hexRes strings.Builder
	for _, plmnRat := range plmnRatList {
		//处理MCC+MNC编码转Upp编码
		plmnCodes := buildPLMNCodes(plmnRat.Plmn)
		hexRes.WriteString(plmnCodes + plmnRat.Rat)
	}
	return hexRes.String()
}

func buildPLMNCodes(plmnStr string) string {
	plmnStr = asn.RightPadding(plmnStr[:min(len(plmnStr), 6)], 6, "F")
	plmn := []byte(plmnStr)
	swap(plmn, 0, 1)
	swap(plmn, 2, 5)
	swap(plmn, 3, 5)
	return string(plmn)
}

func swap(arr []byte, i, j int) {
	arr[i], arr[j] = arr[j], arr[i]
}

func swapIccid(iccid string) string {
	iccid = strings.TrimSpace(iccid)
	if len(iccid) == 0 || len(iccid) < 19 {
		return ""
	}
	if len(iccid) == 19 {
		return iccid + "f"
	}
	var sb strings.Builder
	for i := 0; i < 20; i += 2 {
		sb.WriteByte(iccid[i+1])
		sb.WriteByte(iccid[i])
	}
	return sb.String()
}

type PlmnRat struct {
	Plmn string `json:"plmn,omitempty"`
	Rat  string `json:"rat,omitempty"`
}

type PlmnParam struct {
	Plmn string `json:"plmn,omitempty"`
}

type SmspParam struct {
	AlphaIdentifier         string `json:"alphaIdentifier,omitempty"`
	ServiceCenterAddressTon string `json:"serviceCenterAddressTon,omitempty"`
	Number                  string `json:"number,omitempty"`
	Time                    int32  `json:"time,omitempty"`
	TimeUnit                string `json:"timeUnit,omitempty"`
}

type EccParam struct {
	EmergencyCode string `json:"emergencyCode,omitempty"`
	EmergencyType string `json:"emergencyType,omitempty"`
}

type AdnParam struct {
	DisplayName string `json:"displayName,omitempty"`
	Number      string `json:"number,omitempty"`
	Ton         string `json:"ton,omitempty"`
}

type MnoParam struct {
	Enc        string `json:"enc,omitempty"`
	Mac        string `json:"mac,omitempty"`
	Dek        string `json:"dek,omitempty"`
	Type       string `json:"type,omitempty"`
	KeyVersion int32  `json:"keyVersion,omitempty"`
}

type RfmParam struct {
	RfmTarList           []string `json:"rfmTarList,omitempty"`
	MinimumSecurityLevel string   `json:"minimumSecurityLevel,omitempty"`
}

type PcscfParam struct {
	Type  string `json:"type,omitempty"`
	Value string `json:"value,omitempty"`
}

type IsimParam struct {
	Domain    string        `json:"domain,omitempty"`
	Ad        string        `json:"ad,omitempty"`
	ParamList []*PcscfParam `json:"paramList,omitempty"`
	RfmParam  *RfmParam     `json:"rfmParam,omitempty"`
}

type ApplicationInstanceParam struct {
	ApplicationLoadPackageAID                                  string `json:"applicationLoadPackageAID,omitempty"`
	ClassAID                                                   string `json:"classAID,omitempty"`
	InstanceAID                                                string `json:"instanceAID,omitempty"`
	UiccToolkitApplicationSpecificParametersField              string `json:"uiccToolkitApplicationSpecificParametersField,omitempty"`
	UiccAccessApplicationSpecificParametersField               string `json:"uiccAccessApplicationSpecificParametersField,omitempty"`
	UiccAdministrativeAccessApplicationSpecificParametersField string `json:"uiccAdministrativeAccessApplicationSpecificParametersField,omitempty"`
	Ts102226SIMFileAccessToolkitParameter                      string `json:"ts102226SIMFileAccessToolkitParameter,omitempty"`
}

type AppletTemplate struct {
	LoadPackageAID    string                      `json:"loadPackageAID,omitempty"`
	LoadBlockObject   string                      `json:"loadBlockObject,omitempty"`
	FileManagementCMD string                      `json:"fileManagementCMD,omitempty"`
	InstanceParam     []*ApplicationInstanceParam `json:"instanceParam,omitempty"`
	C9                string                      `json:"c9,omitempty"`
}

type PublicKeyParam struct {
	PublicKey string `json:"publicKey,omitempty"`
	Pki       string `json:"pki,omitempty"`
}

type FifthGenerationParam struct {
	ProtectionScheme   int32             `json:"protectionScheme,omitempty"`
	PublicKeys         []*PublicKeyParam `json:"publicKeys,omitempty"`
	KeyIndex           int32             `json:"keyIndex,omitempty"`
	UpdateStatusAccess int32             `json:"updateStatusAccess,omitempty"`
	Supi               string            `json:"supi,omitempty"`
	RoutingIndicator   int32             `json:"routingIndicator,omitempty"`
	Ursp               string            `json:"ursp,omitempty"`
	Tn3gppsnn          string            `json:"tn3Gppsnn,omitempty"`
	Cag                string            `json:"cag,omitempty"`
	SorCmci            string            `json:"sorCmci,omitempty"`
	Dri                string            `json:"dri,omitempty"`
	Sedrx5g            string            `json:"sedrx5G,omitempty"`
	NswoConf5g         string            `json:"nswoConf5G,omitempty"`
	Mchpplmn           string            `json:"mchpplmn,omitempty"`
	KausfDerivation    string            `json:"kausfDerivation,omitempty"`
	UsimCalcSuci       bool              `json:"usimCalcSuci,omitempty"`
}

type EuiccMandatoryAIDParam struct {
	Aid     string `json:"aid,omitempty"`
	Version string `json:"version,omitempty"`
}

type ProfileTemplate struct {
	Name                 string                    `json:"name,omitempty"`
	HplmnRatList         []*PlmnRat                `json:"hplmnRatList,omitempty"`
	FplmnList            []*PlmnParam              `json:"fplmnList,omitempty"`
	OplmnRatList         []*PlmnRat                `json:"oplmnRatList,omitempty"`
	PlmnRatList          []*PlmnRat                `json:"plmnRatList,omitempty"`
	Rplmn                string                    `json:"rplmn,omitempty"`
	SmspParam            []*SmspParam              `json:"smspParam,omitempty"`
	Pnn                  string                    `json:"pnn,omitempty"`
	Sume                 string                    `json:"sume,omitempty"`
	EccParam             *EccParam                 `json:"eccParam,omitempty"`
	AdnParamList         []*AdnParam               `json:"adnParamList,omitempty"`
	SdnParamList         []*AdnParam               `json:"sdnParamList,omitempty"`
	MnoParam             *MnoParam                 `json:"mnoParam,omitempty"`
	UsimRFMParam         *RfmParam                 `json:"usimRFMParam,omitempty"`
	UiccRFMParam         *RfmParam                 `json:"uiccRFMParam,omitempty"`
	IsimParam            *IsimParam                `json:"isimParam,omitempty"`
	Rotation             string                    `json:"rotation,omitempty"`
	Pin1                 string                    `json:"pin1,omitempty"`
	Pin2                 string                    `json:"pin2,omitempty"`
	Adm1                 string                    `json:"adm1,omitempty"`
	Adm2                 string                    `json:"adm2,omitempty"`
	Puk1                 string                    `json:"puk1,omitempty"`
	Puk2                 string                    `json:"puk2,omitempty"`
	AppletTemplate1      *AppletTemplate           `json:"appletTemplate1,omitempty"`
	Gid1                 string                    `json:"gid1,omitempty"`
	Gid2                 string                    `json:"gid2,omitempty"`
	FifthGenerationParam *FifthGenerationParam     `json:"fifthGenerationParam,omitempty"`
	Eap                  string                    `json:"eap,omitempty"`
	EuiccMandatoryAIDs   []*EuiccMandatoryAIDParam `json:"euiccMandatoryAIDs,omitempty"`
	Snpn                 string                    `json:"snpn,omitempty"`
	Prose5g              string                    `json:"prose5G,omitempty"`
	Brand                string                    `json:"brand,omitempty"`
	Model                string                    `json:"model,omitempty"`
	Tac                  string                    `json:"tac,omitempty"`
}

type UppProfileTemplate struct {
	Name       string `json:"name,omitempty"`
	UppContent string `json:"uppContent,omitempty"`
	Gid1       string `json:"gid1,omitempty"`
	Gid2       string `json:"gid2,omitempty"`
	Brand      string `json:"brand,omitempty"`
	Model      string `json:"model,omitempty"`
	Tac        string `json:"tac,omitempty"`
}
