package manager

import (
	"esim.common/src/entity/dp/mno"
	"k/app"
	"k/db"
)

type ProfileTypeManager struct {
	A *app.App
}

func (ptm *ProfileTypeManager) SelectByType(operatorId int64, typeName string) *mno.ProfileType {
	profileType, err := db.SelectEntity[*mno.ProfileType](ptm.A.DA(), "(operator_id=? or operator_id=0) and name=?", operatorId, typeName)
	if err != nil {
		panic(err)
	}
	return profileType
}

func (ptm *ProfileTypeManager) SelectByTypeId(typeId int64) *mno.ProfileType {
	profileType, err := db.SelectEntity[*mno.ProfileType](ptm.A.DA(), "type_id=?", typeId)
	if err != nil {
		panic(err)
	}
	return profileType
}
func (ptm *ProfileTypeManager) SelectByProfileId(da *db.DA, profileId int64) *mno.ProfileType {
	profileType, err := da.Select("select * from mno_profile_t p,mno_profile_type_t pt where p.type_id=pt.type_id and p.profile_id=?", profileId)
	if err != nil {
		panic(err)
	}
	return db.ParseEntity[*mno.ProfileType](profileType)
}

func (ptm *ProfileTypeManager) SelectByState(operatorId int64, state int) []*mno.ProfileType {
	list, err := db.SelectList[*mno.ProfileType](ptm.A.DA(), "operator_id=? and state=?", operatorId, state)
	if err != nil {
		panic(err)
	}
	return list
}

func (ptm *ProfileTypeManager) InsertProfileType(da *db.DA, profileType *mno.ProfileType) {
	typeId, err := da.InsertEntity(profileType, ptm.A.Instance.Id)
	if err != nil {
		da.Rollback()
		panic(err)
	}
	profileType.TypeId = int64(typeId)
}

func (ptm *ProfileTypeManager) Update(da *db.DA, entity *mno.ProfileType) int {
	res, err := da.UpdateEntity(ptm.A.Instance.Id, entity)
	if err != nil {
		da.Rollback()
		panic(err)
	}
	return res
}
