package manager

import (
	"bytes"
	"esim.common/src/entity/dp/sum"
	"fmt"
	"k/app"
	"k/db"
	"k/logger"
	"sync/atomic"
	"time"
)

type DownloadOrderReportManager struct {
	A          *app.App
	run        atomic.Bool
	da         *db.DA
	currentDay string
}

type DownloadOrderReport struct {
	TenantId   string `json:"tenantId"`
	LocalState int    `json:"localState"`
	State      int    `json:"state"`
	Mode       int    `json:"mode"`
	TypeId     int64  `json:"typeId"`
	Num        int64  `json:"num"`
	Category   int    `json:"category"`
}

func (nm *DownloadOrderReportManager) Init() {
	nm.run.Store(false)
	nm.da = nm.A.DA()
	nm.A.RegisterTimeCycleCallback("DownloadOrder", func() {
		nm.doDownloadOrderReport()
	})
}

func (nm *DownloadOrderReportManager) doDownloadOrderReport() {
	if !nm.run.CompareAndSwap(false, true) {
		return
	}
	defer func() {
		if err := recover(); err != nil {
			logger.E("DownloadOrderReport", err)
		}
		nm.run.Store(false)
	}()
	if nm.currentDay == "" {
		nm.currentDay = time.Now().Format("2006-01-02")
		return
	}
	today := time.Now().Format("2006-01-02")
	if today == nm.currentDay {
		return
	}
	fmt.Println("DownloadOrderReport start")
	//业务
	sql := new(bytes.Buffer)
	sql.WriteString("SELECT tenant_id ,local_state ,state ,mode,type_id,COUNT(1) as num,0 as category FROM rsp_download_order_t where")
	sql.WriteString(" create_time <=")
	sql.WriteString(fmt.Sprintf("%v", nm.getYesterdayMaxTime()))
	sql.WriteString(" GROUP BY tenant_id,type_id,state,local_state,mode")
	orderReport, err := nm.da.Select(sql.String())
	if err != nil {
		panic(err)
	}
	sumList := db.ParseList[*DownloadOrderReport](orderReport)
	for _, item := range sumList {
		downloadSum := &sum.DownloadOrderReport{
			TenantId:   item.TenantId,
			TypeId:     item.TypeId,
			LocalState: item.LocalState,
			State:      item.State,
			Mode:       item.Mode,
			Num:        item.Num,
			Category:   item.Category,
			DateYmd:    nm.getYesterdayTime(),
			CreateTime: time.Now().UnixMilli(),
			CreateBy:   nm.A.Instance.Id,
		}
		nm.da.InsertEntity(downloadSum, nm.A.Instance.Id)
	}

	sqlHis := new(bytes.Buffer)
	sqlHis.WriteString("SELECT tenant_id ,local_state ,state ,mode,type_id,COUNT(1) as num,1 as category FROM rsp_download_order_history_t where")
	sqlHis.WriteString(" create_time <")
	sqlHis.WriteString(fmt.Sprintf("%v", nm.getYesterdayMaxTime()))
	sqlHis.WriteString(" GROUP BY tenant_id,type_id,state,local_state,mode")
	orderHisReport, err1 := nm.da.Select(sqlHis.String())
	if err1 != nil {
		panic(err1)
	}
	sumHisList := db.ParseList[*DownloadOrderReport](orderHisReport)
	for _, item := range sumHisList {
		downloadSum := &sum.DownloadOrderReport{
			TenantId:   item.TenantId,
			TypeId:     item.TypeId,
			LocalState: item.LocalState,
			State:      item.State,
			Mode:       item.Mode,
			Num:        item.Num,
			Category:   item.Category,
			DateYmd:    nm.getYesterdayTime(),
			CreateTime: time.Now().UnixMilli(),
			CreateBy:   nm.A.Instance.Id,
		}
		nm.da.InsertEntity(downloadSum, nm.A.Instance.Id)
	}
	nm.currentDay = today
}
func (nm *DownloadOrderReportManager) getYesterdayMaxTime() int64 {
	// 获取当前时间
	now := time.Now()
	// 设置为昨天的最大时间（23:59:59）
	yesterdayMax := time.Date(
		now.Year(),
		now.Month(),
		now.Day(),
		0, 0, 0, 0,
		now.Location(),
	)
	return yesterdayMax.UnixMilli()
}

func (nm *DownloadOrderReportManager) getYesterdayTime() string {
	// 获取当前时间
	now := time.Now()
	// 计算昨天的起始时间（当前时间减去24小时）
	yesterdayStart := now.Add(-24 * time.Hour)
	// 计算昨天的最大时间（昨天起始时间加上23小时59分59秒）
	return yesterdayStart.Format("2006-01-02")
}
