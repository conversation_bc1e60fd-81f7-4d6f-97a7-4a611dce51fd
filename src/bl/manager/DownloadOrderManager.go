package manager

import (
	"bytes"
	"esim.common/src/code"
	"esim.common/src/entity/dp/mno"
	"esim.common/src/entity/dp/rsp"
	"esim.common/src/entity/hub/sys"
	"esim.es2/src/bo/gsma"
	"k/app"
	"k/db"
	"time"
)

type DownloadOrderManager struct {
	A *app.App
}

func (dom *DownloadOrderManager) FindDownloadOrderByEidWithProfileId(operatorId, profileId int64, eid string) *rsp.DownloadOrder {
	order, err := db.SelectEntity[*rsp.DownloadOrder](dom.A.DA(), "operator_id=? and profile_id=? and eid=?", operatorId, profileId, eid)
	if err != nil {
		panic(err)
	}
	return order
}

func (dom *DownloadOrderManager) FindAllocatedDownloadOrder(operatorId int64, iccid string) *rsp.DownloadOrder {
	order, err := db.SelectEntity[*rsp.DownloadOrder](dom.A.DA(), "operator_id=? and iccid=? and (state=? or state=?)", operatorId, iccid, code.DownloadStateAllocated, code.DownloadStateLinked)
	if err != nil {
		panic(err)
	}
	return order
}

func (dom *DownloadOrderManager) FindDownloadOrderByMatchingId(operatorId int64, iccid, matchingId string) *rsp.DownloadOrder {
	order, err := db.SelectEntity[*rsp.DownloadOrder](dom.A.DA(), "operator_id=? and iccid=? and matching_id=?", operatorId, iccid, matchingId)
	if err != nil {
		panic(err)
	}
	return order
}

func (dom *DownloadOrderManager) FindDownloadOrderByIccid(operatorId int64, iccid string) *rsp.DownloadOrder {
	order, err := db.SelectEntity[*rsp.DownloadOrder](dom.A.DA(), "operator_id=? and iccid=? order by create_time desc", operatorId, iccid)
	if err != nil {
		panic(err)
	}
	return order
}

func (dom *DownloadOrderManager) FindConfirmDownloadOrder(operatorId int64, iccid string) *rsp.DownloadOrder {
	order, err := db.SelectEntity[*rsp.DownloadOrder](dom.A.DA(), "operator_id=? and iccid=? and state=?", operatorId, iccid, code.DownloadStateConfirmed)
	if err != nil {
		panic(err)
	}
	return order
}

func (dom *DownloadOrderManager) CheckMatchingIdExist(operatorId, downloadId int64, matchingId string) bool {
	count, err := dom.A.DA().DataCount("rsp_download_order_t", "operator_id=? and download_id!=? and matching_id=?", operatorId, downloadId, matchingId)
	if err != nil {
		panic(err)
	}
	return count > 0
}

func (dom *DownloadOrderManager) CancelOrderByMatchingId(operatorId int64, matchingId string, state, mode int) {
	entity := &rsp.DownloadOrder{}
	//Riken@20250605:状态为已下载或已安装的，不允许取消
	dom.A.DA().Update(dom.A.Instance.Id, entity.Table(), "state=?", "operator_id=? and matching_id=? and mode=? and state!=5 and state!=6", state, operatorId, matchingId, mode)
}
func (dom *DownloadOrderManager) CreateDownloadOrder(da *db.DA, profile *mno.Profile, state int, profileType *mno.ProfileType, operator *sys.Operator, downloadOrderRequest *gsma.DownloadOrderRequest) int64 {
	downloadOrder := &rsp.DownloadOrder{
		ProfileId:                      profile.ProfileId,
		OperatorId:                     operator.OperatorId,
		TypeId:                         profileType.TypeId,
		Eid:                            downloadOrderRequest.Eid,
		Iccid:                          profile.RequestIccid,
		HashConfirmationCode:           downloadOrderRequest.ConfirmationCode,
		FunctionRequesterIdentifier:    downloadOrderRequest.Header.FunctionRequesterIdentifier,
		FunctionCallIdentifier:         downloadOrderRequest.Header.FunctionCallIdentifier,
		ServiceProviderName:            profile.ServiceProviderName,
		ShortDescription:               profile.ShortDescription,
		SmdpAddress:                    operator.SmdpAddress,
		State:                          state,
		Icon:                           profileType.Icon,
		IconType:                       profileType.IconType,
		Ara:                            profileType.Ara,
		Mode:                           code.DownloadOrderModeAc,
		ProfileType:                    profileType.Name,
		ProfileClazz:                   profile.Clazz,
		PolicyRules:                    profileType.PolicyRules,
		DownloadOrderAttemptMaxCount:   profileType.DownloadOrderAttemptMaxCount,
		DownloadOrderCcAttemptMaxCount: profileType.DownloadOrderCcAttemptMaxCount,
		DownloadOrderExpiredMinute:     profileType.DownloadOrderExpiredMinute,
		DownloadOrderMaxSuccessCount:   profileType.DownloadOrderMaxSuccessCount,
		ReDownloadPolicy:               profileType.ReDownloadPolicy,
		SuccessDownloadCount:           0,
		NotificationConfiguration:      profileType.NotificationConfiguration,
		ServiceProviderMessage:         profileType.ServiceProviderMessage,
		PppModel:                       profile.PppModel,
		DownloadOrderAttemptCount:      profileType.DownloadOrderAttemptMaxCount,
		ConfirmationCodeAttemptCount:   profileType.DownloadOrderCcAttemptMaxCount,
		ExpiredTime:                    time.Now().UnixMilli() + int64(profileType.DownloadOrderExpiredMinute*60000),
		ProfileOwner:                   profile.ProfileOwner,
		TenantId:                       operator.TenantId,
	}
	id, err := da.InsertEntity(downloadOrder, dom.A.Instance.Id)
	if err != nil {
		panic(err)
	}
	return int64(id)
}

func (dom *DownloadOrderManager) SelectByCondition(iccid string, operatorId int64) []*rsp.DownloadOrder {
	orders, err := db.SelectList[*rsp.DownloadOrder](dom.A.DA(), `iccid=? and operator_id=? order by update_time desc`, iccid, operatorId)
	if err != nil {
		panic(err)
	}
	return orders
}

func (dom *DownloadOrderManager) SelectLastDownload(iccid string, profileId, operatorId int64) *rsp.DownloadOrder {
	orders, err := db.SelectEntity[*rsp.DownloadOrder](dom.A.DA(), `iccid=? and operator_id=? and profile_id=? order by create_time desc`, iccid, operatorId, profileId)
	if err != nil {
		panic(err)
	}
	return orders
}

func (dom *DownloadOrderManager) SelectDownloadsByEid(eid string, operatorId int64) []*rsp.DownloadOrder {
	orders, err := db.SelectList[*rsp.DownloadOrder](dom.A.DA(), `eid=? and operator_id=? order by update_time desc LIMIT 100`, eid, operatorId)
	if err != nil {
		panic(err)
	}
	return orders
}

func (dom *DownloadOrderManager) SelectByParams(iccid, eid, matchingId string, operatorId int64) *rsp.DownloadOrder {
	where := new(bytes.Buffer)
	where.WriteString(" iccid=? and operator_id=?")
	if eid != "" {
		where.WriteString(" and eid=  ")
		where.WriteString(db.BuildSqlStringParam(eid))
	}
	if matchingId != "" {
		where.WriteString(" and matching_id=  ")
		where.WriteString(db.BuildSqlStringParam(matchingId))
	}
	where.WriteString("  order by create_time desc")
	order, err := db.SelectEntity[*rsp.DownloadOrder](dom.A.DA(), where.String(), iccid, operatorId)
	if err != nil {
		panic(err)
	}
	return order
}

func (dom *DownloadOrderManager) GetDownloadSuccessCount(typeId int64, operatorId int64) int32 {
	e := &rsp.DownloadOrder{}
	// Downloaded，Installed，Deleted，Expired，Unavailable
	count, err := dom.A.DA().DataCount(e.Table(), "type_id=? and operator_id=? and state in (4,5,8,9,10)", typeId, operatorId)
	if err != nil {
		panic(err)
	}
	return int32(count)
}

func (dom *DownloadOrderManager) SelectByEidAndIccid(operatorId int64, eid string, iccid string) []*rsp.DownloadOrder {
	if iccid == "" {
		orders, err := db.SelectList[*rsp.DownloadOrder](dom.A.DA(), "operator_id=? and eid=?", operatorId, eid)
		if err != nil {
			panic(err)
		}
		return orders
	} else {
		orders, err := db.SelectList[*rsp.DownloadOrder](dom.A.DA(), "operator_id=? and eid=? and iccid=?", operatorId, eid, iccid)
		if err != nil {
			panic(err)
		}
		return orders
	}
}

func (dom *DownloadOrderManager) SwapEid(da *db.DA, eid string, downloadId int64) int {
	entity := &rsp.DownloadOrder{}
	count, err := da.Update(dom.A.Instance.Id, entity.Table(), "eid=?", "download_id=?", eid, downloadId)
	if err != nil {
		da.Rollback()
		panic(err)
	}
	return count
}

func (dom *DownloadOrderManager) Expire(da *db.DA, downloadId int64) int {
	count, err := da.Update(dom.A.Instance.Id, rsp.DownloadOrder{}.Table(), "expired_time=?", "download_id=?", time.Now().UnixMilli(), downloadId)
	if err != nil {
		da.Rollback()
		panic(err)
	}
	return count
}

func (dom *DownloadOrderManager) UpdateState(da *db.DA, state int, downloadId int64) int {
	entity := &rsp.DownloadOrder{}
	count, err := da.Update(dom.A.Instance.Id, entity.Table(), "state=?", "download_id=?", state, downloadId)
	if err != nil {
		da.Rollback()
		panic(err)
	}
	return count
}

func (dom *DownloadOrderManager) ResetOrder(da *db.DA, downloadId int64) int {
	entity := &rsp.DownloadOrder{}
	count, err := da.Update(dom.A.Instance.Id, entity.Table(), "download_order_attempt_count = 0,confirmation_code_attempt_count=0,success_download_count=0", "download_id=?", downloadId)
	if err != nil {
		da.Rollback()
		panic(err)
	}
	return count
}
