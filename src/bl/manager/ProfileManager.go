package manager

import (
	"bytes"
	"esim.common/src/bl"
	"esim.common/src/code"
	"esim.common/src/entity/dp/mno"
	sys2 "esim.common/src/entity/dp/sys"
	"esim.common/src/entity/hub/sys"
	"esim.es2/src/bo/private"
	"esim.es2/src/bo/private/profile"
	"fmt"
	"k"
	"k/app"
	"k/asn"
	"k/db"
	"k/security"
	"regexp"
	"strconv"
	"strings"
	"time"
)

type ProfileManager struct {
	A   *app.App
	km  *bl.KmsManager
	ptm *ProfileTypeManager
	utm *UppTempManager
	pum *ProfileUppManager
}

func (pm *ProfileManager) Init() {
	pm.km = app.GetManager[*bl.KmsManager](pm.A)
	pm.ptm = app.GetManager[*ProfileTypeManager](pm.A)
	pm.utm = app.GetManager[*UppTempManager](pm.A)
	pm.pum = app.GetManager[*ProfileUppManager](pm.A)
}

const ICCIDRegex = `^[0-9]{19}[0-9F]?$`

func (pm *ProfileManager) GetDefaultProfileType(operatorId int64) *mno.ProfileType {
	profileType, err := db.SelectEntity[*mno.ProfileType](pm.A.DA(), "operator_id=? and state=1 order by is_default desc,type_id desc", operatorId)
	if err != nil {
		panic(err)
	}
	return profileType
}

func (pm *ProfileManager) GetProfileTypeByName(operatorId int64, name string) *mno.ProfileType {
	profileType, err := db.SelectEntity[*mno.ProfileType](pm.A.DA(), "operator_id=? and state=1 and name=?", operatorId, name)
	if err != nil {
		panic(err)
	}
	return profileType
}

func (pm *ProfileManager) FindProfileByTypeWithState(operatorId, typeId int64, state int) *mno.Profile {
	p, err := db.SelectEntity[*mno.Profile](pm.A.DA(), "operator_id=? and state=? and type_id=?", operatorId, state, typeId)
	if err != nil {
		panic(err)
	}
	return p
}

func (pm *ProfileManager) FindProfileByProfileId(profileId int64) *mno.Profile {
	p, err := db.SelectEntity[*mno.Profile](pm.A.DA(), "profile_id=?", profileId)
	if err != nil {
		panic(err)
	}
	return p
}

func (pm *ProfileManager) FindProfileTypeById(operatorId, typeId int64) *mno.ProfileType {
	profileType, err := db.SelectEntity[*mno.ProfileType](pm.A.DA(), "operator_id=? and type_id=?", operatorId, typeId)
	if err != nil {
		panic(err)
	}
	return profileType
}

func (pm *ProfileManager) FindProfileTypeByName(operatorId int64, profileTypeName string) *mno.ProfileType {
	profileType, err := db.SelectEntity[*mno.ProfileType](pm.A.DA(), "operator_id=? and name=?", operatorId, profileTypeName)
	if err != nil {
		panic(err)
	}
	return profileType
}

func (pm *ProfileManager) FindProfileByIccid(operatorId int64, iccid string) *mno.Profile {
	p, err := db.SelectEntity[*mno.Profile](pm.A.DA(), "operator_id=? and (iccid=? or request_iccid=?)", operatorId, iccid, iccid)
	if err != nil {
		panic(err)
	}
	return p
}

func (pm *ProfileManager) InitProfileLifeCycle(da *db.DA, profile *mno.Profile, operator *sys.Operator, eid string, description string, downloadId int64) {
	lifeCycle := &mno.ProfileLifeCycle{
		OperatorId:  operator.OperatorId,
		ProfileId:   profile.ProfileId,
		DownloadId:  downloadId,
		Iccid:       profile.RequestIccid,
		Eid:         eid,
		State:       profile.State,
		StartTime:   time.Now().UnixMilli(),
		Description: description,
		TenantId:    operator.TenantId,
	}
	_, err := da.InsertEntity(lifeCycle, pm.A.Instance.Id)
	if err != nil {
		da.Rollback()
		panic(err)
	}
}

// ChangeLifecycle 需要校验之前的状态是否已经改变，如果修改了，刚基于DA的操作将回滚
func (pm *ProfileManager) ChangeLifecycle(da *db.DA, operator *sys.Operator, downloadId, profileId int64, beforeState, currentState int, eid, iccid, description string) bool {
	count, err := da.Update(pm.A.Instance.Id, "mno_profile_t", "state=?", "operator_id=? and profile_id=? and state=?", currentState, operator.OperatorId, profileId, beforeState)
	if err != nil {
		da.Rollback()
		panic(err)
	}
	if count < 1 {
		da.Rollback()
		return false
	}
	lifeCycle := &mno.ProfileLifeCycle{
		OperatorId:  operator.OperatorId,
		ProfileId:   profileId,
		DownloadId:  downloadId,
		Iccid:       iccid,
		Eid:         eid,
		State:       currentState,
		StartTime:   time.Now().UnixMilli(),
		Description: description,
		TenantId:    operator.TenantId,
	}
	_, err = da.InsertEntity(lifeCycle, pm.A.Instance.Id)
	if err != nil {
		da.Rollback()
		panic(err)
	}
	return true
}

func (pm *ProfileManager) InsertProfile(da *db.DA, profile *mno.Profile) {
	profileId, err := da.InsertEntity(profile, pm.A.Instance.Id)
	if err != nil {
		da.Rollback()
		panic(err)
	}
	profile.ProfileId = int64(profileId)
}

func (pm *ProfileManager) InsertProfileUppByProfile(da *db.DA, profileEntity *mno.Profile, encUpp string) {
	profileUppEntity := &mno.ProfileUpp{
		ProfileId:  profileEntity.ProfileId,
		EncUpp:     encUpp,
		EncKekId:   profileEntity.EncKekId,
		TypeId:     profileEntity.TypeId,
		OperatorId: profileEntity.OperatorId,
		TenantId:   profileEntity.TenantId,
		UpdateBy:   profileEntity.UpdateBy,
	}
	uppId, err := da.InsertEntity(profileUppEntity, pm.A.Instance.Id)
	if err != nil {
		da.Rollback()
		panic(err)
	}
	profileUppEntity.UppId = int64(uppId)
}

func (pm *ProfileManager) GenerateAndInsertProfileUpp(da *db.DA, entity *mno.Profile, operatorKek *private.OperatorKek, bImsi []byte, bKi []byte, bOpc []byte, c9 string) {
	profileType := pm.ptm.SelectByTypeId(entity.TypeId)
	//profileType := pm.ptm.SelectByProfileId(da, entity.ProfileId)
	if profileType == nil {
		return
	}
	if profileType.UppId == 0 {
		return
	}
	uppTemp := pm.utm.SelectByPrimaryKey(profileType.UppId)
	pm.GenerateUppByTemplate(da, entity, uppTemp, profileType, operatorKek, bImsi, bKi, bOpc, c9)
}

func (pm *ProfileManager) GenerateUppByTemplate(da *db.DA, entity *mno.Profile, template *sys2.UppTemplate, profileType *mno.ProfileType, operatorKek *private.OperatorKek, bImsi []byte, bKi []byte, bOpc []byte, c9 string) *mno.ProfileUpp {
	parser := asn.NewTlvByHexTag("30", asn.DerBase64Value(template.Upp))
	iccid := entity.Iccid
	FillProfileHeaders(parser, entity.ProfileType, iccid)
	FillPEMF(parser, iccid, entity.Pin1, entity.Pin2, entity.Adm1)
	FillPuk(parser, entity.Puk1, entity.Puk2)
	FillPin(parser, entity.Pin1, entity.Pin2, entity.Adm1)
	FillPeUsimImsi(parser, entity.Pin1, entity.Pin2, bImsi, entity.ServiceProviderName)
	FillEfPnn(parser, entity.ServiceProviderName, entity.ServiceProviderName)
	FillKiAndOpc(parser, bKi, bOpc)
	FillC9(parser, c9)
	FillMSISDN(parser, entity.Msisdn)
	adjustPlmnA1(parser, "b0", nil, []byte{0x6f, 0x61})
	adjustPlmnA1(parser, "b1", nil, []byte{0x6f, 0x62})
	adjustPlmnA1(parser, "bf26", []byte{0x42, 0x21, 0x00, 0x10}, []byte{0x6f, 0xc5})
	adjustPlmnA1(parser, "bf27", []byte{0x42, 0x21, 0x00, 0x08}, []byte{0x6f, 0xc6})
	adjustPlmnA1(parser, "bf3a", nil, []byte{0x6f, 0xd9})

	adjustAkaRotation(parser, string(bImsi)) //移动和非移动的AKA参数调整

	encUpp := pm.km.EncryptByDataCryptKey(parser.GetValue(), operatorKek.DataKeyName)
	profileUpp := &mno.ProfileUpp{
		EncUpp:     asn.BytesToBase64(encUpp),
		ProfileId:  entity.ProfileId,
		TypeId:     profileType.TypeId,
		OperatorId: entity.OperatorId,
		TenantId:   entity.TenantId,
		Brand:      template.Brand,
		EncKekId:   operatorKek.KekId,
		Model:      template.Model,
		Tac:        template.Tac,
		CreateBy:   entity.CreateBy,
	}
	pm.pum.Insert(da, profileUpp)
	return profileUpp
}

func (pm *ProfileManager) UpdateByPrimaryKey(da *db.DA, entity *mno.Profile) {
	_, err := da.UpdateEntity(pm.A.Instance.Id, entity)
	if err != nil {
		da.Rollback()
		panic(err)
	}
}

func (pm *ProfileManager) DeleteByProfileId(da *db.DA, profileId int64) {
	e := &mno.Profile{}
	_, err := da.Delete(e.Table(), "profile_id=?", profileId)
	if err != nil {
		da.Rollback()
		panic(err)
	}
}

func (pm *ProfileManager) DeleteLifeCycleByProfileId(da *db.DA, profileId int64) {
	e := &mno.ProfileLifeCycle{}
	_, err := da.Delete(e.Table(), "profile_id=?", profileId)
	if err != nil {
		da.Rollback()
		panic(err)
	}
}

func (pm *ProfileManager) SummaryByProfileType(da *db.DA, profileType string, stateStr string, operatorId int64) []*profile.ProfileStateStatistics {
	var state int
	if len(strings.TrimSpace(stateStr)) > 0 {
		for key, value := range code.ProfileState {
			if strings.ToUpper(value) == stateStr {
				state = key
				break
			}
		}
	}
	ifT := len(strings.TrimSpace(profileType)) > 0
	ifS := len(strings.TrimSpace(stateStr)) > 0
	where := new(bytes.Buffer)
	group := new(bytes.Buffer)
	where.WriteString(" where operator_id=" + strconv.Itoa(int(operatorId)))
	if ifT || ifS {
		if ifT && ifS {
			where.WriteString(" and profile_type=")
			where.WriteString(db.BuildSqlStringParam(profileType))
			where.WriteString(" and state=")
			where.WriteString(strconv.Itoa(state))
		} else if ifT && !ifS {
			where.WriteString(" and profile_type=")
			where.WriteString(db.BuildSqlStringParam(profileType))
		} else {
			where.WriteString(" and state=")
			where.WriteString(strconv.Itoa(state))
		}
	}
	if ifT {
		group.WriteString(" group by profile_type,state")
	} else {
		group.WriteString(" group by state")
	}
	sql := new(bytes.Buffer)
	sql.WriteString("select state,count(1) as `count` from mno_profile_t")
	sql.WriteString(where.String())
	sql.WriteString(group.String())
	summary, err := da.Select(sql.String())
	if err != nil {
		panic(err)
	}
	return db.ParseList[*profile.ProfileStateStatistics](summary)
}

func (pm *ProfileManager) SelectLifeCycleListByProfileIdAndDownloadId(da *db.DA, profileId int64, downloadId int64) []*mno.ProfileLifeCycle {
	list, err := da.Select("select * from mno_profile_life_cycle_t where profile_id=? and download_id=? order by update_time desc", profileId, downloadId)
	if err != nil {
		panic(err)
	}
	return db.ParseList[*mno.ProfileLifeCycle](list)
}

func (pm *ProfileManager) InsertProfileCycle(da *db.DA, entity *mno.ProfileLifeCycle) {
	res, err := da.InsertEntity(entity, 0)
	if err != nil {
		da.Rollback()
		panic(err)
	}
	entity.CycleId = int64(res)
}

func FillProfileParams(entity *mno.Profile, paramMap map[string]string) {
	if len(strings.TrimSpace(paramMap["PIN1"])) > 0 {
		entity.Pin1 = paramMap["PIN1"]
	}
	if len(strings.TrimSpace(paramMap["PIN2"])) > 0 {
		entity.Pin2 = paramMap["PIN2"]
	}
	if len(strings.TrimSpace(paramMap["PUK1"])) > 0 {
		entity.Puk1 = paramMap["PUK1"]
	}
	if len(strings.TrimSpace(paramMap["PUK2"])) > 0 {
		entity.Puk2 = paramMap["PUK2"]
	}
	if len(strings.TrimSpace(paramMap["MSISDN"])) > 0 {
		entity.Msisdn = paramMap["MSISDN"]
	}
	if len(strings.TrimSpace(paramMap["ADM1"])) > 0 {
		entity.Adm1 = paramMap["ADM1"]
	}
	if len(strings.TrimSpace(paramMap["IMPI"])) > 0 {
		entity.Impi = paramMap["IMPI"]
	}
	if len(strings.TrimSpace(paramMap["IMPU"])) > 0 {
		//暂不设置
		entity.ImpuList = ""
	}
	if len(strings.TrimSpace(paramMap["C9"])) > 0 {
		entity.AppletRecycle = paramMap["C9"]
	}
	if len(strings.TrimSpace(paramMap["SPN"])) > 0 {
		entity.ServiceProviderName = paramMap["SPN"]
	}
}

var iccidRegex = regexp.MustCompile(ICCIDRegex)

func PaddingIccid(iccid string) string {
	total := 0
	for i := 0; i < len(iccid); i++ {
		number := int(iccid[len(iccid)-i-1])
		if number < 65 { // '0'-'9'
			number -= 48
		} else if number < 97 { // 'A'-'Z'
			number -= 55
		} else { // 'a'-'z'
			number -= 87
		}
		if i%2 == 0 {
			number = number * 2
			number = number/10 + number%10
		}
		total += number
	}
	checkDigit := (total * 9) % 10
	return iccid + fmt.Sprintf("%d", checkDigit)
}

func CheckIccid(iccid string) string {
	res := iccidRegex.MatchString(iccid)
	if res == false {
		return ""
	}
	iccid = strings.ToUpper(iccid)
	if len(iccid) == 20 {
		return iccid
	}
	return iccid + "F"
}

func (pm *ProfileManager) DoAddProfileByMeta(request *app.Request, response *app.Response, addRequest *profile.AddProfileByMetaRequest, baseProfileResponse *profile.BaseProfileResponse) *mno.Profile {
	iccid := CheckIccid(addRequest.Iccid)
	if iccid == "" {
		baseProfileResponse.ProfileNotExist(response, addRequest.Iccid)
		return nil
	}
	ticket := request.FromInstance.ReadTicket(request)
	operator := k.FromJson[*sys.Operator](ticket.Key)
	oldProfile := pm.FindProfileByIccid(operator.OperatorId, iccid)
	if oldProfile != nil {
		baseProfileResponse.ProfileAlreadyExist(response, iccid)
		return nil
	}
	requestProfileType := addRequest.ProfileType
	var profileType *mno.ProfileType
	if len(strings.TrimSpace(requestProfileType)) == 0 {
		profileType = pm.GetDefaultProfileType(operator.OperatorId)
	} else {
		profileType = pm.ptm.SelectByType(operator.OperatorId, requestProfileType)
	}
	if profileType == nil {
		baseProfileResponse.ProfileTypeNotExist(response, requestProfileType)
		return nil
	}
	operatorKek := private.GetOperatorKek(operator)
	aesKey := pm.km.Decrypt(operatorKek.Category, asn.DerBase64Value(addRequest.EncAesKey), operatorKek.TransportKeyName)
	iv := make([]byte, 16)
	bImsi, err := security.AesDecryptByCBCPkcs7(aesKey, iv, asn.DerHexValue(addRequest.Imsi))
	bKi, err1 := security.AesDecryptByCBCPkcs7(aesKey, iv, asn.DerHexValue(addRequest.Ki))
	bOpc, err2 := security.AesDecryptByCBCPkcs7(aesKey, iv, asn.DerHexValue(addRequest.Opc))
	if (err != nil) || (err1 != nil) || (err2 != nil) {
		baseProfileResponse.DecryptImsiKiOpcError(response, iccid)
		return nil
	}

	encImsi := asn.BytesToHex(pm.km.EncryptByDataCryptKey(bImsi, operatorKek.DataKeyName))
	encKi := asn.BytesToHex(pm.km.EncryptByDataCryptKey(bKi, operatorKek.DataKeyName))
	encOpc := asn.BytesToHex(pm.km.EncryptByDataCryptKey(bOpc, operatorKek.DataKeyName))

	profileEntity := &mno.Profile{
		State:               code.ProfileStateAvailable,
		ProfileType:         profileType.Name,
		TypeId:              profileType.TypeId,
		Iccid:               iccid,
		RequestIccid:        addRequest.Iccid,
		EncKekId:            operatorKek.KekId,
		OperatorId:          operator.OperatorId,
		ShortDescription:    profileType.ProfileName,
		TenantId:            operator.TenantId,
		Pin1:                addRequest.Pin1,
		Pin2:                addRequest.Pin2,
		Puk1:                addRequest.Puk1,
		Puk2:                addRequest.Puk2,
		Msisdn:              addRequest.Msisdn,
		Adm1:                addRequest.Adm1,
		AppletRecycle:       addRequest.C9,
		Impi:                addRequest.Impi,
		ImpuList:            "",
		PppModel:            profileType.PppModel,
		Clazz:               profileType.Clazz,
		Imsi:                encImsi,
		Ki:                  encKi,
		Opc:                 encOpc,
		UpdateBy:            pm.A.Instance.Id,
		ServiceProviderName: profileType.ServiceProviderName,
	}
	if addRequest.ServiceProviderName != "" {
		profileEntity.ServiceProviderName = addRequest.ServiceProviderName
	}
	CreateProfileOwner(profileEntity, profileType, bImsi)
	da := pm.A.DA()
	da.Begin()
	defer da.Rollback()
	pm.InsertProfile(da, profileEntity)
	pm.GenerateAndInsertProfileUpp(da, profileEntity, operatorKek, bImsi, bKi, bOpc, profileEntity.AppletRecycle)
	pm.InitProfileLifeCycle(da, profileEntity, operator, "", "", 0)
	da.Commit()
	baseProfileResponse.Iccid = profileEntity.Iccid
	return profileEntity
}
