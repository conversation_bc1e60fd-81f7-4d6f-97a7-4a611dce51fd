package manager

import (
	"esim.common/src/code"
	"esim.common/src/entity/dp/mno"
	"esim.common/src/entity/dp/rsp"
	"esim.common/src/entity/hub/sys"
	"k/app"
	"k/db"
	"time"
)

type OrderManager struct {
	A *app.App
}

func (om *OrderManager) CreateDownloadOrder(da *db.DA, profile *mno.Profile, state int, profileType *mno.ProfileType, operator *sys.Operator, eid, confirmationCode, matchingId, smdsAddress, functionRequesterIdentifier, functionCallIdentifier string) *rsp.DownloadOrder {
	downloadOrder := &rsp.DownloadOrder{
		ProfileId:                      profile.ProfileId,
		OperatorId:                     operator.OperatorId,
		TypeId:                         profileType.TypeId,
		Eid:                            eid,
		Iccid:                          profile.RequestIccid,
		ReDownloadPolicy:               profileType.ReDownloadPolicy,
		HashConfirmationCode:           confirmationCode,
		ServiceProviderName:            profile.ServiceProviderName,
		ShortDescription:               profile.ShortDescription,
		SmdpAddress:                    operator.SmdpAddress,
		State:                          state,
		Icon:                           profileType.Icon,
		IconType:                       profileType.IconType,
		Ara:                            profileType.Ara,
		Mode:                           code.DownloadOrderModeAc,
		ProfileType:                    profileType.Name,
		ProfileClazz:                   profile.Clazz,
		SmdsAddress:                    smdsAddress,
		FunctionRequesterIdentifier:    functionRequesterIdentifier,
		FunctionCallIdentifier:         functionCallIdentifier,
		PolicyRules:                    profileType.PolicyRules,
		MatchingId:                     matchingId,
		ReleaseFlag:                    1,
		DownloadOrderAttemptMaxCount:   profileType.DownloadOrderAttemptMaxCount,
		DownloadOrderCcAttemptMaxCount: profileType.DownloadOrderCcAttemptMaxCount,
		DownloadOrderExpiredMinute:     profileType.DownloadOrderExpiredMinute,
		DownloadOrderMaxSuccessCount:   profileType.DownloadOrderMaxSuccessCount,
		SuccessDownloadCount:           0,
		NotificationConfiguration:      profileType.NotificationConfiguration,
		ServiceProviderMessage:         profileType.ServiceProviderMessage,
		PppModel:                       profile.PppModel,
		ExpiredTime:                    time.Now().UnixMilli() + int64(profileType.DownloadOrderExpiredMinute*60000),
		ProfileOwner:                   profile.ProfileOwner,
		DownloadOrderAttemptCount:      profileType.DownloadOrderAttemptMaxCount,
		ConfirmationCodeAttemptCount:   profileType.DownloadOrderCcAttemptMaxCount,
		TenantId:                       operator.TenantId,
	}
	id, err := da.InsertEntity(downloadOrder, om.A.Instance.Id)
	if err != nil {
		panic(err)
	}
	downloadOrder.DownloadId = int64(id)
	return downloadOrder
}

func (om *OrderManager) CreateDownloadOrderByBatchPreOccupy(da *db.DA, state int, profileType *mno.ProfileType, operator *sys.Operator, matchingId, functionRequesterIdentifier, functionCallIdentifier string) *rsp.DownloadOrder {
	downloadOrder := &rsp.DownloadOrder{
		ProfileId:                      0,
		OperatorId:                     operator.OperatorId,
		TypeId:                         profileType.TypeId,
		Eid:                            "",
		Iccid:                          "",
		HashConfirmationCode:           "",
		ServiceProviderName:            "",
		ShortDescription:               "",
		SmdpAddress:                    operator.SmdpAddress,
		State:                          state,
		Mode:                           code.DownloadOrderModePreOccupy,
		ProfileType:                    profileType.Name,
		ProfileClazz:                   0,
		SmdsAddress:                    "",
		FunctionRequesterIdentifier:    functionRequesterIdentifier,
		FunctionCallIdentifier:         functionCallIdentifier,
		PolicyRules:                    profileType.PolicyRules,
		MatchingId:                     matchingId,
		ReleaseFlag:                    1,
		Ara:                            profileType.Ara,
		DownloadOrderAttemptMaxCount:   profileType.DownloadOrderAttemptMaxCount,
		DownloadOrderCcAttemptMaxCount: profileType.DownloadOrderCcAttemptMaxCount,
		DownloadOrderExpiredMinute:     profileType.DownloadOrderExpiredMinute,
		DownloadOrderMaxSuccessCount:   profileType.DownloadOrderMaxSuccessCount,
		ReDownloadPolicy:               profileType.ReDownloadPolicy,
		SuccessDownloadCount:           0,
		NotificationConfiguration:      profileType.NotificationConfiguration,
		ServiceProviderMessage:         profileType.ServiceProviderMessage,
		PppModel:                       0,
		ExpiredTime:                    time.Now().UnixMilli() + int64(profileType.DownloadOrderExpiredMinute*60000),
		ProfileOwner:                   "",
		DownloadOrderAttemptCount:      profileType.DownloadOrderAttemptMaxCount,
		ConfirmationCodeAttemptCount:   profileType.DownloadOrderCcAttemptMaxCount,
		TenantId:                       operator.TenantId,
	}
	if profileType.DownloadOrderExpiredMinute == 0 {
		downloadOrder.ExpiredTime = 0
	}
	id, err := da.InsertEntity(downloadOrder, om.A.Instance.Id)
	if err != nil {
		panic(err)
	}
	downloadOrder.DownloadId = int64(id)
	return downloadOrder
}
