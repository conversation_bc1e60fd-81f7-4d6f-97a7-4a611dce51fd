package manager

import (
	"esim.common/src/entity/dp/rsp"
	"esim.es2/src"
	"fmt"
	"k/app"
	"k/db"
	"k/logger"
	"sync/atomic"
	"time"
)

type DownloadOrderScheduledManager struct {
	A     *app.App
	run   atomic.Bool
	param *src.Es2Param
	da    *db.DA
}

func (nm *DownloadOrderScheduledManager) Init() {
	nm.run.Store(false)
	nm.da = nm.A.DA()
	nm.param = app.GetClientParam[*src.Es2Param](nm.A)
	nm.A.RegisterTimeCycleCallback("DownloadOrder", func() {
		nm.doDownloadOrder()
	})
}

func (nm *DownloadOrderScheduledManager) doDownloadOrder() {
	if !nm.run.CompareAndSwap(false, true) {
		return
	}
	defer func() {
		if err := recover(); err != nil {
			logger.E("doDownloadOrder", err)
		}
		nm.run.Store(false)
	}()
	current := time.Now().UnixMilli()
	stateTime := current - nm.param.DownloadOrderCycleStateTime*24*60*60*1000
	expiredTime := current - nm.param.DownloadOrderCycleExpiredTime*24*60*60*1000

	list, err := db.SelectList[*rsp.DownloadOrder](nm.da, "(expired_time<? or (state=9 and update_time<?)) and (locked_by=0 or locked_by=?) and expired_time<>0  limit ?", expiredTime, stateTime, nm.A.Instance.Id, nm.param.DownloadOrderCycleLimitCount)
	if err != nil {
		panic(err)
	}
	if len(list) < 1 {
		return
	}
	da := nm.A.DA()
	column := ""
	for _, v := range list {
		column += fmt.Sprintf("%v", v.DownloadId) + ","
	}
	column = column[:len(column)-1]
	updateSql := fmt.Sprintf("update rsp_download_order_t set locked_by=%d where download_id in(%v)", nm.A.Instance.Id, column)
	nm.da.Exec(updateSql)
	da.Begin()
	defer da.Rollback()
	sql := fmt.Sprintf("insert into rsp_download_order_history_t select * from rsp_download_order_t WHERE download_id in(%v) and locked_by=%d", column, nm.A.Instance.Id)
	result, err := da.Exec(sql)
	if err != nil {
		da.Rollback()
		panic(err)
	}
	if result.AffectedRows > 0 {
		entity := &rsp.DownloadOrder{}
		where := fmt.Sprintf("download_id in (%v) and locked_by=%v", column, nm.A.Instance.Id)
		count, err1 := da.Delete(entity.Table(), where)
		if err1 != nil || count < 1 {
			da.Rollback()
			panic(err)
		}
		logger.TL("DownloadOrderScheduledManager", "SyncSuccessNum", result.AffectedRows)
		da.Commit()
	}
}
