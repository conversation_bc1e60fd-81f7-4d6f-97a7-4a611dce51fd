package manager

import (
	"bytes"
	"esim.common/src/entity/dp/sum"
	"fmt"
	"k/app"
	"k/db"
	"k/logger"
	"sync/atomic"
	"time"
)

type ProfileStatisticManager struct {
	A          *app.App
	run        atomic.Bool
	da         *db.DA
	currentDay string
}

type ProfileStatistic struct {
	TenantId       string `json:"tenantId"`
	DateStr        string `json:"dateStr"`
	TotalCount     int    `json:"totalCount"`
	AvailableCount int    `json:"availableCount"`
}

func (nm *ProfileStatisticManager) Init() {
	nm.run.Store(false)
	nm.da = nm.A.DA()
	nm.A.RegisterTimeCycleCallback("ProfileStatistic", func() {
		nm.doProfileReport()
	})
}

func (nm *ProfileStatisticManager) doProfileReport() {
	if !nm.run.CompareAndSwap(false, true) {
		return
	}
	defer func() {
		if err := recover(); err != nil {
			logger.E("ProfileStatistic", err)
		}
		nm.run.Store(false)
	}()
	if nm.currentDay == "" {
		nm.currentDay = time.Now().Format("2006-01-02")
		return
	}
	today := time.Now().Format("2006-01-02")
	if today == nm.currentDay {
		return
	}
	fmt.Println("ProfileStatistic start")
	//业务
	sql := new(bytes.Buffer)
	sql.WriteString(" SELECT a.tenant_id,b.dateStr date_str,count(*) total_count,SUM(CASE WHEN state = 0 THEN 1 ELSE 0 END) available_count")
	sql.WriteString(" FROM mno_profile_life_cycle_t a INNER JOIN(")
	sql.WriteString("SELECT FROM_UNIXTIME(create_time / 1000, '%Y-%m-%d') dateStr, iccid, MAX(cycle_id) cycle_id FROM mno_profile_life_cycle_t")
	sql.WriteString(" WHERE create_time BETWEEN ")
	sql.WriteString(fmt.Sprintf("%v", nm.getYesterdayMinTime()))
	sql.WriteString(" and ")
	sql.WriteString(fmt.Sprintf("%v", nm.getYesterdayMaxTime()))
	sql.WriteString("  GROUP BY iccid, dateStr) b ON a.cycle_id = b.cycle_id GROUP BY a.tenant_id, b.dateStr")
	orderReport, err := nm.da.Select(sql.String())
	if err != nil {
		panic(err)
	}
	sumList := db.ParseList[*ProfileStatistic](orderReport)
	for _, item := range sumList {
		downloadSum := &sum.ProfileStatistic{
			TenantId:       item.TenantId,
			TotalCount:     item.TotalCount,
			AvailableCount: item.AvailableCount,
			DateYmd:        item.DateStr,
			CreateTime:     time.Now().UnixMilli(),
			CreateBy:       nm.A.Instance.Id,
		}
		nm.da.InsertEntity(downloadSum, nm.A.Instance.Id)
	}
	nm.currentDay = today
}
func (nm *ProfileStatisticManager) getYesterdayMaxTime() int64 {
	// 获取当前时间
	now := time.Now()
	// 设置为昨天的最大时间（23:59:59）
	yesterdayMax := time.Date(
		now.Year(),
		now.Month(),
		now.Day(),
		0, 0, 0, 0,
		now.Location(),
	)
	return yesterdayMax.UnixMilli()
}

func (nm *ProfileStatisticManager) getYesterdayMinTime() int64 {
	// 获取当前时间
	now := time.Now()
	// 计算昨天的起始时间（将当前时间截断到天，再减去1天）
	yesterdayStart := now.Add(-24 * time.Hour)
	// 设置为昨天的最小时间（0:0:0）
	yesterdayMax := time.Date(
		yesterdayStart.Year(),
		yesterdayStart.Month(),
		yesterdayStart.Day(),
		0, 0, 0, 0,
		yesterdayStart.Location(),
	)
	return yesterdayMax.UnixMilli()
}

func (nm *ProfileStatisticManager) getYesterdayTime() string {
	// 获取当前时间
	now := time.Now()
	// 计算昨天的起始时间（当前时间减去24小时）
	yesterdayStart := now.Add(-24 * time.Hour)
	// 计算昨天的最大时间（昨天起始时间加上23小时59分59秒）
	return yesterdayStart.Format("2006-01-02")
}
