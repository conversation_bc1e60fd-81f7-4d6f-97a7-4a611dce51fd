package manager

import (
	"esim.common/src/bl"
	"esim.common/src/entity/dp/mno"
	"esim.es2/src/bo/private"
	"k/app"
	"k/asn"
	"k/db"
)

type ProfileUppManager struct {
	A  *app.App
	km *bl.KmsManager
}

func (pum *ProfileUppManager) Init() {
	pum.km = app.GetManager[*bl.KmsManager](pum.A)
}

func (pum *ProfileUppManager) Insert(da *db.DA, upp *mno.ProfileUpp) {
	uppId, err := da.InsertEntity(upp, pum.A.Instance.Id)
	if err != nil {
		da.Rollback()
		panic(err)
	}
	upp.UppId = int64(uppId)
}

func (pum *ProfileUppManager) SelectByProfileId(profileId int64) *mno.ProfileUpp {
	pu, err := db.SelectEntity[*mno.ProfileUpp](pum.A.DA(), "profile_id=?", profileId)
	if err != nil {
		panic(err)
	}
	return pu
}

func (pum *ProfileUppManager) UpdateByCondition(da *db.DA, entity *mno.Profile, operatorKek *private.OperatorKek, bImsi []byte, bKi []byte, bOpc []byte, c9 string, pUpp *mno.ProfileUpp) {
	bUpp := pum.km.DecryptByDataCryptKey(asn.DerBase64Value(pUpp.EncUpp), operatorKek.DataKeyName)
	parser := asn.NewTlvByHexTag("30", bUpp)
	iccid := entity.Iccid
	//update upp asn
	FillProfileHeaders(parser, entity.ProfileType, iccid)
	FillPEMF(parser, iccid, entity.Pin1, entity.Pin2, entity.Adm1)
	FillPuk(parser, entity.Puk1, entity.Puk2)
	FillPin(parser, entity.Pin1, entity.Pin2, entity.Adm1)
	FillPeUsimImsi(parser, entity.Pin1, entity.Pin2, bImsi, entity.ServiceProviderName)
	FillKiAndOpc(parser, bKi, bOpc)
	FillC9(parser, c9)
	pUpp.EncUpp = asn.BytesToBase64(pum.km.EncryptByDataCryptKey(parser.GetValue(), operatorKek.DataKeyName))
	pUpp.UpdateBy = entity.UpdateBy
	pum.UpdateByPrimaryKey(da, pUpp)
}

func (pum *ProfileUppManager) UpdateByPrimaryKey(da *db.DA, entity *mno.ProfileUpp) {
	_, err := da.UpdateEntity(pum.A.Instance.Id, entity)
	if err != nil {
		da.Rollback()
		panic(err)
	}
}

func (pum *ProfileUppManager) DeleteByProfileId(da *db.DA, profileId int64) {
	_, err := da.Delete(mno.ProfileUpp{}.Table(), "profile_id=?", profileId)
	if err != nil {
		da.Rollback()
		panic(err)
	}
}
