package handler

import (
	"esim.common/src/bo"
	"esim.common/src/code"
	"esim.common/src/entity/dp/mno"
	"esim.common/src/entity/dp/rsp"
	"esim.common/src/entity/hub/sys"
	"esim.es2/src"
	"esim.es2/src/bl/manager"
	"esim.es2/src/bo/gsma"
	"k"
	"k/app"
	"k/asn"
	"k/db"
	"k/logger"
	"k/security"
	"strconv"
)

type GsmaHandler struct {
	A     *app.App
	param *src.Es2Param
	dm    *manager.DsManager
	dom   *manager.DownloadOrderManager
	pm    *manager.ProfileManager
}

func (gh *GsmaHandler) Init() {
	gh.param = app.GetClientParam[*src.Es2Param](gh.A)
	gh.dm = app.GetManager[*manager.DsManager](gh.A)
	gh.pm = app.GetManager[*manager.ProfileManager](gh.A)
	gh.dom = app.GetManager[*manager.DownloadOrderManager](gh.A)
}

// DownloadOrder This function is used to instruct the SM-DP+ of a new Profile download request.
func (gh *GsmaHandler) DownloadOrder(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "DownloadOrderException")
		}
	}()

	downloadOrderRequest := k.FromJson[*gsma.DownloadOrderRequest](string(request.GetBody()))
	downloadOrderRequest.Header.Check(response)
	ticket := request.FromInstance.ReadTicket(request)
	operator := k.FromJson[*sys.Operator](ticket.Key)

	downloadOrderResponse := gsma.NewDownloadOrderResponse()

	//The EID is optional and MAY not be known at this stage. If the EID is known, the SM-DP+, with the Operator,
	//MAY verify if the EID is compatible with the requested Profile Type (see also Annex F).
	//If an SM-DS or Default SM-DP+ is to be used for the Profile download, then the EID SHOULD be present;
	//if not present, the EID SHALL be provided later in "ES2+.ConfirmOrder".
	profileState := code.ProfileStateLinked
	if downloadOrderRequest.Eid == "" {
		profileState = code.ProfileStateAllocated
	}
	var profileType *mno.ProfileType
	var profile *mno.Profile
	if downloadOrderRequest.Iccid == "" {
		if downloadOrderRequest.ProfileType == "" {
			profileType = gh.pm.GetDefaultProfileType(operator.OperatorId)
		} else {
			profileType = gh.pm.GetProfileTypeByName(operator.OperatorId, downloadOrderRequest.ProfileType)
		}
		if profileType == nil {
			downloadOrderResponse.ProfileTypeUnknown(response, downloadOrderRequest.ProfileType)
			return
		}
		//根据ProfileType，查找一个合适的Iccid
		profile = gh.pm.FindProfileByTypeWithState(operator.OperatorId, profileType.TypeId, code.ProfileStateAvailable)
		if profile == nil {
			downloadOrderResponse.ProfileTypeUnAvailable(response, profileType.Name)
			return
		}
	} else {
		profile = gh.pm.FindProfileByIccid(operator.OperatorId, downloadOrderRequest.Iccid)
		if profile == nil {
			downloadOrderResponse.ProfileIccidUnknown(response, downloadOrderRequest.Iccid)
			return
		}
		if profile.ProfileType != downloadOrderRequest.ProfileType && downloadOrderRequest.ProfileType != "" {
			downloadOrderResponse.ProfileTypeRefused(response, downloadOrderRequest.ProfileType)
			return
		}
		profileType = gh.pm.FindProfileTypeById(operator.OperatorId, profile.TypeId)
		if profileType.State == 0 {
			downloadOrderResponse.ProfileTypeNotAllowed(response, profileType.Name)
			return
		}
	}

	//profile当前的状态判断
	if profile.State == code.ProfileStateAvailable { //新增Download
		da := gh.A.DA()
		da.Begin()
		defer da.Rollback()
		downloadId := gh.dom.CreateDownloadOrder(da, profile, profileState, profileType, operator, downloadOrderRequest)
		if gh.pm.ChangeLifecycle(da, operator, downloadId, profile.ProfileId, profile.State, profileState, downloadOrderRequest.Eid, profile.RequestIccid, "DownloadOrder") {
			da.Commit()
			downloadOrderResponse.Iccid = profile.Iccid
			response.WriteJson(downloadOrderResponse)
			return
		}
		downloadOrderResponse.ProfileIccidAlreadyInUse(response, profile.Iccid)
		return
	}

	//If the Profile identified by the ICCID is already in state "Linked" or "Allocated" and this function would result in exactly
	//this state when performed on an unallocated Profile, the function may return 'Executed-Success' and take no other action.
	if profile.State == code.ProfileStateLinked { //判断是否Link到当前Eid
		if downloadOrderRequest.Eid == "" {
			downloadOrderResponse.ProfileIccidNotAllowed(response, profile.Iccid)
			return
		}
		order := gh.dom.FindDownloadOrderByEidWithProfileId(operator.OperatorId, profile.ProfileId, downloadOrderRequest.Eid)
		if order == nil {
			downloadOrderResponse.ProfileIccidNotAllowed(response, profile.Iccid)
			return
		}
		downloadOrderResponse.Iccid = profile.Iccid
		response.WriteJson(downloadOrderResponse)
		return
	}
	downloadOrderResponse.ProfileIccidAlreadyInUse(response, profile.Iccid)
}

// ConfirmOrder
// This function is used to confirm a previously requested download order.
// If an SM-DS or Default SM-DP+ is to be used for the Profile download and the EID has not been provided within the DownloadOrder function, then the EID SHALL be present.
// If EID is not present, the SM-DP+ SHALL return a 'Function execution status' indicating 'Failed' with a status code "EID - Mandatory Element Missing".
// If the EID is present in both the DownloadOrder and ConfirmOrder functions it SHALL be the same value. If EID is different, the SM-DP+ SHALL return a 'Function execution status' indicating 'Failed' with a status code "EID - Invalid Association".
func (gh *GsmaHandler) ConfirmOrder(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "ConfirmOrderException")
		}
	}()

	coRequest := k.FromJson[*gsma.ConfirmOrderRequest](string(request.GetBody()))
	coRequest.Header.Check(response)
	ticket := request.FromInstance.ReadTicket(request)
	operator := k.FromJson[*sys.Operator](ticket.Key)

	coResponse := gsma.NewConfirmOrderResponse()
	order := gh.dom.FindAllocatedDownloadOrder(operator.OperatorId, coRequest.Iccid)
	if order == nil {
		coResponse.ProfileIccidUnknown(response, coRequest.Iccid)
		return
	}
	column := ""
	if order.MatchingId == "" { //需要生成
		if coRequest.MatchingId == "" {
			order.MatchingId = code.NewMatchingId(order.OperatorId).String()
		} else { //校验提供的MatchingId
			mi := code.ParseMatchingId(coRequest.MatchingId)
			if mi.Verify() {
				order.MatchingId = mi.String()
			} else {
				coResponse.MatchingIdInvalid(response)
				return
			}
			if gh.dom.CheckMatchingIdExist(operator.OperatorId, order.DownloadId, coRequest.MatchingId) {
				coResponse.MatchingIdAlreadyInUse(response)
				return
			}
		}
		//保存MatchingId
		column = ",matching_id=" + db.BuildSqlStringParam(order.MatchingId)
	}
	if order.Eid != "" && coRequest.Eid != "" && coRequest.Eid != order.Eid {
		coResponse.EidInvalidAssociation(response)
		return
	}
	if order.Eid == "" && coRequest.Eid != "" {
		order.Eid = coRequest.Eid
		column += ",eid=" + db.BuildSqlStringParam(order.Eid)
	}
	if coRequest.ConfirmationCode != "" {
		order.HashConfirmationCode = asn.BytesToHex(security.Sha256([]byte(coRequest.ConfirmationCode)))
		column += ",hash_confirmation_code=" + db.BuildSqlStringParam(order.HashConfirmationCode)
	}
	if coRequest.RootSmdsAddress != "" {
		if coRequest.RootSmdsAddress[0] == '.' {
			//TODO：采用自己配置的If the Root SM-DS address begins with a full stop character (e.g., '.unspecified'), the SM-DP+ MAY determine the applicable Root SM-DS for this Profile in an implementation-dependent manner.
			order.RootSmdsAddress = gh.param.DefaultRootSmdsDomain
		} else {
			order.RootSmdsAddress = coRequest.RootSmdsAddress
		}
		column += ",root_smds_address=" + db.BuildSqlStringParam(order.RootSmdsAddress)
		if order.MatchingId == "" {
			coResponse.MatchingIdInvalid(response)
			return
		}
		if order.Eid == "" {
			coResponse.EidMandatoryElementMissing(response)
			return
		}
		if coRequest.ReleaseFlag { //ReleaseProfile
			gh.dm.RegisterEvent(order.MatchingId, order.Eid, order.RootSmdsAddress)
		}
	}
	if coRequest.SmdsAddress != "" {
		if coRequest.SmdsAddress[0] == '.' {
			//TODO：采用自己配置的If the Root SM-DS address begins with a full stop character (e.g., '.unspecified'), the SM-DP+ MAY determine the applicable Root SM-DS for this Profile in an implementation-dependent manner.
			order.SmdsAddress = gh.param.DefaultRootSmdsDomain
		} else {
			order.SmdsAddress = coRequest.SmdsAddress
		}
		column += ",smds_address=" + db.BuildSqlStringParam(order.SmdsAddress)
		if order.MatchingId == "" {
			coResponse.MatchingIdInvalid(response)
			return
		}
		if order.Eid == "" {
			coResponse.EidMandatoryElementMissing(response)
			return
		}

	}
	beforeState := order.State
	if coRequest.ReleaseFlag {
		column += ",state=" + strconv.Itoa(code.DownloadStateReleased)
		column += ",release_flag=1"
		order.State = code.DownloadStateReleased
	} else {
		column += ",state=" + strconv.Itoa(code.DownloadStateConfirmed)
		column += ",release_flag=0"
		order.State = code.DownloadStateConfirmed
	}
	da := gh.A.DA()
	da.Begin()
	defer da.Rollback()
	if !gh.pm.ChangeLifecycle(da, operator, order.DownloadId, order.ProfileId, beforeState, order.State, order.Eid, order.Iccid, "ConfirmOrder") {
		coResponse.ProfileIccidNotAllowed(response, order.Iccid)
		return
	}
	if coRequest.ReleaseFlag { //ReleaseProfile
		if order.SmdsAddress != "" {
			gh.dm.RegisterEvent(order.MatchingId, order.Eid, order.SmdsAddress)
		}
		if order.RootSmdsAddress != "" {
			gh.dm.RegisterEvent(order.MatchingId, order.Eid, order.RootSmdsAddress)
		}
	}

	//fill function identifier
	column += ",function_requester_identifier=" + db.BuildSqlStringParam(coRequest.Header.FunctionRequesterIdentifier)
	column += ",function_call_identifier=" + db.BuildSqlStringParam(coRequest.Header.FunctionCallIdentifier)

	if column != "" {
		column = column[1:]
		_, err := gh.A.DA().Update(gh.A.Instance.Id, order.Table(), column, "download_id=?", order.DownloadId)
		if err != nil {
			da.Rollback()
			panic(err)
		}
	}

	da.Commit()
	coResponse.Eid = order.Eid
	coResponse.MatchingId = order.MatchingId
	coResponse.SmdpAddress = order.SmdpAddress
	response.WriteJson(coResponse)
}

func (gh *GsmaHandler) ReleaseProfile(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "ReleaseOrderException")
		}
	}()

	rpRequest := k.FromJson[*gsma.ReleaseProfileRequest](string(request.GetBody()))
	rpRequest.Header.Check(response)
	ticket := request.FromInstance.ReadTicket(request)
	operator := k.FromJson[*sys.Operator](ticket.Key)
	rpResponse := gsma.NewReleaseProfileResponse()
	order := gh.dom.FindConfirmDownloadOrder(operator.OperatorId, rpRequest.Iccid)
	if order == nil {
		rpResponse.ProfileIccidUnknown(response, rpRequest.Iccid)
		return
	}
	da := gh.A.DA()
	da.Begin()
	defer da.Rollback()
	if !gh.pm.ChangeLifecycle(da, operator, order.DownloadId, order.ProfileId, code.ProfileStateConfirmed, code.ProfileStateReleased, order.Eid, order.Iccid, "ReleaseProfile") {
		rpResponse.ProfileIccidInvalidTransition(response, order.Iccid)
		return
	}
	_, err := da.Update(gh.A.Instance.Id, order.Table(), "state=?", "download_id=?", code.ProfileStateReleased, order.DownloadId)
	if err != nil {
		da.Rollback()
		panic(err)
	}
	if order.ReleaseFlag == 0 { //ReleaseProfile
		if order.SmdsAddress != "" {
			gh.dm.RegisterEvent(order.MatchingId, order.Eid, order.SmdsAddress)
		}
		if order.RootSmdsAddress != "" {
			gh.dm.RegisterEvent(order.MatchingId, order.Eid, order.RootSmdsAddress)
		}
	}
	da.Commit()
	response.WriteJson(rpResponse)
}

func (gh *GsmaHandler) CancelOrder(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "CancelOrderException")
		}
	}()

	coRequest := k.FromJson[*gsma.CancelOrderRequest](string(request.GetBody()))
	coRequest.Header.Check(response)
	ticket := request.FromInstance.ReadTicket(request)
	operator := k.FromJson[*sys.Operator](ticket.Key)
	if coRequest.FinalProfileStatusIndicator == "" { //RPM
		//TODO
		return
	}
	coResponse := gsma.NewCancelOrderResponse()
	if coRequest.Iccid == "" {
		coResponse.ProfileIccidMandatoryElementMissing(response)
		return
	}
	profile := gh.pm.FindProfileByIccid(operator.OperatorId, coRequest.Iccid)
	if profile == nil {
		coResponse.ProfileIccidUnknown(response)
		return
	}

	if profile.State == code.ProfileStateDownloaded || profile.State == code.ProfileStateInstalled {
		coResponse.ProfileIccidAlreadyInUse(response, profile.Iccid)
		return
	}

	var downloadOrder *rsp.DownloadOrder
	if coRequest.MatchingId == "" {
		downloadOrder = gh.dom.FindDownloadOrderByIccid(operator.OperatorId, coRequest.Iccid)
	} else {
		downloadOrder = gh.dom.FindDownloadOrderByMatchingId(operator.OperatorId, coRequest.Iccid, coRequest.MatchingId)
	}
	if downloadOrder == nil {
		coResponse.ProfileIccidNotAllowed(response, coRequest.Iccid)
		return
	}
	if downloadOrder.Eid != "" {
		if coRequest.Eid == "" {
			coResponse.EidMandatoryElementMissing(response)
			return
		}
		if downloadOrder.Eid != coRequest.Eid {
			coResponse.EidInvalidAssociation(response, coRequest.Iccid)
			return
		}
	}
	if downloadOrder.MatchingId != "" {
		if coRequest.MatchingId == "" {
			coResponse.MatchingIdMandatoryElementMissing(response, coRequest.Iccid)
			return
		}
		if downloadOrder.MatchingId != coRequest.MatchingId {
			coResponse.MatchingIdInvalidAssociation(response)
			return
		}
	}
	if coRequest.FinalProfileStatusIndicator != finalProfileStatusUnAvailable && coRequest.FinalProfileStatusIndicator != finalProfileStatusAvailable {
		coResponse.ProfileIccidNotAllowed(response, coRequest.Iccid)
		return
	}
	da := gh.A.DA()
	da.Begin()
	defer da.Rollback()
	state := code.ProfileStateAvailable
	if coRequest.FinalProfileStatusIndicator == finalProfileStatusUnAvailable {
		state = code.ProfileStateUnavailable
	}
	if gh.pm.ChangeLifecycle(da, operator, downloadOrder.DownloadId, profile.ProfileId, profile.State, state, downloadOrder.Eid, profile.RequestIccid, "CancelOrder") {
		gh.dom.UpdateState(da, code.DownloadStateCancelled, downloadOrder.DownloadId)
	}
	da.Commit()
	response.WriteJson(coResponse)
}

const (
	finalProfileStatusAvailable   = "Available"
	finalProfileStatusUnAvailable = "Unavailable"
)
