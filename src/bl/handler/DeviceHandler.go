package handler

import (
	"esim.common/src/bl"
	"esim.common/src/bo"
	"esim.common/src/code"
	"esim.common/src/entity/dp/mno"
	"esim.common/src/entity/hub/sys"
	"esim.es2/src"
	"esim.es2/src/bl/manager"
	"esim.es2/src/bo/private"
	"esim.es2/src/bo/private/device"
	"k"
	"k/app"
	"k/asn"
	"k/logger"
	"strconv"
)

type DeviceHandler struct {
	A     *app.App
	param *src.Es2Param
	dm    *manager.DeviceManager
	dom   *manager.DownloadOrderManager
	tm    *manager.TacManager
	pm    *manager.ProfileManager
	km    *bl.KmsManager
}

func (dh *DeviceHandler) Init() {
	dh.param = app.GetClientParam[*src.Es2Param](dh.A)
	dh.dm = app.GetManager[*manager.DeviceManager](dh.A)
	dh.tm = app.GetManager[*manager.TacManager](dh.A)
	dh.dom = app.GetManager[*manager.DownloadOrderManager](dh.A)
	dh.pm = app.GetManager[*manager.ProfileManager](dh.A)
	dh.km = app.GetManager[*bl.KmsManager](dh.A)
}

func (dh *DeviceHandler) DeviceInfo(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "DeviceInfo")
		}
	}()
	deviceInfoResponse := device.NewGetDeviceInfoResponse()
	deviceInfoRequest := k.FromJson[*device.GetDeviceInfoRequest](string(request.GetBody()))
	deviceInfoRequest.Header.Check(response)
	ticket := request.FromInstance.ReadTicket(request)
	operator := k.FromJson[*sys.Operator](ticket.Key)
	operatorKek := private.GetOperatorKek(operator)
	if deviceInfoRequest.Eid == "" {
		deviceInfoResponse.DeviceInfoEIDInvalid(response)
		return
	}
	deviceBO := dh.dm.FindDeviceByEid(deviceInfoRequest.Eid)
	if deviceBO == nil {
		deviceInfoResponse.DeviceInfoNotExist(response)
		return
	}
	block := dh.dm.FindBlockByEidOrTac(operator.OperatorId, deviceBO.Eid, deviceBO.Tac)
	if block == nil {
		deviceInfoResponse.Block = false
	} else {
		deviceInfoResponse.Block = true
	}
	deviceInfoResponse.Eid = deviceBO.Eid
	deviceInfo := &device.Deviceinfo{
		Imei:       deviceBO.Imei,
		Tac:        deviceBO.Tac,
		DeviceName: deviceBO.Name,
		DeviceType: deviceBO.Model,
		Brand:      deviceBO.Brand,
	}
	deviceInfoResponse.Info = deviceInfo
	orders := dh.dom.SelectDownloadsByEid(deviceBO.Eid, operator.OperatorId)
	var profileTypeMap = map[string]*mno.ProfileType{}
	var profileMap = map[int64]*mno.Profile{}
	if orders != nil || len(orders) > 0 {
		list := make([]*device.DeviceOrder, len(orders))
		for i := range orders {
			item := orders[i]
			list[i] = &device.DeviceOrder{
				Iccid:                  item.Iccid,
				State:                  code.DownloadState[item.State],
				CreatedTime:            k.W3cDate(item.CreateTime, 0),
				LastModified:           k.W3cDate(item.UpdateTime, 0),
				MnoId:                  int32(item.OperatorId),
				ProfileClass:           code.Clazz[item.ProfileClazz],
				AssociatedEid:          item.Eid,
				ActivationCode:         code.BuildAc(item, operator.SmdpAddress),
				SmdpOid:                dh.param.SmdpOid,
				SmdpAddress:            item.SmdpAddress,
				MaxDownloadTime:        item.DownloadOrderAttemptCount,    //返回剩余最大次数
				MaxCCTime:              item.ConfirmationCodeAttemptCount, //返回剩余最大次数
				MaxDownloadSuccessTime: item.SuccessDownloadCount,
				NotificationEvent:      code.ParseNotificationEvents(item.NotificationConfiguration),
			}
			encOriginRules := asn.DerBase64Value(item.PolicyRules)
			originRules := dh.km.DecryptByDataCryptKey(encOriginRules, operatorKek.DataKeyName)
			list[i].Ppr = asn.BytesToBase64(dh.km.Encrypt(operatorKek.Category, originRules, operatorKek.TransportKeyName))
			profileType := profileTypeMap[item.ProfileType]
			if profileType == nil {
				profileType = dh.pm.FindProfileTypeByName(operator.OperatorId, item.ProfileType)
				if profileType != nil {
					profileTypeMap[item.ProfileType] = profileType
					list[i].ProfileTypeName = profileType.Name
					list[i].ReDownloadRule = strconv.Itoa(profileType.ReDownloadPolicy)

				}
			}
			profile := profileMap[item.ProfileId]
			if profile == nil {
				profile = dh.pm.FindProfileByProfileId(item.ProfileId)
				if profile != nil {
					profileMap[item.ProfileId] = profile
					list[i].MccMnc = profile.Mcc + profile.Mnc
				}
			}
		}
		deviceInfoResponse.Orders = list
	}
	response.WriteJson(deviceInfoResponse)
}

func (dh *DeviceHandler) getMccMnc(imsi string, mncLength int) string {
	if len(imsi) < 3+mncLength {
		return ""
	}
	return imsi[0 : 3+mncLength]
}
