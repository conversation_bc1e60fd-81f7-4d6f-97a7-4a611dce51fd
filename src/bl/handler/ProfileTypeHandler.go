package handler

import (
	"esim.common/src/bl"
	"esim.common/src/bo"
	"esim.common/src/code"
	"esim.common/src/entity/dp/mno"
	sys2 "esim.common/src/entity/dp/sys"
	"esim.common/src/entity/hub/sys"
	"esim.es2/src/bl/manager"
	"esim.es2/src/bo/private"
	"esim.es2/src/bo/private/profileType"
	"k"
	"k/app"
	"k/asn"
	"k/logger"
	"strings"
)

type ProfileTypeHandler struct {
	A   *app.App
	km  *bl.KmsManager
	pm  *manager.ProfileManager
	ptm *manager.ProfileTypeManager
	dom *manager.DownloadOrderManager
	utm *manager.UppTempManager
}

func (pth *ProfileTypeHandler) Init() {
	pth.km = app.GetManager[*bl.KmsManager](pth.A)
	pth.pm = app.GetManager[*manager.ProfileManager](pth.A)
	pth.ptm = app.GetManager[*manager.ProfileTypeManager](pth.A)
	pth.dom = app.GetManager[*manager.DownloadOrderManager](pth.A)
	pth.utm = app.GetManager[*manager.UppTempManager](pth.A)
}

const defaultMinute = 360000000

func (pth *ProfileTypeHandler) ListProfileTypes(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "ListProfileTypesException")
		}
	}()
	req := k.FromJson[*bo.RspRequest](string(request.GetBody()))
	req.Header.Check(response)
	listResponse := profileType.NewProfileTypeListResponse()
	ticket := request.FromInstance.ReadTicket(request)
	operator := k.FromJson[*sys.Operator](ticket.Key)
	types := pth.ptm.SelectByState(operator.OperatorId, code.Enabled)
	if types != nil || len(types) > 0 {
		list := make([]*profileType.Item, len(types))
		for i, item := range types {
			list[i] = &profileType.Item{
				ProfileTypeName: item.Name,
				DefaultValue:    item.IsDefault == 1,
			}
		}
		listResponse.ProfileTypeList = list
	}
	response.WriteJson(listResponse)
}

func (pth *ProfileTypeHandler) GetProfileTypeInfo(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "GetProfileTypeInfoException")
		}
	}()
	getRequest := k.FromJson[*profileType.GetProfileTypeInfoRequest](string(request.GetBody()))
	getRequest.Header.Check(response)

	ticket := request.FromInstance.ReadTicket(request)
	res := profileType.NewProfileTypeInfoResponse()
	operator := k.FromJson[*sys.Operator](ticket.Key)
	entity := pth.ptm.SelectByType(operator.OperatorId, getRequest.ProfileTypeName)
	operatorKek := private.GetOperatorKek(operator)
	if entity == nil {
		res.ProfileTypeNotExistsResponse(response, getRequest.ProfileTypeName)
		return
	}
	count := pth.dom.GetDownloadSuccessCount(entity.TypeId, operator.OperatorId)
	res.ProfileTypeName = entity.Name
	res.DefaultType = entity.IsDefault == 1
	encOriginRules := asn.DerBase64Value(entity.PolicyRules)
	originRules := pth.km.DecryptByDataCryptKey(encOriginRules, operatorKek.DataKeyName)
	res.Ppr = asn.BytesToBase64(pth.km.Encrypt(operatorKek.Category, originRules, operatorKek.TransportKeyName))
	res.ProfileClass = strings.ToUpper(code.Clazz[entity.Clazz])
	res.MaxDownloadTime = entity.DownloadOrderAttemptMaxCount
	res.MaxCCTime = entity.DownloadOrderCcAttemptMaxCount
	res.MaxDownloadSuccessTime = count
	res.NotificationEvent = code.ParseNotificationEvents(entity.NotificationConfiguration)
	res.CreatedTime = k.W3cDate(entity.UpdateTime, 0)
	res.LastModified = k.W3cDate(entity.UpdateTime, 0)
	res.Prp = entity.ReDownloadPolicy
	res.Operator = entity.ServiceProviderName
	response.WriteJson(res)
}

func (pth *ProfileTypeHandler) AddProfileType(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "AddProfileTypeException")
		}
	}()
	addRequest := k.FromJson[*profileType.AddProfileTypeRequest](string(request.GetBody()))
	addRequest.Header.Check(response)

	ticket := request.FromInstance.ReadTicket(request)
	res := profileType.NewProfileTypeAddResponse()
	operator := k.FromJson[*sys.Operator](ticket.Key)
	old := pth.ptm.SelectByType(operator.OperatorId, addRequest.Name)
	uppTemp := pth.utm.SelectByOperatorAndName(operator.OperatorId, addRequest.ProfileTemplate.Name)
	operatorKek := private.GetOperatorKek(operator)

	if old != nil || uppTemp != nil {
		res.ProfileTypeAlreadyExistsResponse(response, addRequest.Name)
		return
	}
	currentProfileType := &mno.ProfileType{}
	currentTemp := &sys2.UppTemplate{
		TenantId:   operator.TenantId,
		CreateBy:   pth.A.Instance.Id,
		CreateName: pth.A.Instance.Context,
	}
	template := addRequest.ProfileTemplate
	upp := pth.BuildUppByBasic(template, operator.OperatorId)
	pth.fillUppTemplate(currentTemp, template.Name, operator, template.Brand, template.Model, template.Tac, upp)
	da := pth.A.DA()
	da.Begin()
	defer da.Rollback()
	pth.utm.InsertUppTemplate(da, currentTemp)
	pth.fillProfileTypeByParams(currentProfileType, addRequest, operator, currentTemp.UppId, operatorKek)
	pth.ptm.InsertProfileType(da, currentProfileType)
	da.Commit()
	res.ProfileTypeName = currentProfileType.Name
	response.WriteJson(res)
}

func (pth *ProfileTypeHandler) UpdateProfileType(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "UpdateProfileTypeException")
		}
	}()
	updRequest := k.FromJson[*profileType.UpdateProfileTypeRequest](string(request.GetBody()))
	updRequest.Header.Check(response)

	ticket := request.FromInstance.ReadTicket(request)
	res := profileType.NewProfileTypeUpdateResponse()
	operator := k.FromJson[*sys.Operator](ticket.Key)
	old := pth.ptm.SelectByType(operator.OperatorId, updRequest.Name)
	if old == nil {
		res.ProfileTypeNotExistsResponse(response, updRequest.Name)
		return
	}
	old.DownloadOrderAttemptMaxCount = updRequest.MaxDownloadAttempts
	old.DownloadOrderCcAttemptMaxCount = updRequest.MaxCCAttempts
	old.DownloadOrderMaxSuccessCount = updRequest.MaxDownloadSuccessTimes
	old.MncLength = updRequest.MncLen
	if len(strings.TrimSpace(updRequest.ServiceProviderName)) > 0 {
		old.ServiceProviderName = updRequest.ServiceProviderName
	}
	old.NotificationConfiguration = updRequest.GetNotificationString()
	old.ReDownloadPolicy = updRequest.Prp
	old.UpdateBy = pth.A.Instance.Id
	old.UpdateName = pth.A.Instance.Context
	da := pth.A.DA()
	da.Begin()
	defer da.Rollback()
	pth.ptm.Update(da, old)
	da.Commit()
	response.WriteJson(res)
}

func (pth *ProfileTypeHandler) AddProfileTypeByUPP(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "AddProfileTypeByUPPException")
		}
	}()
	addRequest := k.FromJson[*profileType.AddProfileTypeByUppRequest](string(request.GetBody()))
	addRequest.Header.Check(response)

	ticket := request.FromInstance.ReadTicket(request)
	res := profileType.NewProfileTypeAddResponse()
	operator := k.FromJson[*sys.Operator](ticket.Key)
	old := pth.ptm.SelectByType(operator.OperatorId, addRequest.Name)
	uppOld := pth.utm.SelectByOperatorAndName(operator.OperatorId, addRequest.UppProfileTemplate.Name)
	operatorKek := private.GetOperatorKek(operator)
	if old != nil || uppOld != nil {
		res.ProfileTypeAlreadyExistsResponse(response, addRequest.Name)
		return
	}
	template := addRequest.UppProfileTemplate
	if template == nil || len(strings.TrimSpace(template.UppContent)) == 0 {
		res.ProfileTypeUppContentMissResponse(response, addRequest.Name)
		return
	}
	currentType := &mno.ProfileType{}
	currentTemp := &sys2.UppTemplate{
		TenantId:   operator.TenantId,
		CreateBy:   pth.A.Instance.Id,
		CreateName: pth.A.Instance.Context,
	}
	pth.fillUppTemplate(currentTemp, template.Name, operator, template.Brand, template.Model, template.Tac, template.UppContent)
	da := pth.A.DA()
	da.Begin()
	defer da.Rollback()
	pth.utm.InsertUppTemplate(da, currentTemp)
	pth.fillProfileTypeByUpp(currentType, addRequest, operator, currentTemp.UppId, operatorKek)
	pth.ptm.InsertProfileType(da, currentType)
	da.Commit()
	res.ProfileTypeName = currentType.Name
	response.WriteJson(res)
}

func (pth *ProfileTypeHandler) UpdateProfileTemplateByUPP(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "UpdateProfileTemplateByUPPException")
		}
	}()
	updRequest := k.FromJson[*profileType.UpdateProfileTypeByUppRequest](string(request.GetBody()))
	updRequest.Header.Check(response)
	ticket := request.FromInstance.ReadTicket(request)
	res := profileType.NewProfileTypeUpdateResponse()
	operator := k.FromJson[*sys.Operator](ticket.Key)
	oldType := pth.ptm.SelectByType(operator.OperatorId, updRequest.ProfileTypeName)
	uppOld := pth.utm.SelectByOperatorAndName(operator.OperatorId, updRequest.UppProfileTemplate.Name)
	if oldType == nil {
		res.ProfileTypeNotExistsResponse(response, updRequest.ProfileTypeName)
		return
	}
	if uppOld == nil {
		res.ProfileTypeNotExistsResponse(response, updRequest.UppProfileTemplate.Name)
		return
	}
	template := updRequest.UppProfileTemplate
	pth.fillUppTemplate(uppOld, template.Name, operator, template.Brand, template.Model, template.Tac, template.UppContent)
	gid1 := updRequest.UppProfileTemplate.Gid1
	gid2 := updRequest.UppProfileTemplate.Gid2
	da := pth.A.DA()
	da.Begin()
	defer da.Rollback()
	pth.utm.Update(da, uppOld)
	if oldType.Gid1 != gid1 || oldType.Gid2 != gid2 {
		if len(strings.TrimSpace(gid1)) == 0 {
			oldType.Gid1 = "0"
		} else {
			oldType.Gid1 = gid1
		}
		if len(strings.TrimSpace(gid2)) == 0 {
			oldType.Gid2 = "0"
		} else {
			oldType.Gid2 = gid2
		}
		oldType.UpdateBy = pth.A.Instance.Id
		oldType.UpdateName = pth.A.Instance.Context
		pth.ptm.Update(da, oldType)
	}
	da.Commit()
	res.ProfileTypeName = oldType.ProfileName
	res.ProfileTemplateName = uppOld.Descripton
	response.WriteJson(res)
}

func (pth *ProfileTypeHandler) UpdateProfileTemplate(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "UpdateProfileTemplateException")
		}
	}()
	updRequest := k.FromJson[*profileType.UpdateProfileTemplateRequest](string(request.GetBody()))
	updRequest.Header.Check(response)
	ticket := request.FromInstance.ReadTicket(request)
	res := profileType.NewProfileTypeUpdateResponse()
	operator := k.FromJson[*sys.Operator](ticket.Key)
	oldType := pth.ptm.SelectByType(operator.OperatorId, updRequest.ProfileTypeName)
	if oldType == nil {
		res.ProfileTypeNotExistsResponse(response, updRequest.ProfileTypeName)
		return
	}
	uppOld := pth.utm.SelectByPrimaryKey(oldType.UppId)
	if uppOld == nil {
		res.ProfileTypeNotExistsResponse(response, updRequest.ProfileTypeName)
		return
	}
	upp := pth.buildUppByBasicForRequest(updRequest, operator)
	uppOld.Upp = upp
	uppOld.UpdateBy = pth.A.Instance.Id
	uppOld.UpdateName = pth.A.Instance.Context
	da := pth.A.DA()
	da.Begin()
	defer da.Rollback()
	pth.utm.Update(da, uppOld)
	da.Commit()
	res.ProfileTypeName = oldType.Name
	res.ProfileTemplateName = uppOld.Descripton
	response.WriteJson(res)
}

func (pth *ProfileTypeHandler) buildUppByBasicForRequest(template *profileType.UpdateProfileTemplateRequest, operator *sys.Operator) string {
	defaultTemp := pth.utm.SelectByOperator(operator.OperatorId)
	parser := asn.NewTlvByHexTag("30", asn.DerBase64Value(defaultTemp.Upp))
	manager.FillProfileHeaders(parser, template.ProfileTypeName, "")
	manager.FillPEMF(parser, "", template.Pin1, template.Pin2, template.Adm1)
	manager.FillPuk(parser, template.Puk1, template.Puk2)
	manager.FillPin(parser, template.Pin1, template.Pin2, template.Adm1)
	manager.FillGID(parser, template.Gid1, template.Gid2)
	if template.AppletTemplate1 != nil {
		manager.FillLoadBlock(parser, template.AppletTemplate1.LoadPackageAID, template.AppletTemplate1.LoadBlockObject)
		manager.FillInstanceList(parser, template.AppletTemplate1.InstanceParam, template.AppletTemplate1.C9)
	}

	//	支持网络选择
	manager.FillHPlMNRatList(parser, template.HplmnRatList)
	manager.FillFPlMNList(parser, template.FplmnList)
	manager.FillOPlMNwACT(parser, template.OplmnRatList)
	manager.FillPlMNRatList(parser, template.PlmnRatList)
	return asn.BytesToBase64(parser.GetValue())
}

func (pth *ProfileTypeHandler) fillProfileTypeByParams(entity *mno.ProfileType, request *profileType.AddProfileTypeRequest, operator *sys.Operator, uppId int64, operatorKek *private.OperatorKek) {
	if request.IsDefault != 1 || pth.pm.GetDefaultProfileType(operator.OperatorId) != nil {
		entity.IsDefault = 0
	} else {
		entity.IsDefault = 1
	}
	entity.OperatorId = operator.OperatorId
	entity.UppId = uppId
	entity.Name = request.Name
	entity.ProfileName = entity.Name
	entity.State = code.Enabled
	entity.DownloadOrderAttemptMaxCount = request.MaxDownloadAttempts
	entity.DownloadOrderCcAttemptMaxCount = request.MaxCCAttempts
	entity.DownloadOrderExpiredMinute = defaultMinute
	entity.DownloadOrderMaxSuccessCount = request.MaxDownloadSuccessTimes
	entity.PolicyRules = pth.GetEncryptPolicyRules(request.ProfilePolicyRules, operatorKek)
	entity.Clazz = request.ProfileClass
	entity.ReDownloadPolicy = request.ReDownloadPolicy
	if request.DeviceChangeAttemptMaxCount == 0 {
		entity.DeviceChangeAttemptMaxCount = 10
	} else {
		entity.DeviceChangeAttemptMaxCount = request.DeviceChangeAttemptMaxCount
	}
	entity.DeviceChangeSupportRecovery = request.DeviceChangeSupportRecovery
	if request.DeviceChangeRecoveryValidityPeriodMinute == 0 {
		entity.DeviceChangeRecoveryValidityPeriodMinute = defaultMinute
	} else {
		entity.DeviceChangeRecoveryValidityPeriodMinute = request.DeviceChangeRecoveryValidityPeriodMinute
	}
	entity.SupportEuiccFieldTest = request.SupportEuiccFieldTest
	entity.SupportDeviceChange = request.SupportDeviceChange
	entity.IconType = request.IconType
	if len(strings.TrimSpace(request.Icon)) == 0 {
		entity.Icon = "0"
	} else {
		entity.Icon = request.Icon
	}
	entity.MncLength = request.MncLen
	entity.Gid1 = request.ProfileTemplate.Gid1
	entity.Gid2 = request.ProfileTemplate.Gid2
	entity.ServiceProviderMessage = request.ServiceProviderMessage
	entity.ServiceProviderName = request.ServiceProviderName
	entity.Description = ""
	entity.NotificationConfiguration = request.GetNotificationString()
	entity.TenantId = operator.TenantId
	entity.CreateBy = pth.A.Instance.Id
	entity.CreateName = pth.A.Instance.Context
	entity.UpdateBy = pth.A.Instance.Id
	entity.UpdateName = pth.A.Instance.Context
}

func (pth *ProfileTypeHandler) fillUppTemplate(uppTemplate *sys2.UppTemplate, tempName string, operator *sys.Operator, brand string, model string, tac string, upp string) {
	uppTemplate.Descripton = tempName
	uppTemplate.OperatorId = operator.OperatorId
	uppTemplate.Brand = brand
	uppTemplate.Model = model
	uppTemplate.Tac = tac
	uppTemplate.Upp = upp
	uppTemplate.UpdateBy = pth.A.Instance.Id
	uppTemplate.UpdateName = pth.A.Instance.Context
}

func (pth *ProfileTypeHandler) BuildUppByBasic(template *manager.ProfileTemplate, operatorId int64) string {
	defaultTemp := pth.utm.SelectByOperator(operatorId)
	parser := asn.NewTlvByHexTag("30", asn.DerBase64Value(defaultTemp.Upp))
	manager.FillProfileHeaders(parser, template.Name, "")
	manager.FillPEMF(parser, "", template.Pin1, template.Pin2, template.Adm1)
	manager.FillPuk(parser, template.Puk1, template.Puk2)
	manager.FillPin(parser, template.Pin1, template.Pin2, template.Adm1)
	manager.FillGID(parser, template.Gid1, template.Gid2)
	if template.AppletTemplate1 != nil {
		manager.FillC9(parser, template.AppletTemplate1.C9)
		manager.FillLoadBlock(parser, template.AppletTemplate1.LoadPackageAID, template.AppletTemplate1.LoadBlockObject)
	}

	//	支持网络选择
	manager.FillHPlMNRatList(parser, template.HplmnRatList)
	manager.FillFPlMNList(parser, template.FplmnList)
	manager.FillOPlMNwACT(parser, template.OplmnRatList)
	manager.FillPlMNRatList(parser, template.PlmnRatList)
	return asn.BytesToBase64(parser.GetValue())
}

func (pth *ProfileTypeHandler) GetEncryptPolicyRules(rules string, operatorKek *private.OperatorKek) string {
	return asn.BytesToBase64(pth.km.EncryptByDataCryptKey(pth.km.Decrypt(operatorKek.Category, asn.DerBase64Value(rules), operatorKek.TransportKeyName), operatorKek.DataKeyName))
}

func (pth *ProfileTypeHandler) fillProfileTypeByUpp(entity *mno.ProfileType, request *profileType.AddProfileTypeByUppRequest, operator *sys.Operator, uppId int64, operatorKek *private.OperatorKek) {
	if request.IsDefault != 1 || pth.pm.GetDefaultProfileType(operator.OperatorId) != nil {
		entity.IsDefault = 0
	} else {
		entity.IsDefault = 1
	}
	entity.OperatorId = operator.OperatorId
	entity.UppId = uppId
	entity.Name = request.Name
	entity.ProfileName = entity.Name
	entity.State = code.Enabled
	entity.DownloadOrderAttemptMaxCount = request.MaxDownloadAttempts
	entity.DownloadOrderCcAttemptMaxCount = request.MaxCCAttempts
	entity.DownloadOrderExpiredMinute = defaultMinute
	entity.DownloadOrderMaxSuccessCount = request.MaxDownloadSuccessTimes
	entity.PolicyRules = pth.GetEncryptPolicyRules(request.ProfilePolicyRules, operatorKek)
	entity.Clazz = request.ProfileClass
	entity.ReDownloadPolicy = request.ReDownloadPolicy
	if request.DeviceChangeAttemptMaxCount == 0 {
		entity.DeviceChangeAttemptMaxCount = 10
	} else {
		entity.DeviceChangeAttemptMaxCount = request.DeviceChangeAttemptMaxCount
	}
	entity.DeviceChangeSupportRecovery = request.DeviceChangeSupportRecovery
	if request.DeviceChangeRecoveryValidityPeriodMinute == 0 {
		entity.DeviceChangeRecoveryValidityPeriodMinute = defaultMinute
	} else {
		entity.DeviceChangeRecoveryValidityPeriodMinute = request.DeviceChangeRecoveryValidityPeriodMinute
	}
	entity.SupportEuiccFieldTest = request.SupportEuiccFieldTest
	entity.SupportDeviceChange = request.SupportDeviceChange
	entity.IconType = request.IconType
	if len(strings.TrimSpace(request.Icon)) == 0 {
		entity.Icon = "0"
	} else {
		entity.Icon = request.Icon
	}
	entity.MncLength = request.MncLen
	entity.Gid1 = request.UppProfileTemplate.Gid1
	entity.Gid2 = request.UppProfileTemplate.Gid2
	entity.ServiceProviderMessage = request.ServiceProviderMessage
	entity.ServiceProviderName = request.ServiceProviderName
	entity.Description = ""
	entity.NotificationConfiguration = request.GetNotificationString()
	entity.TenantId = operator.TenantId
	entity.CreateBy = pth.A.Instance.Id
	entity.CreateName = pth.A.Instance.Context
	entity.UpdateBy = pth.A.Instance.Id
	entity.UpdateName = pth.A.Instance.Context
}
