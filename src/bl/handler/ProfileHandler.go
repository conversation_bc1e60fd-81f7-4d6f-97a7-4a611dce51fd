package handler

import (
	"esim.common/src/bl"
	"esim.common/src/bo"
	"esim.common/src/code"
	"esim.common/src/entity/dp/mno"
	"esim.common/src/entity/dp/rsp"
	"esim.common/src/entity/hub/sys"
	"esim.es2/src"
	"esim.es2/src/bl/manager"
	"esim.es2/src/bo/private"
	"esim.es2/src/bo/private/profile"
	"k"
	"k/app"
	"k/asn"
	"k/logger"
	"k/security"
	"regexp"
	"strconv"
	"strings"
)

type ProfileHandler struct {
	A     *app.App
	param *src.Es2Param
	km    *bl.KmsManager
	pm    *manager.ProfileManager
	ptm   *manager.ProfileTypeManager
	utm   *manager.UppTempManager
	pum   *manager.ProfileUppManager
	lnm   *manager.LpaNotificationManager
	sm    *manager.SessionManager
	dom   *manager.DownloadOrderManager
}

func (ph *ProfileHandler) Init() {
	ph.param = app.GetClientParam[*src.Es2Param](ph.A)
	ph.km = app.GetManager[*bl.KmsManager](ph.A)
	ph.pm = app.GetManager[*manager.ProfileManager](ph.A)
	ph.ptm = app.GetManager[*manager.ProfileTypeManager](ph.A)
	ph.utm = app.GetManager[*manager.UppTempManager](ph.A)
	ph.pum = app.GetManager[*manager.ProfileUppManager](ph.A)
	ph.lnm = app.GetManager[*manager.LpaNotificationManager](ph.A)
	ph.sm = app.GetManager[*manager.SessionManager](ph.A)
	ph.dom = app.GetManager[*manager.DownloadOrderManager](ph.A)
}

func (ph *ProfileHandler) AddProfileByUpp(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "AddProfileByUppException")
		}
	}()
	addRequest := k.FromJson[*profile.AddProfileByUppRequest](string(request.GetBody()))
	addRequest.Header.Check(response)
	ticket := request.FromInstance.ReadTicket(request)
	operator := k.FromJson[*sys.Operator](ticket.Key)
	baseProfileResponse := profile.NewBaseProfileResponse()
	operatorKek := private.GetOperatorKek(operator)
	aesKey := ph.km.Decrypt(operatorKek.Category, asn.DerBase64Value(addRequest.EncAesKey), operatorKek.TransportKeyName)
	iv := make([]byte, 16)
	bUpp, err := security.AesDecryptByCBCPkcs7(aesKey, iv, asn.DerBase64Value(addRequest.EncUpp))
	if err != nil {
		baseProfileResponse.DecryptUppFormatError(response, addRequest.Iccid)
		return
	}
	tlvUpp := asn.NewTlvByHexTag("30", bUpp)
	defaultType := ph.pm.GetDefaultProfileType(operator.OperatorId)
	if len(strings.TrimSpace(addRequest.ProfileType)) == 0 && defaultType != nil {
		addRequest.ProfileType = defaultType.Name
	}
	iccid := manager.GetIccid(tlvUpp)
	if addRequest.Iccid != iccid {
		addRequest.Iccid = iccid
	}
	oldProfile := ph.pm.FindProfileByIccid(operator.OperatorId, addRequest.Iccid)
	if oldProfile != nil {
		baseProfileResponse.ProfileAlreadyExist(response, addRequest.Iccid)
		return
	}
	profileType := ph.ptm.SelectByType(operator.OperatorId, addRequest.ProfileType)
	if profileType == nil {
		baseProfileResponse.ProfileTypeNotExist(response, addRequest.Iccid)
		return
	}
	//填充默认或入参的profile type
	manager.FillProfileHeaders(tlvUpp, addRequest.ProfileType, iccid)
	var bImsi []byte
	if addRequest.Imsi == "" {
		bImsi = manager.GetImsi(tlvUpp)
	} else {
		bImsi, _ = security.AesDecryptByCBCPkcs7(aesKey, iv, asn.DerHexValue(addRequest.Imsi))
	}
	bKi := manager.GetKi(tlvUpp)
	bOpc := manager.GetOpc(tlvUpp)
	logger.TL("Imsi", string(bImsi))
	if len(bImsi) == 0 || len(bKi) == 0 || len(bOpc) == 0 {
		baseProfileResponse.DecryptUppFormatError(response, addRequest.Iccid)
		return
	}
	if len(strings.TrimSpace(manager.GetShortName(tlvUpp))) == 0 {
		//	设置默认SPN-即ProfileType
		manager.FillPeUsimImsi(tlvUpp, "", "", nil, profileType.ServiceProviderName)
	}
	encImsi := asn.BytesToHex(ph.km.EncryptByDataCryptKey(bImsi, operatorKek.DataKeyName))
	encKi := asn.BytesToHex(ph.km.EncryptByDataCryptKey(bKi, operatorKek.DataKeyName))
	encOpc := asn.BytesToHex(ph.km.EncryptByDataCryptKey(bOpc, operatorKek.DataKeyName))
	profileEntity := &mno.Profile{
		State:               code.ProfileStateAvailable,
		ProfileType:         addRequest.ProfileType,
		TypeId:              profileType.TypeId,
		Iccid:               iccid,
		RequestIccid:        addRequest.Iccid,
		EncKekId:            operatorKek.KekId,
		OperatorId:          operator.OperatorId,
		TenantId:            operator.TenantId,
		PppModel:            profileType.PppModel,
		Clazz:               profileType.Clazz,
		Imsi:                encImsi,
		Ki:                  encKi,
		Opc:                 encOpc,
		UpdateBy:            ph.A.Instance.Id,
		ShortDescription:    profileType.ProfileName,
		ServiceProviderName: profileType.ServiceProviderName,
	}

	msisdn := manager.GetMsisdn(tlvUpp)
	if len(msisdn) > 0 {
		profileEntity.Msisdn = msisdn
	}
	manager.CreateProfileOwner(profileEntity, profileType, bImsi)
	da := ph.A.DA()
	da.Begin()
	defer da.Rollback()
	ph.pm.InsertProfile(da, profileEntity)
	ph.pm.InsertProfileUppByProfile(da, profileEntity, asn.BytesToBase64(ph.km.EncryptByDataCryptKey(tlvUpp.GetValue(), operatorKek.DataKeyName)))
	ph.pm.InitProfileLifeCycle(da, profileEntity, operator, "", "", 0)
	da.Commit()
	baseProfileResponse.Iccid = profileEntity.Iccid
	response.WriteJson(baseProfileResponse)
}

func (ph *ProfileHandler) AddProfileByMeta(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "AddProfileByMetaException")
		}
	}()
	baseProfileResponse := profile.NewBaseProfileResponse()
	addRequest := k.FromJson[*profile.AddProfileByMetaRequest](string(request.GetBody()))
	addRequest.Header.Check(response)
	ph.pm.DoAddProfileByMeta(request, response, addRequest, baseProfileResponse)
	response.WriteJson(baseProfileResponse)
}

func (ph *ProfileHandler) AddProfileByUppParams(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "AddProfileByUppParamsException")
		}
	}()
	addRequest := k.FromJson[*profile.AddProfileByUppParamsRequest](string(request.GetBody()))
	addRequest.Header.Check(response)
	ticket := request.FromInstance.ReadTicket(request)
	operator := k.FromJson[*sys.Operator](ticket.Key)
	baseProfileResponse := profile.NewBaseProfileResponse()
	operatorKek := private.GetOperatorKek(operator)
	aesKey := ph.km.Decrypt(operatorKek.Category, asn.DerBase64Value(addRequest.EncAesKey), operatorKek.TransportKeyName)
	paramMap := make(map[string]string)
	for _, item := range addRequest.ParamKeyValueList {
		if item != nil {
			paramMap[strings.ToUpper(item.ParamKey)] = item.ParamValue
		}
	}
	rIccid := paramMap["ICCID"]
	rImsi := paramMap["IMSI"]
	rKi := paramMap["KI"]
	rOpc := paramMap["OPC"]
	iccid := manager.CheckIccid(rIccid)
	if len(strings.TrimSpace(iccid)) == 0 {
		baseProfileResponse.IccidFormatError(response, iccid)
		return
	}
	profileOld := ph.pm.FindProfileByIccid(operator.OperatorId, iccid)
	if profileOld != nil {
		baseProfileResponse.ProfileAlreadyExist(response, iccid)
		return
	}
	requestProfileType := addRequest.ProfileType
	var profileType *mno.ProfileType
	if len(strings.TrimSpace(requestProfileType)) == 0 {
		profileType = ph.pm.GetDefaultProfileType(operator.OperatorId)
	} else {
		profileType = ph.ptm.SelectByType(operator.OperatorId, requestProfileType)
	}
	if profileType == nil {
		baseProfileResponse.ProfileTypeNotExist(response, requestProfileType)
		return
	}
	iv := make([]byte, 16)
	bImsi, err := security.AesDecryptByCBCPkcs7(aesKey, iv, asn.DerHexValue(rImsi))
	bKi, err1 := security.AesDecryptByCBCPkcs7(aesKey, iv, asn.DerHexValue(rKi))
	bOpc, err2 := security.AesDecryptByCBCPkcs7(aesKey, iv, asn.DerHexValue(rOpc))

	if err != nil {
		logger.E("Imsi", err)
		baseProfileResponse.DecryptImsiKiOpcError(response, iccid)
		return
	}
	if err1 != nil {
		logger.E("Ki", err1)
		baseProfileResponse.DecryptImsiKiOpcError(response, iccid)
		return
	}
	if err2 != nil {
		logger.E("Opc", err2)
		baseProfileResponse.DecryptImsiKiOpcError(response, iccid)
		return
	}
	encImsi := asn.BytesToHex(ph.km.EncryptByDataCryptKey(bImsi, operatorKek.DataKeyName))
	encKi := asn.BytesToHex(ph.km.EncryptByDataCryptKey(bKi, operatorKek.DataKeyName))
	encOpc := asn.BytesToHex(ph.km.EncryptByDataCryptKey(bOpc, operatorKek.DataKeyName))

	profileEntity := &mno.Profile{
		State:               code.ProfileStateAvailable,
		ProfileType:         profileType.Name,
		TypeId:              profileType.TypeId,
		Iccid:               iccid,
		RequestIccid:        rIccid, //Riken@20250410:保存原始的ICCID
		EncKekId:            operatorKek.KekId,
		OperatorId:          operator.OperatorId,
		ShortDescription:    profileType.ProfileName,
		TenantId:            operator.TenantId,
		PppModel:            profileType.PppModel,
		Clazz:               profileType.Clazz,
		Imsi:                encImsi,
		Ki:                  encKi,
		Opc:                 encOpc,
		UpdateBy:            ph.A.Instance.Id,
		ServiceProviderName: profileType.ServiceProviderName,
	}

	manager.FillProfileParams(profileEntity, paramMap)
	manager.CreateProfileOwner(profileEntity, profileType, bImsi)
	da := ph.A.DA()
	da.Begin()
	defer da.Rollback()
	ph.pm.InsertProfile(da, profileEntity)
	ph.pm.GenerateAndInsertProfileUpp(da, profileEntity, operatorKek, bImsi, bKi, bOpc, profileEntity.AppletRecycle)
	ph.pm.InitProfileLifeCycle(da, profileEntity, operator, "", "", 0)
	da.Commit()
	baseProfileResponse.Iccid = profileEntity.Iccid
	response.WriteJson(baseProfileResponse)
}

func (ph *ProfileHandler) UpdateByUppParams(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "UpdateByUppParamsException")
		}
	}()
	da := ph.A.DA()
	updateRequest := k.FromJson[*profile.UpdateProfileByUppParamsRequest](string(request.GetBody()))
	updateRequest.Header.Check(response)
	ticket := request.FromInstance.ReadTicket(request)
	operator := k.FromJson[*sys.Operator](ticket.Key)
	operatorKek := private.GetOperatorKek(operator)
	aesKey := ph.km.Decrypt(operatorKek.Category, asn.DerBase64Value(updateRequest.EncAesKey), operatorKek.TransportKeyName)
	paramMap := make(map[string]string)
	for _, item := range updateRequest.ParamKeyValueList {
		if item != nil {
			paramMap[strings.ToUpper(item.ParamKey)] = item.ParamValue
		}
	}
	rIccid := paramMap["ICCID"]
	rImsi := paramMap["IMSI"]
	rKi := paramMap["KI"]
	rOpc := paramMap["OPC"]
	iccid := manager.CheckIccid(rIccid)
	updResponse := profile.NewBaseProfileResponse()
	if len(strings.TrimSpace(iccid)) == 0 {
		updResponse.ProfileNotExist(response, iccid)
		return
	}
	profileOld := ph.pm.FindProfileByIccid(operator.OperatorId, iccid)
	if profileOld == nil {
		updResponse.ProfileNotExist(response, iccid)
		return
	}
	if profileOld.State == code.ProfileStateDownloaded || (profileOld.State == code.ProfileStateLocalDeleted) {
		updResponse.ProfileStateNotAvailable(response, iccid)
		return
	}
	pUpp := ph.pum.SelectByProfileId(profileOld.ProfileId)
	if pUpp == nil {
		updResponse.ProfileUppNotExist(response, profileOld.ProfileType)
		return
	}
	profileType := ph.ptm.SelectByTypeId(profileOld.TypeId)
	//profileType := ph.ptm.SelectByProfileId(ph.A.DA(), profileOld.ProfileId)
	if profileType == nil {
		updResponse.ProfileTypeNotExist(response, iccid)
		return
	}
	iv := make([]byte, 16)
	bImsi, err := security.AesDecryptByCBCPkcs7(aesKey, iv, asn.DerHexValue(rImsi))
	bKi, err1 := security.AesDecryptByCBCPkcs7(aesKey, iv, asn.DerHexValue(rKi))
	bOpc, err2 := security.AesDecryptByCBCPkcs7(aesKey, iv, asn.DerHexValue(rOpc))
	if (err != nil) || (err1 != nil) || (err2 != nil) {
		//imsi/ki/opc解密失败
		updResponse.DecryptImsiKiOpcError(response, iccid)
		return
	}
	encImsi := asn.BytesToHex(ph.km.EncryptByDataCryptKey(bImsi, operatorKek.DataKeyName))
	encKi := asn.BytesToHex(ph.km.EncryptByDataCryptKey(bKi, operatorKek.DataKeyName))
	encOpc := asn.BytesToHex(ph.km.EncryptByDataCryptKey(bOpc, operatorKek.DataKeyName))
	profileOld.ProfileType = profileType.Name
	profileOld.ServiceProviderName = profileType.ServiceProviderName
	profileOld.OperatorId = operator.OperatorId
	profileOld.ShortDescription = profileType.ProfileName
	profileOld.Imsi = encImsi
	profileOld.Ki = encKi
	profileOld.Opc = encOpc
	profileOld.ImpuList = ""
	manager.FillProfileParams(profileOld, paramMap)
	manager.CreateProfileOwner(profileOld, profileType, bImsi)
	da.Begin()
	defer da.Rollback()
	ph.pm.UpdateByPrimaryKey(da, profileOld)
	ph.pum.UpdateByCondition(da, profileOld, operatorKek, bImsi, bKi, bOpc, profileOld.AppletRecycle, pUpp)
	da.Commit()
	response.WriteJson(updResponse)
}

func (ph *ProfileHandler) DeleteProfile(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "DeleteProfileException")
		}
	}()
	delRequest := k.FromJson[*profile.DeleteProfileRequest](string(request.GetBody()))
	delRequest.Header.Check(response)
	ticket := request.FromInstance.ReadTicket(request)
	baseProfileResponse := profile.NewBaseProfileResponse()

	operator := k.FromJson[*sys.Operator](ticket.Key)
	iccid := delRequest.Iccid
	if (len(strings.TrimSpace(iccid)) == 0) || !regexp.MustCompile(manager.ICCIDRegex).MatchString(iccid) {
		baseProfileResponse.IccidFormatError(response, iccid)
		return
	}
	entity := ph.pm.FindProfileByIccid(operator.OperatorId, iccid)
	if entity == nil {
		baseProfileResponse.ProfileNotExist(response, iccid)
		return
	}
	state := entity.State
	if delRequest.Force == false && (state == code.ProfileStateAllocated ||
		state == code.ProfileStateConfirmed ||
		state == code.ProfileStateReleased ||
		state == code.ProfileStateDownloaded ||
		state == code.ProfileStateInstalled ||
		state == code.ProfileStateLocalDisabled ||
		state == code.ProfileStateLocalEnabled) {
		//	非强制删除条件，存在以上状态不允许删除
		baseProfileResponse.ProfileStateNotAvailable(response, iccid)
		return
	}
	da := ph.A.DA()
	da.Begin()
	defer da.Rollback()
	//remove profile
	ph.pm.DeleteByProfileId(da, entity.ProfileId)
	//remove lifeCycle
	ph.pm.DeleteLifeCycleByProfileId(da, entity.ProfileId)
	//FIXME 暂不删除，影响download汇总
	//remove profile upp
	ph.pum.DeleteByProfileId(da, entity.ProfileId)
	//remove lpa notification
	ph.lnm.DeleteByProfileId(da, entity.ProfileId)
	//remove session
	ph.sm.DeleteByProfileId(da, entity.ProfileId)
	da.Commit()
	baseProfileResponse.Iccid = entity.Iccid
	response.WriteJson(baseProfileResponse)
}

func (ph *ProfileHandler) GetProfileInfo(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "GetProfileInfoException")
		}
	}()
	getRequest := k.FromJson[*profile.GetProfileInfoRequest](string(request.GetBody()))
	getRequest.Header.Check(response)
	infoResponse := profile.NewProfileInfoResponse()
	ticket := request.FromInstance.ReadTicket(request)
	operator := k.FromJson[*sys.Operator](ticket.Key)
	operatorKek := private.GetOperatorKek(operator)
	iccid := getRequest.Iccid
	if (len(strings.TrimSpace(iccid)) == 0) || !regexp.MustCompile(manager.ICCIDRegex).MatchString(iccid) {
		infoResponse.ProfileNotExist(response, iccid)
		return
	}
	entity := ph.pm.FindProfileByIccid(operator.OperatorId, iccid)
	if entity == nil {
		infoResponse.ProfileNotExist(response, iccid)
		return
	}
	profileType := ph.ptm.SelectByTypeId(entity.TypeId)
	lastOrder := ph.dom.SelectLastDownload(iccid, entity.ProfileId, operator.OperatorId)
	//lastOrder := FindLastOrder(downloadOrders)
	infoResponse.Iccid = entity.Iccid
	if entity.State == code.ProfileStateInstalled {
		if entity.LocalState == code.ProfileStateLocalInit {
			infoResponse.State = strings.ToUpper(code.ProfileState[entity.State])
		} else {
			infoResponse.State = strings.ToUpper(code.ProfileLocalState[entity.LocalState])
		}
	} else {
		infoResponse.State = strings.ToUpper(code.ProfileState[entity.State])
	}
	infoResponse.CreatedTime = k.W3cDate(entity.CreateTime, 0)
	infoResponse.LastModified = k.W3cDate(entity.UpdateTime, 0)
	infoResponse.MnoId = int32(entity.OperatorId)
	infoResponse.ProfileTypeName = entity.ProfileType
	infoResponse.ProfileClass = strings.ToUpper(code.Clazz[entity.Clazz])
	infoResponse.Prp = profileType.ReDownloadPolicy
	infoResponse.MaxDownloadTime = profileType.DownloadOrderAttemptMaxCount
	if lastOrder != nil {
		infoResponse.MaxDownloadTime = lastOrder.DownloadOrderAttemptCount //返回当前还剩余多少次
		infoResponse.AssociatedEid = lastOrder.Eid
		infoResponse.SmdpAddress = lastOrder.SmdpAddress
		infoResponse.MaxCCTime = lastOrder.ConfirmationCodeAttemptCount
		infoResponse.ServiceProviderName = lastOrder.ServiceProviderName
		infoResponse.Operator = lastOrder.ServiceProviderName
		infoResponse.MaxDownloadSuccessTime = lastOrder.SuccessDownloadCount
		//len(downloadOrders)
		if entity.Clazz != 0 {
			infoResponse.ActivationCode = code.BuildAc(lastOrder, operator.SmdpAddress)
		}
	} else {
		infoResponse.MaxCCTime = profileType.DownloadOrderCcAttemptMaxCount
		infoResponse.ServiceProviderName = profileType.ProfileName
		if len(strings.TrimSpace(entity.ServiceProviderName)) > 0 {
			infoResponse.Operator = entity.ServiceProviderName
		} else {
			infoResponse.Operator = profileType.ServiceProviderName
		}
		infoResponse.MaxDownloadSuccessTime = 0
	}
	infoResponse.MccMnc = entity.Mcc + entity.Mnc
	infoResponse.SmdpOid = ph.param.SmdpOid
	//convert policy rules  Riken@20250608:原来的错误，必须先解密
	bPpr := ph.km.DecryptByDataCryptKey(asn.DerBase64Value(profileType.PolicyRules), operatorKek.DataKeyName)
	infoResponse.Ppr = asn.BytesToBase64(ph.km.Encrypt(operatorKek.Category, bPpr, operatorKek.TransportKeyName))
	//convert notificationEvent
	infoResponse.NotificationEvent = code.ParseNotificationEvents(profileType.NotificationConfiguration)
	infoResponse.Group = ""
	infoResponse.Msisdn = entity.Msisdn
	infoResponse.Pin1 = entity.Pin1
	infoResponse.Pin2 = entity.Pin2
	infoResponse.Puk1 = entity.Puk1
	infoResponse.Puk2 = entity.Puk2
	response.WriteJson(infoResponse)
}

func FindLastOrder(downloadOrders []*rsp.DownloadOrder) *rsp.DownloadOrder {
	if downloadOrders == nil || len(downloadOrders) == 0 {
		return nil
	}
	maxOrder := downloadOrders[0]
	for _, order := range downloadOrders[1:] {
		if order.CreateTime > maxOrder.CreateTime {
			maxOrder = order
		}
	}
	return maxOrder
}

func (ph *ProfileHandler) UpdateProfileParam(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "UpdateProfileParamException")
		}
	}()
	updRequest := k.FromJson[*profile.UpdateProfileByParamRequest](string(request.GetBody()))
	updRequest.Header.Check(response)
	ticket := request.FromInstance.ReadTicket(request)
	baseProfileResponse := profile.NewBaseProfileResponse()
	ph.DoUpdateProfileByParam(updRequest.Iccid, updRequest.EncAesKey, updRequest.ParamKeyValueList, response, ticket)
	response.WriteJson(baseProfileResponse)
}

func (ph *ProfileHandler) DoUpdateProfileByParam(iccid string, encAesKey string, paramKeyList []*private.ParamKeyValue, response *app.Response, ticket *app.InstanceTicket) *mno.Profile {
	baseProfileResponse := profile.NewBaseProfileResponse()
	operator := k.FromJson[*sys.Operator](ticket.Key)
	operatorKek := private.GetOperatorKek(operator)
	aesKey := ph.km.Decrypt(operatorKek.Category, asn.DerBase64Value(encAesKey), operatorKek.TransportKeyName)
	paramMap := make(map[string]string)
	for _, item := range paramKeyList {
		if item != nil {
			paramMap[strings.ToUpper(item.ParamKey)] = item.ParamValue
		}
	}
	rImsi := paramMap["IMSI"]
	rKi := paramMap["KI"]
	rOpc := paramMap["OPC"]
	profileOld := ph.pm.FindProfileByIccid(operator.OperatorId, iccid)
	if profileOld == nil {
		baseProfileResponse.ProfileNotExist(response, iccid)
		return nil
	}
	if profileOld.State == code.ProfileStateDownloaded || profileOld.State == code.ProfileStateLocalDeleted {
		baseProfileResponse.ProfileStateNotAvailable(response, profileOld.Iccid)
		return nil
	}
	pUpp := ph.pum.SelectByProfileId(profileOld.ProfileId)
	if pUpp == nil {
		baseProfileResponse.ProfileUppNotExist(response, profileOld.ProfileType)
		return nil
	}
	da := ph.A.DA()
	profileType := ph.ptm.SelectByTypeId(profileOld.TypeId)
	//profileType := ph.ptm.SelectByProfileId(da, profileOld.ProfileId)
	if profileType == nil {
		//profile type不存在
		baseProfileResponse.ProfileTypeNotExist(response, profileOld.ProfileType)
		return nil
	}
	iv := make([]byte, 16)
	bImsi, err := security.AesDecryptByCBCPkcs7(aesKey, iv, asn.DerHexValue(rImsi))
	bKi, err1 := security.AesDecryptByCBCPkcs7(aesKey, iv, asn.DerHexValue(rKi))
	bOpc, err2 := security.AesDecryptByCBCPkcs7(aesKey, iv, asn.DerHexValue(rOpc))
	if (err != nil) || (err1 != nil) || (err2 != nil) {
		//imsi/ki/opc解密失败
		baseProfileResponse.DecryptImsiKiOpcError(response, iccid)
		return nil
	}
	encImsi := asn.BytesToHex(ph.km.EncryptByDataCryptKey(bImsi, operatorKek.DataKeyName))
	encKi := asn.BytesToHex(ph.km.EncryptByDataCryptKey(bKi, operatorKek.DataKeyName))
	encOpc := asn.BytesToHex(ph.km.EncryptByDataCryptKey(bOpc, operatorKek.DataKeyName))
	profileOld.ProfileType = profileType.Name
	profileOld.ServiceProviderName = profileType.ServiceProviderName
	profileOld.OperatorId = operator.OperatorId
	profileOld.ShortDescription = profileType.ProfileName
	profileOld.Imsi = encImsi
	profileOld.Ki = encKi
	profileOld.Opc = encOpc
	profileOld.ImpuList = ""
	profileOld.UpdateBy = ph.A.Instance.Id

	//Riken@20250520:识别出MCC、MNC
	imsi := string(bImsi)
	profileOld.Mcc = imsi[0:3]
	profileOld.Mnc = imsi[3 : 3+profileType.MncLength]

	manager.FillProfileParams(profileOld, paramMap)
	manager.CreateProfileOwner(profileOld, profileType, bImsi)
	da.Begin()
	defer da.Rollback()
	ph.pm.UpdateByPrimaryKey(da, profileOld)
	ph.pum.UpdateByCondition(da, profileOld, operatorKek, bImsi, bKi, bOpc, profileOld.AppletRecycle, pUpp)
	da.Commit()
	return profileOld
}

func (ph *ProfileHandler) GetProfileStateStatistics(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "GetProfileStateStatisticsException")
		}
	}()
	getRequest := k.FromJson[*profile.GetProfileStateStatisticsRequest](string(request.GetBody()))
	getRequest.Header.Check(response)
	ticket := request.FromInstance.ReadTicket(request)
	operator := k.FromJson[*sys.Operator](ticket.Key)
	summaryResponse := profile.NewGetProfileStateStatisticsResponse()
	if len(strings.TrimSpace(getRequest.ProfileType)) > 0 {
		if ph.ptm.SelectByType(operator.OperatorId, getRequest.ProfileType) == nil {
			summaryResponse.ProfileTypeNotInvalid(response, getRequest.ProfileType)
			return
		}
		summaryResponse.ProfileType = getRequest.ProfileType
	}
	da := ph.A.DA()
	var totalSum = int64(0)
	profileStatistics := ph.pm.SummaryByProfileType(da, getRequest.ProfileType, getRequest.State, operator.OperatorId)
	for _, item := range profileStatistics {
		totalSum += item.Count
		s, err := strconv.Atoi(item.State)
		if err == nil {
			item.State = strings.ToUpper(code.ProfileState[s])
		}
	}
	if profileStatistics != nil && len(profileStatistics) > 0 {
		summaryResponse.Statistics = profileStatistics
	} else {
		summaryResponse.Statistics = make([]*profile.ProfileStateStatistics, 0)
	}
	summaryResponse.Total = &totalSum
	response.WriteJson(summaryResponse)
}

func (ph *ProfileHandler) ReGenerateUpp(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "ReGenerateUppException")
		}
	}()

	da := ph.A.DA()
	updateRequest := k.FromJson[*profile.ReGenerateUppRequest](string(request.GetBody()))
	updateRequest.Header.Check(response)
	ticket := request.FromInstance.ReadTicket(request)
	operator := k.FromJson[*sys.Operator](ticket.Key)
	operatorKek := private.GetOperatorKek(operator)
	iccid := manager.CheckIccid(updateRequest.Iccid)
	updResponse := profile.NewBaseProfileResponse()
	if len(strings.TrimSpace(iccid)) == 0 {
		updResponse.ProfileNotExist(response, iccid)
		return
	}

	profileOld := ph.pm.FindProfileByIccid(operator.OperatorId, iccid)
	if profileOld == nil {
		updResponse.ProfileNotExist(response, iccid)
		return
	}
	state := profileOld.State
	if strings.EqualFold(updateRequest.State, "AVAILABLE") ||
		strings.EqualFold(updateRequest.State, "ALLOCATED") ||
		strings.EqualFold(updateRequest.State, "LINKED") ||
		strings.EqualFold(updateRequest.State, "CONFIRMED") ||
		strings.EqualFold(updateRequest.State, "RELEASED") ||
		strings.EqualFold(updateRequest.State, "DOWNLOADED") ||
		strings.EqualFold(updateRequest.State, "INSTALLED") ||
		strings.EqualFold(updateRequest.State, "ERROR") ||
		strings.EqualFold(updateRequest.State, "UNAVAILABLE") {
		state = code.ProfileStateInt[strings.ToUpper(updateRequest.State)]
	}
	if operatorKek.KekId != profileOld.EncKekId {
		sysKek := ph.km.GetOperatorKek(profileOld.EncKekId, operator.OperatorId)
		operatorKek.KekId = sysKek.KekId
		operatorKek.Category = sysKek.Category
		operatorKek.DataKeyName = sysKek.DataEncryptKeyName
		operatorKek.TransportKeyName = sysKek.DataTransportKeyName
	}

	bImsi := ph.km.DecryptByDataCryptKey(asn.DerHexValue(profileOld.Imsi), operatorKek.DataKeyName)
	bKi := ph.km.DecryptByDataCryptKey(asn.DerHexValue(profileOld.Ki), operatorKek.DataKeyName)
	bOpc := ph.km.DecryptByDataCryptKey(asn.DerHexValue(profileOld.Opc), operatorKek.DataKeyName)
	da.Begin()
	defer da.Rollback()
	if profileOld.ProfileType != updateRequest.ProfileType || profileOld.State != state {
		profileType := ph.ptm.SelectByType(operator.OperatorId, updateRequest.ProfileType)
		if profileType == nil {
			updResponse.ProfileTypeNotExist(response, iccid)
			return
		}
		profileOld.State = state
		profileOld.ProfileType = profileType.Name
		profileOld.TypeId = profileType.TypeId
		profileOld.PppModel = profileType.PppModel
		profileOld.Clazz = profileType.Clazz
		profileOld.OperatorId = operator.OperatorId
		profileOld.ShortDescription = profileType.ProfileName
		profileOld.ServiceProviderName = profileType.ServiceProviderName
		manager.CreateProfileOwner(profileOld, profileType, bImsi)
		ph.pm.UpdateByPrimaryKey(da, profileOld)
	}
	ph.pum.DeleteByProfileId(da, profileOld.ProfileId)
	ph.pm.GenerateAndInsertProfileUpp(da, profileOld, operatorKek, bImsi, bKi, bOpc, profileOld.AppletRecycle)
	da.Commit()
	response.WriteJson(updResponse)
}
