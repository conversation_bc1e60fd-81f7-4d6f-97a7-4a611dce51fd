package handler

import (
	"esim.common/src/bo"
	"esim.common/src/code"
	"esim.common/src/entity/dp/mno"
	"esim.common/src/entity/dp/rsp"
	"esim.common/src/entity/hub/sys"
	"esim.es2/src"
	"esim.es2/src/bl/manager"
	"esim.es2/src/bo/gsma"
	"esim.es2/src/bo/private/order"
	"esim.es2/src/bo/private/profile"
	"k"
	"k/app"
	"k/asn"
	"k/logger"
	"k/security"
	"strings"
	"time"
)

type OrderHandler struct {
	A   *app.App
	om  *manager.OrderManager
	dom *manager.DownloadOrderManager
	pm  *manager.ProfileManager
	ptm *manager.ProfileTypeManager

	gam   *manager.GenerateACManager
	dm    *manager.DsManager
	pum   *manager.ProfileUppManager
	param *src.Es2Param
}

func (oh *OrderHandler) Init() {
	oh.om = app.GetManager[*manager.OrderManager](oh.A)
	oh.dom = app.GetManager[*manager.DownloadOrderManager](oh.A)
	oh.pm = app.GetManager[*manager.ProfileManager](oh.A)
	oh.ptm = app.GetManager[*manager.ProfileTypeManager](oh.A)
	oh.pum = app.GetManager[*manager.ProfileUppManager](oh.A)
	oh.gam = app.GetManager[*manager.GenerateACManager](oh.A)
	oh.dm = app.GetManager[*manager.DsManager](oh.A)
	oh.param = app.GetClientParam[*src.Es2Param](oh.A)
}

func (oh *OrderHandler) SwapEID(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "SwapEIDException")
		}
	}()
	swapResponse := order.NewSwapEIDResponse()
	swapRequest := k.FromJson[*order.SwapEIDRequest](string(request.GetBody()))
	swapRequest.Header.Check(response)
	ticket := request.FromInstance.ReadTicket(request)
	operator := k.FromJson[*sys.Operator](ticket.Key)
	if swapRequest.TargetEid == "" {
		swapResponse.SwapTargetEidInvalid(response)
		return
	}
	if swapRequest.SourceEid == "" {
		swapResponse.SwapOriginalEidNotExist(response)
		return
	}
	var res []*rsp.DownloadOrder
	if swapRequest.Iccid != "" {
		res = oh.dom.SelectByEidAndIccid(operator.OperatorId, swapRequest.SourceEid, swapRequest.Iccid)
	} else {
		res = oh.dom.SelectByEidAndIccid(operator.OperatorId, swapRequest.SourceEid, "")
	}
	da := oh.A.DA()
	da.Begin()
	defer da.Rollback()
	for _, item := range res {
		count := oh.dom.SwapEid(da, swapRequest.TargetEid, item.DownloadId)
		if count > 0 {
			lifeCycle := oh.pm.SelectLifeCycleListByProfileIdAndDownloadId(da, item.ProfileId, item.DownloadId)
			if len(lifeCycle) > 0 {
				currentLife := lifeCycle[0]
				currentLife.CycleId = 0
				currentLife.Eid = swapRequest.TargetEid
				currentLife.StartTime = time.Now().UnixMilli()
				currentLife.UpdateTime = time.Now().UnixMilli()
				currentLife.Description = "To forcefully change the linked EID"
				oh.pm.InsertProfileCycle(da, currentLife)
			}
		}
	}
	da.Commit()
	response.WriteJson(swapResponse)
}

// FIXME

func (oh *OrderHandler) GenerateByProfileMetadata(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "GenerateByProfileMetadataException")
		}
	}()
	originRequest := k.FromJson[*order.GenerateByProfileMetadataRequest](string(request.GetBody()))
	originRequest.Header.Check(response)
	baseProfileResponse := profile.NewBaseProfileResponse()

	metaRequest := &profile.AddProfileByMetaRequest{
		Iccid:               originRequest.Iccid,
		Imsi:                originRequest.Imsi,
		Ki:                  originRequest.Ki,
		Opc:                 originRequest.Opc,
		Pin1:                originRequest.Pin1,
		Pin2:                originRequest.Pin2,
		Puk1:                originRequest.Puk1,
		Puk2:                originRequest.Puk2,
		Adm1:                originRequest.Adm1,
		Msisdn:              originRequest.Msisdn,
		Impi:                originRequest.Impi,
		ImpuList:            originRequest.ImpuList,
		C9:                  originRequest.C9,
		Ara:                 originRequest.Ara,
		ProfileType:         originRequest.ProfileType,
		EncAesKey:           originRequest.EncAesKey,
		ServiceProviderName: originRequest.ServiceProviderName, //Rike@0717版本新墒
	}
	profileEntity := oh.pm.DoAddProfileByMeta(request, response, metaRequest, baseProfileResponse)
	if profileEntity == nil {
		response.WriteJson(baseProfileResponse)
		return
	}
	//	GenerateAC
	ticket := request.FromInstance.ReadTicket(request)
	operator := k.FromJson[*sys.Operator](ticket.Key)
	profileType := oh.pm.FindProfileTypeById(operator.OperatorId, profileEntity.TypeId)
	downloadOrder := oh.GenerateAC(profileEntity, profileType, originRequest.Eid, originRequest.ConfirmationCode, originRequest.MatchingId, originRequest.SmdsAddress, originRequest.Header.FunctionRequesterIdentifier, originRequest.Header.FunctionCallIdentifier, response, operator)
	if downloadOrder != nil {
		generateResponse := order.NewGenerateByProfileMetadataResponse()
		generateResponse.Eid = downloadOrder.Eid
		generateResponse.Iccid = downloadOrder.Iccid
		generateResponse.ActivationCode = code.BuildAc(downloadOrder, operator.SmdpAddress)
		response.WriteJson(generateResponse)
	} else {

	}
}

func (oh *OrderHandler) BatchGenerateByProfileMetadata(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "BatchGenerateByProfileMetadataException")
		}
	}()
}

func (oh *OrderHandler) BatchGenerateByProfileUppParams(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "BatchGenerateByProfileUppParamsException")
		}
	}()
}

func (oh *OrderHandler) BatchGenerate(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "BatchGenerateException")
		}
	}()
}

func (oh *OrderHandler) BatchTaskInfo(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "BatchTaskInfoException")
		}
	}()
}

func (oh *OrderHandler) ExpireOrder(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "ExpireOrderException")
		}
	}()
	expireResponse := order.NewExpireOrderResponse()
	expireOrderRequest := k.FromJson[*order.ExpireOrderRequest](string(request.GetBody()))
	expireOrderRequest.Header.Check(response)
	if expireOrderRequest.Iccid == "" {
		expireResponse.ExpireOrderICCIDInvalid(response)
		return
	}
	if expireOrderRequest.FinalProfileStatusIndicator == "" {
		expireResponse.ExpireOrderFinalProfileStatusIndicatorInvalid(response)
		return
	}
	if !strings.EqualFold(expireOrderRequest.FinalProfileStatusIndicator, "Available") && !strings.EqualFold(expireOrderRequest.FinalProfileStatusIndicator, "Unavailable") {
		expireResponse.ExpireOrderFinalProfileStatusIndicatorInvalid(response)
		return
	}
	ticket := request.FromInstance.ReadTicket(request)
	operator := k.FromJson[*sys.Operator](ticket.Key)
	downloadOrder := oh.dom.SelectByParams(expireOrderRequest.Iccid, expireOrderRequest.Eid, expireOrderRequest.MatchingId, operator.OperatorId)
	if downloadOrder == nil {
		expireResponse.ExpireOrderICCIDNotExist(response)
		return
	}
	if downloadOrder.State == code.DownloadStateExpired || downloadOrder.State == code.DownloadStateCancelled {
		expireResponse.ExpireOrderICCIDInvalidStatus(response)
		return
	}
	da := oh.A.DA()
	da.Begin()
	defer da.Rollback()
	count := oh.dom.Expire(da, downloadOrder.DownloadId) //只修改过期时间
	if count > 0 {
		p := oh.pm.FindProfileByProfileId(downloadOrder.ProfileId)
		currentState := code.ProfileStateAvailable
		if strings.EqualFold(expireOrderRequest.FinalProfileStatusIndicator, "Available") {
			currentState = code.ProfileStateAvailable
		} else {
			currentState = code.ProfileStateUnavailable
		}
		oh.pm.ChangeLifecycle(da, operator, downloadOrder.DownloadId, p.ProfileId, p.State, currentState, downloadOrder.Eid, p.RequestIccid, "To expire the Profile")
		da.Commit()
		response.WriteJson(expireResponse)
	}
}

func (oh *OrderHandler) ResetOrderDownloadCount(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "ResetOrderDownloadCountException")
		}
	}()

	resetResponse := order.NewResetOrderResponse()
	resetOrderRequest := k.FromJson[*order.ResetOrderRequest](string(request.GetBody()))
	resetOrderRequest.Header.Check(response)
	if resetOrderRequest.Iccid == "" {
		resetResponse.ResetOrderICCIDInvalid(response)
		return
	}
	ticket := request.FromInstance.ReadTicket(request)
	operator := k.FromJson[*sys.Operator](ticket.Key)
	downloadOrder := oh.dom.SelectByParams(resetOrderRequest.Iccid, "", "", operator.OperatorId)
	if downloadOrder == nil {
		resetResponse.ResetOrderICCIDNotExist(response)
		return
	}
	if downloadOrder.State != code.DownloadStateReleased && downloadOrder.State != code.DownloadStateDownloaded && downloadOrder.State != code.DownloadStateError && downloadOrder.State != code.DownloadStateInstalled {
		resetResponse.ResetOrderICCIDNotExist(response)
		return
	}
	da := oh.A.DA()
	da.Begin()
	defer da.Rollback()
	count := oh.dom.ResetOrder(da, downloadOrder.DownloadId)
	if count > 0 {
		lifeCycle := oh.pm.SelectLifeCycleListByProfileIdAndDownloadId(oh.A.DA(), downloadOrder.ProfileId, downloadOrder.DownloadId)
		if len(lifeCycle) > 0 {
			currentLife := lifeCycle[0]
			currentLife.CycleId = 0
			currentLife.StartTime = time.Now().UnixMilli()
			currentLife.UpdateTime = time.Now().UnixMilli()
			currentLife.Description = "To reset the numbers of Confirmation Code input attempts, Profile download attempts and successful Profile downloads to ‘0’"
			oh.pm.InsertProfileCycle(da, currentLife)
		}
		da.Commit()
		response.WriteJson(resetResponse)
	}
}

func (oh *OrderHandler) UpdateProfileParamAndGenerateAC(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "UpdateProfileParamAndGenerateACException")
		}
	}()
}

func (oh *OrderHandler) GetAppleUniversalLink(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "GetAppleUniversalLinkException")
		}
	}()

	appleUniversalLinkResponse := order.NewAppleUniversalLinkResponse()
	appleUniversalLinkRequest := k.FromJson[*order.AppleUniversalLinkRequest](string(request.GetBody()))
	appleUniversalLinkRequest.Header.Check(response)
	if appleUniversalLinkRequest.Iccid == "" {
		appleUniversalLinkResponse.AppleUniversalLinkICCIDInvalid(response)
		return
	}
	ticket := request.FromInstance.ReadTicket(request)
	operator := k.FromJson[*sys.Operator](ticket.Key)
	downloadOrder := oh.dom.SelectByParams(appleUniversalLinkRequest.Iccid, "", "", operator.OperatorId)
	if downloadOrder == nil {
		appleUniversalLinkResponse.AppleUniversalLinkICCIDNotExist(response)
		return
	}
	appleUniversalLinkResponse.Link = oh.param.AppleUniversalLinkUrl + code.BuildAcCode(downloadOrder, operator.SmdpAddress)
	response.WriteJson(appleUniversalLinkResponse)
}

func (oh *OrderHandler) GetOrderInfo(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "GetOrderInfoException")
		}
	}()
}

func (oh *OrderHandler) BatchPreOccupy(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "BatchPreOccupyException")
		}
	}()
	batchPreOccupyResponse := order.NewBatchPreOccupyResponse()
	batchPreOccupyRequest := k.FromJson[*order.BatchPreOccupyRequest](string(request.GetBody()))
	batchPreOccupyRequest.Header.Check(response)
	if batchPreOccupyRequest.Quantity < 1 || batchPreOccupyRequest.Quantity > 200 {
		batchPreOccupyResponse.BatchPreOccupyQuantityInvalid(response)
		return
	}
	ticket := request.FromInstance.ReadTicket(request)
	operator := k.FromJson[*sys.Operator](ticket.Key)
	profileType := oh.ptm.SelectByType(operator.OperatorId, batchPreOccupyRequest.ProfileType)
	if profileType == nil {
		profileType = &mno.ProfileType{
			TypeId:                         0,
			Name:                           "",
			Clazz:                          0,
			IsDefault:                      0,
			DownloadOrderAttemptMaxCount:   0,
			DownloadOrderCcAttemptMaxCount: 0,
			DownloadOrderExpiredMinute:     0,
			DownloadOrderMaxSuccessCount:   10,
			ReDownloadPolicy:               0,
			PolicyRules:                    "",
			NotificationConfiguration:      "",
			ServiceProviderName:            "",
			ServiceProviderMessage:         "",
			OperatorId:                     0,
			CreateTime:                     0,
			UpdateTime:                     0,
		}
	}
	var acList []string
	da := oh.A.DA()
	for i := 0; i < batchPreOccupyRequest.Quantity; i++ {
		matchingId := code.NewMatchingId(operator.OperatorId).String()
		downloadOrder := oh.om.CreateDownloadOrderByBatchPreOccupy(da, code.DownloadStateReleased, profileType, operator, matchingId, batchPreOccupyRequest.Header.FunctionRequesterIdentifier, batchPreOccupyRequest.Header.FunctionCallIdentifier)
		acList = append(acList, code.BuildAc(downloadOrder, operator.SmdpAddress))
	}
	batchPreOccupyResponse.AcList = acList
	response.WriteJson(batchPreOccupyResponse)

}

func (oh *OrderHandler) CancelPreOccupy(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "CancelPreOccupyException")
		}
	}()
	cancelPreOccupyResponse := order.NewCancelPreOccupyResponse()
	cancelPreOccupyRequest := k.FromJson[*order.CancelPreOccupyRequest](string(request.GetBody()))
	cancelPreOccupyRequest.Header.Check(response)
	if cancelPreOccupyRequest.MatchingIds == nil {
		cancelPreOccupyResponse.CancelPreOccupyMatchingIdsInvalid(response)
		return
	}
	if len(cancelPreOccupyRequest.MatchingIds) < 1 || len(cancelPreOccupyRequest.MatchingIds) > 200 {
		cancelPreOccupyResponse.CancelPreOccupyMatchingIdsInvalid(response)
		return
	}
	ticket := request.FromInstance.ReadTicket(request)
	operator := k.FromJson[*sys.Operator](ticket.Key)
	for i := 0; i < len(cancelPreOccupyRequest.MatchingIds); i++ {
		oh.dom.CancelOrderByMatchingId(operator.OperatorId, cancelPreOccupyRequest.MatchingIds[i], code.DownloadStateCancelled, code.DownloadOrderModePreOccupy)
	}
	response.WriteJson(cancelPreOccupyResponse)
}

func (oh *OrderHandler) GenerateAC(profile *mno.Profile, profileType *mno.ProfileType, eid, confirmationCode, matchingId, smdsAddress, functionRequesterIdentifier, functionCallIdentifier string, response *app.Response, operator *sys.Operator) *rsp.DownloadOrder {
	defer func() {
		if err := recover(); err != nil {
			logger.E(err)
			da := oh.A.DA()
			da.Begin()
			defer da.Rollback()
			//remove Profile
			oh.pm.DeleteByProfileId(da, profile.ProfileId)
			//remove lifeCycle
			oh.pm.DeleteLifeCycleByProfileId(da, profile.ProfileId)
			//remove profile upp
			oh.pum.DeleteByProfileId(da, profile.ProfileId)
			da.Commit()
			bo.NewInternalExceptionResponse(response, "GenerateACException")

		}
	}()
	coResponse := gsma.NewConfirmOrderResponse()
	if matchingId == "" {
		matchingId = code.NewMatchingId(profile.OperatorId).String()
	} else { //校验提供的MatchingId
		mi := code.ParseMatchingId(matchingId)
		if mi.Verify() {
			matchingId = mi.String()
		} else {
			coResponse.MatchingIdInvalid(response)
			return nil
		}
		if oh.dom.CheckMatchingIdExist(operator.OperatorId, 0, matchingId) {
			coResponse.MatchingIdAlreadyInUse(response)
			return nil
		}
	}
	if confirmationCode != "" {
		confirmationCode = asn.BytesToHex(security.Sha256([]byte(confirmationCode)))
	}
	if smdsAddress != "" {
		if smdsAddress[0] == '.' {
			//TODO：采用自己配置的If the Root SM-DS address begins with a full stop character (e.g., '.unspecified'), the SM-DP+ MAY determine the applicable Root SM-DS for this Profile in an implementation-dependent manner.
			smdsAddress = oh.param.DefaultRootSmdsDomain
		}
		if eid == "" {
			coResponse.EidMandatoryElementMissing(response)
			return nil
		}
		oh.dm.RegisterEvent(matchingId, eid, smdsAddress)
	}
	da := oh.A.DA()
	da.Begin()
	defer da.Rollback()
	download := oh.om.CreateDownloadOrder(da, profile, code.DownloadStateReleased, profileType, operator, eid, confirmationCode, matchingId, smdsAddress, functionRequesterIdentifier, functionCallIdentifier)
	if oh.pm.ChangeLifecycle(da, operator, download.DownloadId, profile.ProfileId, profile.State, code.DownloadStateReleased, eid, profile.RequestIccid, "ReleaseProfile") {
		da.Commit()
	}
	return download
}
