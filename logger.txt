2025-03-12 09:55:18.885[TIP][ES2+]Starting
2025-03-12 09:55:18.910[TIP]Checking db connect...
2025-03-12 09:55:18.998[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-03-12 09:55:18.998[TIP][ES2+][GO_TEST][es2plus]Start success, used 113 milliseconds
2025-03-12 09:55:18.998[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-03-12 09:55:20.009[TIP][RegisterInstance][0]Post "http://127.0.0.1:9000/InstanceEvent/registerInstance": dial tcp 127.0.0.1:9000: connectex: No connection could be made because the target machine actively refused it.
2025-03-12 09:55:20.009[TIP]Register Instance Fail
2025-03-12 09:55:20.009[TIP][ES2+]System will shutdown now...
2025-03-12 09:55:20.010[TIP][下线前Session数据(共0项)保存完成！]
2025-03-12 09:55:20.010[TIP][ConnectExit][0.0.0.0:9003]http: Server closed
2025-03-12 09:55:20.010[TIP][ListenPort]AllClosed
2025-03-12 17:15:29.878[TIP][从保存的Session数据(共0项)恢复完成！]
2025-03-12 17:15:29.879[TIP]Checking db connect...
2025-03-12 17:15:29.968[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-03-12 17:15:29.968[TIP][ES2+][GO_TEST][es2plus]Start success, used 115 milliseconds
2025-03-12 17:15:29.968[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-03-12 17:15:31.133[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-03-12 17:15:31.133[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-03-12 17:15:31.133[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-03-12 17:15:31.134[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-03-12 17:38:33.272[TIP][Instance Online]id[42], context[], ip[127.0.0.1], port[9000]
2025-03-12 17:38:47.827[TIP][ES2+]Starting
2025-03-12 17:38:47.848[TIP]Checking db connect...
2025-03-12 17:38:47.881[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-03-12 17:38:47.881[TIP][ES2+][GO_TEST][es2plus]Start success, used 54 milliseconds
2025-03-12 17:38:47.881[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-03-12 17:38:48.962[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-03-12 17:38:48.962[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-03-12 17:38:48.963[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-03-12 17:38:48.963[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-03-12 17:50:21.092[ERROR][&{<nil> <nil> <nil> /es2plus/order/generateByProfileMetadata 127.0.0.1 POST   127.0.0.1 0  application/json;charset=UTF8 0xc0003080f0 <nil> 0xc0002b8000 /es2plus/order/generatebyprofilemetadata  /es2plus/order/generatebyprofilemetadata 2 0xc000222000 <nil> <nil> 0xc0000cad40 false 0xc00029c080 0xc00029c040 [123 34 101 110 99 65 101 115 75 101 121 34 58 34 50 84 117 122 70 70 79 100 53 117 113 43 109 90 82 74 72 52 103 89 76 89 81 48 90 112 47 117 65 84 120 109 114 85 88 69 105 43 102 66 109 73 77 61 34 44 34 105 99 99 105 100 34 58 34 56 57 56 54 49 49 50 50 50 53 51 57 57 50 53 51 49 48 48 49 34 44 34 105 109 115 105 34 58 34 48 48 53 98 97 102 51 57 102 56 97 102 54 48 54 57 52 50 101 49 53 57 100 48 57 50 99 97 57 48 50 48 34 44 34 107 105 34 58 34 102 51 97 98 99 99 97 102 97 48 49 48 48 98 51 53 50 52 99 57 51 53 51 54 57 55 49 56 49 53 101 52 50 56 57 54 98 53 57 99 100 102 48 48 48 49 52 100 54 100 57 51 100 102 53 100 55 55 101 48 54 48 48 56 34 44 34 111 112 99 34 58 34 55 52 52 101 51 56 101 102 49 100 49 99 100 97 54 51 53 50 49 51 102 55 98 55 57 48 57 48 101 97 57 99 100 51 55 49 48 51 99 56 97 99 101 99 54 102 101 55 57 53 97 53 50 97 99 56 50 98 56 101 57 51 57 52 34 125] [] 1741773020620 9003 0 0}]runtime error: invalid memory address or nil pointer dereference
     esim.es2/src/bl.(*OrderHandler).GenerateByProfileMetadata.func1(E:/go_workplace/esim.es2/src/bl/OrderHandler.go:94)
     panic(C:/Go/src/runtime/panic.go:770)
     esim.es2/src/bl.(*OrderHandler).GenerateByProfileMetadata(E:/go_workplace/esim.es2/src/bl/OrderHandler.go:128)
     esim.es2/src/ui.(*OrderEvent).GenerateByProfileMetadata(E:/go_workplace/esim.es2/src/ui/OrderEvent.go:16)
     reflect.Value.call(C:/Go/src/reflect/value.go:596)
     reflect.Value.Call(C:/Go/src/reflect/value.go:380)
     k/app.(*Server).executeMethod(E:/go_workplace/k/app/server.go:246)
     k/app.(*appHandler).procEvent(E:/go_workplace/k/app/handler.go:531)
     k/app.(*Server).serveHttp.func2(E:/go_workplace/k/app/server.go:555)
     created by k/app.(E:/go_workplace/k/app/server.go:549)
2025-03-12 17:52:55.037[TIP]Checking db connect...
2025-03-12 17:52:55.097[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-03-12 17:52:55.097[TIP][ES2+][GO_TEST][es2plus]Start success, used 78 milliseconds
2025-03-12 17:52:55.098[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-03-12 17:52:56.210[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-03-12 17:52:56.210[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-03-12 17:52:56.210[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-03-12 17:52:56.210[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-03-12 17:53:52.318[ERROR][&{<nil> <nil> <nil> /es2plus/order/generateByProfileMetadata 127.0.0.1 POST   127.0.0.1 0  application/json;charset=UTF8 0xc0000fe0f0 <nil> 0xc000222b40 /es2plus/order/generatebyprofilemetadata  /es2plus/order/generatebyprofilemetadata 2 0xc0001a4000 <nil> <nil> 0xc00008c9c0 false 0xc000232540 0xc000232500 [123 34 101 110 99 65 101 115 75 101 121 34 58 34 76 57 79 65 85 87 122 97 106 52 90 111 80 113 106 56 106 70 100 72 74 54 68 111 55 85 52 72 122 110 110 108 90 52 72 88 87 57 116 111 53 43 69 61 34 44 34 105 99 99 105 100 34 58 34 56 57 56 54 49 49 50 50 50 53 51 57 57 50 53 51 50 48 48 49 34 44 34 105 109 115 105 34 58 34 99 50 99 48 56 52 56 57 54 54 97 48 48 57 55 57 98 98 48 48 102 102 48 53 56 101 98 98 57 52 54 55 34 44 34 107 105 34 58 34 50 55 102 55 100 98 50 49 97 98 99 102 50 53 56 98 97 56 100 101 56 101 98 52 98 100 55 48 57 52 99 100 50 48 102 98 98 98 56 101 52 51 100 97 51 52 52 100 57 48 99 57 98 55 48 57 57 56 54 50 57 99 49 53 34 44 34 111 112 99 34 58 34 97 102 48 101 100 54 98 48 99 48 50 54 50 57 98 97 97 50 98 54 48 99 101 101 99 99 97 97 50 101 97 57 50 55 102 99 102 50 49 99 98 56 100 56 57 51 55 101 100 57 56 50 51 54 52 53 100 48 48 48 54 50 55 100 34 125] [] 1741773231873 9003 0 0}]runtime error: invalid memory address or nil pointer dereference
     esim.es2/src/bl.(*OrderHandler).GenerateByProfileMetadata.func1(E:/go_workplace/esim.es2/src/bl/OrderHandler.go:94)
     panic(C:/Go/src/runtime/panic.go:770)
     esim.es2/src/bl.(*OrderHandler).GenerateByProfileMetadata(E:/go_workplace/esim.es2/src/bl/OrderHandler.go:128)
     esim.es2/src/ui.(*OrderEvent).GenerateByProfileMetadata(E:/go_workplace/esim.es2/src/ui/OrderEvent.go:16)
     reflect.Value.call(C:/Go/src/reflect/value.go:596)
     reflect.Value.Call(C:/Go/src/reflect/value.go:380)
     k/app.(*Server).executeMethod(E:/go_workplace/k/app/server.go:246)
     k/app.(*appHandler).procEvent(E:/go_workplace/k/app/handler.go:531)
     k/app.(*Server).serveHttp.func2(E:/go_workplace/k/app/server.go:555)
     created by k/app.(E:/go_workplace/k/app/server.go:549)
2025-03-12 17:56:33.744[ERROR][&{<nil> <nil> <nil> /es2plus/order/generateByProfileMetadata 127.0.0.1 POST   127.0.0.1 0  application/json;charset=UTF8 0xc0000fe0f0 <nil> 0xc0000ee120 /es2plus/order/generatebyprofilemetadata  /es2plus/order/generatebyprofilemetadata 2 0xc0001a4000 <nil> <nil> 0xc00008c9c0 false 0xc0001de4e0 0xc0001de4a0 [123 34 101 110 99 65 101 115 75 101 121 34 58 34 74 48 118 50 76 104 113 112 110 48 100 66 86 74 113 116 77 119 81 70 74 120 90 84 77 78 113 52 118 69 109 87 74 68 88 55 100 112 112 116 76 81 69 61 34 44 34 105 99 99 105 100 34 58 34 56 57 56 54 49 49 50 50 50 53 51 57 57 50 53 51 51 48 48 49 34 44 34 105 109 115 105 34 58 34 55 99 102 51 57 57 48 51 56 51 48 53 98 50 54 99 50 56 99 102 57 53 53 49 56 97 48 97 51 101 55 49 34 44 34 107 105 34 58 34 102 50 100 101 55 53 102 51 102 52 53 57 48 57 49 49 100 52 48 101 52 98 53 99 101 100 97 56 53 53 98 56 49 55 52 52 49 98 99 55 102 51 97 97 100 101 53 102 57 99 53 51 49 51 55 50 53 48 102 52 100 101 101 56 34 44 34 111 112 99 34 58 34 102 52 98 56 48 97 57 48 54 102 102 100 101 52 51 102 101 49 50 97 98 53 50 97 97 100 52 102 98 102 56 100 48 100 54 49 98 51 100 98 102 101 56 48 98 57 53 57 57 48 102 101 97 102 102 102 54 102 51 54 102 51 56 50 34 125] [] 1741773344038 9003 0 0}]runtime error: invalid memory address or nil pointer dereference
     esim.es2/src/bl.(*OrderHandler).GenerateByProfileMetadata.func1(E:/go_workplace/esim.es2/src/bl/OrderHandler.go:94)
     panic(C:/Go/src/runtime/panic.go:770)
     esim.es2/src/bl.(*OrderHandler).GenerateByProfileMetadata(E:/go_workplace/esim.es2/src/bl/OrderHandler.go:128)
     esim.es2/src/ui.(*OrderEvent).GenerateByProfileMetadata(E:/go_workplace/esim.es2/src/ui/OrderEvent.go:16)
     reflect.Value.call(C:/Go/src/reflect/value.go:596)
     reflect.Value.Call(C:/Go/src/reflect/value.go:380)
     k/app.(*Server).executeMethod(E:/go_workplace/k/app/server.go:246)
     k/app.(*appHandler).procEvent(E:/go_workplace/k/app/handler.go:531)
     k/app.(*Server).serveHttp.func2(E:/go_workplace/k/app/server.go:555)
     created by k/app.(E:/go_workplace/k/app/server.go:549)
2025-03-12 19:35:23.745[TIP]Checking db connect...
2025-03-12 19:35:23.776[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-03-12 19:35:23.777[TIP][ES2+][GO_TEST][es2plus]Start success, used 48 milliseconds
2025-03-12 19:35:23.777[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-03-12 19:35:24.847[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-03-12 19:35:24.847[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-03-12 19:35:24.848[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-03-12 19:35:24.848[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-03-12 19:35:35.489[ERROR][decrypt][profile_crypt_test]&{999 CryptoServer Error B08B0001
	CryptoServer module AES
	length of data is not a multiple of 16 bytes  1741779335447}
     esim.common/src/bl.(*KmsManager).Decrypt(E:/go_workplace/esim.common/src/bl/KmsManager.go:102)
     esim.common/src/bl.(*KmsManager).DecryptByDataCryptKey(	E:/go_workplace/esim.common/src/bl/KmsManager.go:127)
     esim.es2/src/bl.(*DeviceHandler).DeviceInfo(E:/go_workplace/esim.es2/src/bl/DeviceHandler.go:106)
     esim.es2/src/ui.(*DeviceEvent).Info(E:/go_workplace/esim.es2/src/ui/DeviceEvent.go:12)
     reflect.Value.call(C:/Go/src/reflect/value.go:596)
     reflect.Value.Call(C:/Go/src/reflect/value.go:380)
     k/app.(*Server).executeMethod(E:/go_workplace/k/app/server.go:246)
     k/app.(*appHandler).procEvent(E:/go_workplace/k/app/handler.go:531)
     k/app.(*Server).serveHttp.func2(E:/go_workplace/k/app/server.go:555)
     created by k/app.(E:/go_workplace/k/app/server.go:549)
2025-03-12 19:35:35.676[ERROR][decrypt][profile_crypt_test]&{999 CryptoServer Error B08B0001
	CryptoServer module AES
	length of data is not a multiple of 16 bytes  1741779335629}
     esim.common/src/bl.(*KmsManager).Decrypt(E:/go_workplace/esim.common/src/bl/KmsManager.go:102)
     esim.common/src/bl.(*KmsManager).DecryptByDataCryptKey(	E:/go_workplace/esim.common/src/bl/KmsManager.go:127)
     esim.es2/src/bl.(*DeviceHandler).DeviceInfo(E:/go_workplace/esim.es2/src/bl/DeviceHandler.go:106)
     esim.es2/src/ui.(*DeviceEvent).Info(E:/go_workplace/esim.es2/src/ui/DeviceEvent.go:12)
     reflect.Value.call(C:/Go/src/reflect/value.go:596)
     reflect.Value.Call(C:/Go/src/reflect/value.go:380)
     k/app.(*Server).executeMethod(E:/go_workplace/k/app/server.go:246)
     k/app.(*appHandler).procEvent(E:/go_workplace/k/app/handler.go:531)
     k/app.(*Server).serveHttp.func2(E:/go_workplace/k/app/server.go:555)
     created by k/app.(E:/go_workplace/k/app/server.go:549)
2025-03-12 19:35:35.872[ERROR][decrypt][profile_crypt_test]&{999 CryptoServer Error B08B0001
	CryptoServer module AES
	length of data is not a multiple of 16 bytes  1741779335824}
     esim.common/src/bl.(*KmsManager).Decrypt(E:/go_workplace/esim.common/src/bl/KmsManager.go:102)
     esim.common/src/bl.(*KmsManager).DecryptByDataCryptKey(	E:/go_workplace/esim.common/src/bl/KmsManager.go:127)
     esim.es2/src/bl.(*DeviceHandler).DeviceInfo(E:/go_workplace/esim.es2/src/bl/DeviceHandler.go:106)
     esim.es2/src/ui.(*DeviceEvent).Info(E:/go_workplace/esim.es2/src/ui/DeviceEvent.go:12)
     reflect.Value.call(C:/Go/src/reflect/value.go:596)
     reflect.Value.Call(C:/Go/src/reflect/value.go:380)
     k/app.(*Server).executeMethod(E:/go_workplace/k/app/server.go:246)
     k/app.(*appHandler).procEvent(E:/go_workplace/k/app/handler.go:531)
     k/app.(*Server).serveHttp.func2(E:/go_workplace/k/app/server.go:555)
     created by k/app.(E:/go_workplace/k/app/server.go:549)
2025-03-12 19:35:36.038[ERROR][decrypt][profile_crypt_test]&{999 CryptoServer Error B08B0001
	CryptoServer module AES
	length of data is not a multiple of 16 bytes  1741779335994}
     esim.common/src/bl.(*KmsManager).Decrypt(E:/go_workplace/esim.common/src/bl/KmsManager.go:102)
     esim.common/src/bl.(*KmsManager).DecryptByDataCryptKey(	E:/go_workplace/esim.common/src/bl/KmsManager.go:127)
     esim.es2/src/bl.(*DeviceHandler).DeviceInfo(E:/go_workplace/esim.es2/src/bl/DeviceHandler.go:106)
     esim.es2/src/ui.(*DeviceEvent).Info(E:/go_workplace/esim.es2/src/ui/DeviceEvent.go:12)
     reflect.Value.call(C:/Go/src/reflect/value.go:596)
     reflect.Value.Call(C:/Go/src/reflect/value.go:380)
     k/app.(*Server).executeMethod(E:/go_workplace/k/app/server.go:246)
     k/app.(*appHandler).procEvent(E:/go_workplace/k/app/handler.go:531)
     k/app.(*Server).serveHttp.func2(E:/go_workplace/k/app/server.go:555)
     created by k/app.(E:/go_workplace/k/app/server.go:549)
2025-03-12 19:38:12.324[TIP]Checking db connect...
2025-03-12 19:38:12.360[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-03-12 19:38:12.361[TIP][ES2+][GO_TEST][es2plus]Start success, used 51 milliseconds
2025-03-12 19:38:12.361[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-03-12 19:38:13.468[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-03-12 19:38:13.468[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-03-12 19:38:13.469[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-03-12 19:38:13.469[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-03-12 19:41:32.718[TIP][Instance Online]id[42], context[], ip[127.0.0.1], port[9000]
2025-03-12 19:41:46.680[TIP]Checking db connect...
2025-03-12 19:41:46.730[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-03-12 19:41:46.730[TIP][ES2+][GO_TEST][es2plus]Start success, used 68 milliseconds
2025-03-12 19:41:46.731[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-03-12 19:41:47.780[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-03-12 19:41:47.780[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-03-12 19:41:47.780[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-03-12 19:41:47.780[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-03-12 19:45:01.456[TIP]Checking db connect...
2025-03-12 19:45:01.490[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-03-12 19:45:01.492[TIP][ES2+][GO_TEST][es2plus]Start success, used 54 milliseconds
2025-03-12 19:45:01.492[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-03-12 19:45:02.553[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-03-12 19:45:02.553[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-03-12 19:45:02.553[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-03-12 19:45:02.554[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-03-12 19:46:20.837[ERROR][decrypt][profile_crypt_test]&{999 CryptoServer Error B08B0001
	CryptoServer module AES
	length of data is not a multiple of 16 bytes  1741779980793}
     esim.common/src/bl.(*KmsManager).Decrypt(E:/go_workplace/esim.common/src/bl/KmsManager.go:102)
     esim.common/src/bl.(*KmsManager).DecryptByDataCryptKey(E:/go_workplace/esim.common/src/bl/KmsManager.go:127)
     esim.es2/src/bl.(*DeviceHandler).DeviceInfo(E:/go_workplace/esim.es2/src/bl/DeviceHandler.go:106)
     esim.es2/src/ui.(*DeviceEvent).Info(E:/go_workplace/esim.es2/src/ui/DeviceEvent.go:12)
     reflect.Value.call(C:/Go/src/reflect/value.go:596)
     reflect.Value.Call(C:/Go/src/reflect/value.go:380)
     k/app.(*Server).executeMethod(E:/go_workplace/k/app/server.go:246)
     k/app.(*appHandler).procEvent(E:/go_workplace/k/app/handler.go:531)
     k/app.(*Server).serveHttp.func2(E:/go_workplace/k/app/server.go:555)
     created by k/app.(E:/go_workplace/k/app/server.go:549)
2025-03-12 19:46:21.018[ERROR][decrypt][profile_crypt_test]&{999 CryptoServer Error B08B0001
	CryptoServer module AES
	length of data is not a multiple of 16 bytes  1741779980972}
     esim.common/src/bl.(*KmsManager).Decrypt(E:/go_workplace/esim.common/src/bl/KmsManager.go:102)
     esim.common/src/bl.(*KmsManager).DecryptByDataCryptKey(E:/go_workplace/esim.common/src/bl/KmsManager.go:127)
     esim.es2/src/bl.(*DeviceHandler).DeviceInfo(E:/go_workplace/esim.es2/src/bl/DeviceHandler.go:106)
     esim.es2/src/ui.(*DeviceEvent).Info(E:/go_workplace/esim.es2/src/ui/DeviceEvent.go:12)
     reflect.Value.call(C:/Go/src/reflect/value.go:596)
     reflect.Value.Call(C:/Go/src/reflect/value.go:380)
     k/app.(*Server).executeMethod(E:/go_workplace/k/app/server.go:246)
     k/app.(*appHandler).procEvent(E:/go_workplace/k/app/handler.go:531)
     k/app.(*Server).serveHttp.func2(E:/go_workplace/k/app/server.go:555)
     created by k/app.(E:/go_workplace/k/app/server.go:549)
2025-03-12 19:46:21.181[ERROR][decrypt][profile_crypt_test]&{999 CryptoServer Error B08B0001
	CryptoServer module AES
	length of data is not a multiple of 16 bytes  1741779981138}
     esim.common/src/bl.(*KmsManager).Decrypt(E:/go_workplace/esim.common/src/bl/KmsManager.go:102)
     esim.common/src/bl.(*KmsManager).DecryptByDataCryptKey(E:/go_workplace/esim.common/src/bl/KmsManager.go:127)
     esim.es2/src/bl.(*DeviceHandler).DeviceInfo(E:/go_workplace/esim.es2/src/bl/DeviceHandler.go:106)
     esim.es2/src/ui.(*DeviceEvent).Info(E:/go_workplace/esim.es2/src/ui/DeviceEvent.go:12)
     reflect.Value.call(C:/Go/src/reflect/value.go:596)
     reflect.Value.Call(C:/Go/src/reflect/value.go:380)
     k/app.(*Server).executeMethod(E:/go_workplace/k/app/server.go:246)
     k/app.(*appHandler).procEvent(E:/go_workplace/k/app/handler.go:531)
     k/app.(*Server).serveHttp.func2(E:/go_workplace/k/app/server.go:555)
     created by k/app.(E:/go_workplace/k/app/server.go:549)
2025-03-12 19:46:21.374[ERROR][decrypt][profile_crypt_test]&{999 CryptoServer Error B08B0001
	CryptoServer module AES
	length of data is not a multiple of 16 bytes  1741779981319}
     esim.common/src/bl.(*KmsManager).Decrypt(E:/go_workplace/esim.common/src/bl/KmsManager.go:102)
     esim.common/src/bl.(*KmsManager).DecryptByDataCryptKey(E:/go_workplace/esim.common/src/bl/KmsManager.go:127)
     esim.es2/src/bl.(*DeviceHandler).DeviceInfo(E:/go_workplace/esim.es2/src/bl/DeviceHandler.go:106)
     esim.es2/src/ui.(*DeviceEvent).Info(E:/go_workplace/esim.es2/src/ui/DeviceEvent.go:12)
     reflect.Value.call(C:/Go/src/reflect/value.go:596)
     reflect.Value.Call(C:/Go/src/reflect/value.go:380)
     k/app.(*Server).executeMethod(E:/go_workplace/k/app/server.go:246)
     k/app.(*appHandler).procEvent(E:/go_workplace/k/app/handler.go:531)
     k/app.(*Server).serveHttp.func2(E:/go_workplace/k/app/server.go:555)
     created by k/app.(E:/go_workplace/k/app/server.go:549)
2025-03-12 19:49:36.401[ERROR][decrypt][profile_crypt_test]&{999 CryptoServer Error B08B0001
	CryptoServer module AES
	length of data is not a multiple of 16 bytes  1741780176358}
     esim.common/src/bl.(*KmsManager).Decrypt(E:/go_workplace/esim.common/src/bl/KmsManager.go:102)
     esim.common/src/bl.(*KmsManager).DecryptByDataCryptKey(E:/go_workplace/esim.common/src/bl/KmsManager.go:127)
     esim.es2/src/bl.(*DeviceHandler).DeviceInfo(E:/go_workplace/esim.es2/src/bl/DeviceHandler.go:106)
     esim.es2/src/ui.(*DeviceEvent).Info(E:/go_workplace/esim.es2/src/ui/DeviceEvent.go:12)
     reflect.Value.call(C:/Go/src/reflect/value.go:596)
     reflect.Value.Call(C:/Go/src/reflect/value.go:380)
     k/app.(*Server).executeMethod(E:/go_workplace/k/app/server.go:246)
     k/app.(*appHandler).procEvent(E:/go_workplace/k/app/handler.go:531)
     k/app.(*Server).serveHttp.func2(E:/go_workplace/k/app/server.go:555)
     created by k/app.(E:/go_workplace/k/app/server.go:549)
2025-03-12 19:49:36.566[ERROR][decrypt][profile_crypt_test]&{999 CryptoServer Error B08B0001
	CryptoServer module AES
	length of data is not a multiple of 16 bytes  1741780176519}
     esim.common/src/bl.(*KmsManager).Decrypt(E:/go_workplace/esim.common/src/bl/KmsManager.go:102)
     esim.common/src/bl.(*KmsManager).DecryptByDataCryptKey(E:/go_workplace/esim.common/src/bl/KmsManager.go:127)
     esim.es2/src/bl.(*DeviceHandler).DeviceInfo(E:/go_workplace/esim.es2/src/bl/DeviceHandler.go:106)
     esim.es2/src/ui.(*DeviceEvent).Info(E:/go_workplace/esim.es2/src/ui/DeviceEvent.go:12)
     reflect.Value.call(C:/Go/src/reflect/value.go:596)
     reflect.Value.Call(C:/Go/src/reflect/value.go:380)
     k/app.(*Server).executeMethod(E:/go_workplace/k/app/server.go:246)
     k/app.(*appHandler).procEvent(E:/go_workplace/k/app/handler.go:531)
     k/app.(*Server).serveHttp.func2(E:/go_workplace/k/app/server.go:555)
     created by k/app.(E:/go_workplace/k/app/server.go:549)
2025-03-12 19:49:36.741[ERROR][decrypt][profile_crypt_test]&{999 CryptoServer Error B08B0001
	CryptoServer module AES
	length of data is not a multiple of 16 bytes  1741780176696}
     esim.common/src/bl.(*KmsManager).Decrypt(E:/go_workplace/esim.common/src/bl/KmsManager.go:102)
     esim.common/src/bl.(*KmsManager).DecryptByDataCryptKey(E:/go_workplace/esim.common/src/bl/KmsManager.go:127)
     esim.es2/src/bl.(*DeviceHandler).DeviceInfo(E:/go_workplace/esim.es2/src/bl/DeviceHandler.go:106)
     esim.es2/src/ui.(*DeviceEvent).Info(E:/go_workplace/esim.es2/src/ui/DeviceEvent.go:12)
     reflect.Value.call(C:/Go/src/reflect/value.go:596)
     reflect.Value.Call(C:/Go/src/reflect/value.go:380)
     k/app.(*Server).executeMethod(E:/go_workplace/k/app/server.go:246)
     k/app.(*appHandler).procEvent(E:/go_workplace/k/app/handler.go:531)
     k/app.(*Server).serveHttp.func2(E:/go_workplace/k/app/server.go:555)
     created by k/app.(E:/go_workplace/k/app/server.go:549)
2025-03-12 19:49:36.910[ERROR][decrypt][profile_crypt_test]&{999 CryptoServer Error B08B0001
	CryptoServer module AES
	length of data is not a multiple of 16 bytes  1741780176864}
     esim.common/src/bl.(*KmsManager).Decrypt(E:/go_workplace/esim.common/src/bl/KmsManager.go:102)
     esim.common/src/bl.(*KmsManager).DecryptByDataCryptKey(E:/go_workplace/esim.common/src/bl/KmsManager.go:127)
     esim.es2/src/bl.(*DeviceHandler).DeviceInfo(E:/go_workplace/esim.es2/src/bl/DeviceHandler.go:106)
     esim.es2/src/ui.(*DeviceEvent).Info(E:/go_workplace/esim.es2/src/ui/DeviceEvent.go:12)
     reflect.Value.call(C:/Go/src/reflect/value.go:596)
     reflect.Value.Call(C:/Go/src/reflect/value.go:380)
     k/app.(*Server).executeMethod(E:/go_workplace/k/app/server.go:246)
     k/app.(*appHandler).procEvent(E:/go_workplace/k/app/handler.go:531)
     k/app.(*Server).serveHttp.func2(E:/go_workplace/k/app/server.go:555)
     created by k/app.(E:/go_workplace/k/app/server.go:549)
2025-03-12 19:49:37.058[ERROR][decrypt][profile_crypt_test]&{999 CryptoServer Error B08B0001
	CryptoServer module AES
	length of data is not a multiple of 16 bytes  1741780177012}
     esim.common/src/bl.(*KmsManager).Decrypt(E:/go_workplace/esim.common/src/bl/KmsManager.go:102)
     esim.common/src/bl.(*KmsManager).DecryptByDataCryptKey(E:/go_workplace/esim.common/src/bl/KmsManager.go:127)
     esim.es2/src/bl.(*DeviceHandler).DeviceInfo(E:/go_workplace/esim.es2/src/bl/DeviceHandler.go:106)
     esim.es2/src/ui.(*DeviceEvent).Info(E:/go_workplace/esim.es2/src/ui/DeviceEvent.go:12)
     reflect.Value.call(C:/Go/src/reflect/value.go:596)
     reflect.Value.Call(C:/Go/src/reflect/value.go:380)
     k/app.(*Server).executeMethod(E:/go_workplace/k/app/server.go:246)
     k/app.(*appHandler).procEvent(E:/go_workplace/k/app/handler.go:531)
     k/app.(*Server).serveHttp.func2(E:/go_workplace/k/app/server.go:555)
     created by k/app.(E:/go_workplace/k/app/server.go:549)
2025-03-12 19:49:37.225[ERROR][decrypt][profile_crypt_test]&{999 CryptoServer Error B08B0001
	CryptoServer module AES
	length of data is not a multiple of 16 bytes  1741780177181}
     esim.common/src/bl.(*KmsManager).Decrypt(E:/go_workplace/esim.common/src/bl/KmsManager.go:102)
     esim.common/src/bl.(*KmsManager).DecryptByDataCryptKey(E:/go_workplace/esim.common/src/bl/KmsManager.go:127)
     esim.es2/src/bl.(*DeviceHandler).DeviceInfo(E:/go_workplace/esim.es2/src/bl/DeviceHandler.go:106)
     esim.es2/src/ui.(*DeviceEvent).Info(E:/go_workplace/esim.es2/src/ui/DeviceEvent.go:12)
     reflect.Value.call(C:/Go/src/reflect/value.go:596)
     reflect.Value.Call(C:/Go/src/reflect/value.go:380)
     k/app.(*Server).executeMethod(E:/go_workplace/k/app/server.go:246)
     k/app.(*appHandler).procEvent(E:/go_workplace/k/app/handler.go:531)
     k/app.(*Server).serveHttp.func2(E:/go_workplace/k/app/server.go:555)
     created by k/app.(E:/go_workplace/k/app/server.go:549)
2025-03-12 19:49:37.366[ERROR][decrypt][profile_crypt_test]&{999 CryptoServer Error B08B0001
	CryptoServer module AES
	length of data is not a multiple of 16 bytes  1741780177320}
     esim.common/src/bl.(*KmsManager).Decrypt(E:/go_workplace/esim.common/src/bl/KmsManager.go:102)
     esim.common/src/bl.(*KmsManager).DecryptByDataCryptKey(E:/go_workplace/esim.common/src/bl/KmsManager.go:127)
     esim.es2/src/bl.(*DeviceHandler).DeviceInfo(E:/go_workplace/esim.es2/src/bl/DeviceHandler.go:106)
     esim.es2/src/ui.(*DeviceEvent).Info(E:/go_workplace/esim.es2/src/ui/DeviceEvent.go:12)
     reflect.Value.call(C:/Go/src/reflect/value.go:596)
     reflect.Value.Call(C:/Go/src/reflect/value.go:380)
     k/app.(*Server).executeMethod(E:/go_workplace/k/app/server.go:246)
     k/app.(*appHandler).procEvent(E:/go_workplace/k/app/handler.go:531)
     k/app.(*Server).serveHttp.func2(E:/go_workplace/k/app/server.go:555)
     created by k/app.(E:/go_workplace/k/app/server.go:549)
2025-03-12 19:49:37.518[ERROR][decrypt][profile_crypt_test]&{999 CryptoServer Error B08B0001
	CryptoServer module AES
	length of data is not a multiple of 16 bytes  1741780177474}
     esim.common/src/bl.(*KmsManager).Decrypt(E:/go_workplace/esim.common/src/bl/KmsManager.go:102)
     esim.common/src/bl.(*KmsManager).DecryptByDataCryptKey(E:/go_workplace/esim.common/src/bl/KmsManager.go:127)
     esim.es2/src/bl.(*DeviceHandler).DeviceInfo(E:/go_workplace/esim.es2/src/bl/DeviceHandler.go:106)
     esim.es2/src/ui.(*DeviceEvent).Info(E:/go_workplace/esim.es2/src/ui/DeviceEvent.go:12)
     reflect.Value.call(C:/Go/src/reflect/value.go:596)
     reflect.Value.Call(C:/Go/src/reflect/value.go:380)
     k/app.(*Server).executeMethod(E:/go_workplace/k/app/server.go:246)
     k/app.(*appHandler).procEvent(E:/go_workplace/k/app/handler.go:531)
     k/app.(*Server).serveHttp.func2(E:/go_workplace/k/app/server.go:555)
     created by k/app.(E:/go_workplace/k/app/server.go:549)
2025-03-12 19:49:37.690[ERROR][decrypt][profile_crypt_test]&{999 CryptoServer Error B08B0001
	CryptoServer module AES
	length of data is not a multiple of 16 bytes  1741780177640}
     esim.common/src/bl.(*KmsManager).Decrypt(E:/go_workplace/esim.common/src/bl/KmsManager.go:102)
     esim.common/src/bl.(*KmsManager).DecryptByDataCryptKey(E:/go_workplace/esim.common/src/bl/KmsManager.go:127)
     esim.es2/src/bl.(*DeviceHandler).DeviceInfo(E:/go_workplace/esim.es2/src/bl/DeviceHandler.go:106)
     esim.es2/src/ui.(*DeviceEvent).Info(E:/go_workplace/esim.es2/src/ui/DeviceEvent.go:12)
     reflect.Value.call(C:/Go/src/reflect/value.go:596)
     reflect.Value.Call(C:/Go/src/reflect/value.go:380)
     k/app.(*Server).executeMethod(E:/go_workplace/k/app/server.go:246)
     k/app.(*appHandler).procEvent(E:/go_workplace/k/app/handler.go:531)
     k/app.(*Server).serveHttp.func2(E:/go_workplace/k/app/server.go:555)
     created by k/app.(E:/go_workplace/k/app/server.go:549)
2025-03-12 19:49:37.859[ERROR][decrypt][profile_crypt_test]&{999 CryptoServer Error B08B0001
	CryptoServer module AES
	length of data is not a multiple of 16 bytes  1741780177812}
     esim.common/src/bl.(*KmsManager).Decrypt(E:/go_workplace/esim.common/src/bl/KmsManager.go:102)
     esim.common/src/bl.(*KmsManager).DecryptByDataCryptKey(E:/go_workplace/esim.common/src/bl/KmsManager.go:127)
     esim.es2/src/bl.(*DeviceHandler).DeviceInfo(E:/go_workplace/esim.es2/src/bl/DeviceHandler.go:106)
     esim.es2/src/ui.(*DeviceEvent).Info(E:/go_workplace/esim.es2/src/ui/DeviceEvent.go:12)
     reflect.Value.call(C:/Go/src/reflect/value.go:596)
     reflect.Value.Call(C:/Go/src/reflect/value.go:380)
     k/app.(*Server).executeMethod(E:/go_workplace/k/app/server.go:246)
     k/app.(*appHandler).procEvent(E:/go_workplace/k/app/handler.go:531)
     k/app.(*Server).serveHttp.func2(E:/go_workplace/k/app/server.go:555)
     created by k/app.(E:/go_workplace/k/app/server.go:549)
2025-03-12 19:55:14.186[TIP][ES2+]Starting
2025-03-12 19:55:14.203[TIP]Checking db connect...
2025-03-12 19:55:14.321[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-03-12 19:55:14.322[TIP][ES2+][GO_TEST][es2plus]Start success, used 136 milliseconds
2025-03-12 19:55:14.323[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-03-12 19:55:15.429[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-03-12 19:55:15.429[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-03-12 19:55:15.429[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-03-12 19:55:15.430[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-03-12 20:04:56.559[TIP]Checking db connect...
2025-03-12 20:04:56.592[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-03-12 20:04:56.593[TIP][ES2+][GO_TEST][es2plus]Start success, used 52 milliseconds
2025-03-12 20:04:56.594[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-03-12 20:04:57.704[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-03-12 20:04:57.704[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-03-12 20:04:57.704[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-03-12 20:04:57.704[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-03-12 20:08:25.169[TIP]Checking db connect...
2025-03-12 20:08:25.202[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-03-12 20:08:25.202[TIP][ES2+][GO_TEST][es2plus]Start success, used 47 milliseconds
2025-03-12 20:08:25.202[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-03-12 20:08:26.239[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-03-12 20:08:26.240[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-03-12 20:08:26.240[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-03-12 20:08:26.240[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-03-14 10:33:23.693[TIP][ES2+]Starting
2025-03-14 10:33:23.714[TIP]Checking db connect...
2025-03-14 10:33:24.362[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-03-14 10:33:24.363[TIP][ES2+][GO_TEST][es2plus]Start success, used 670 milliseconds
2025-03-14 10:33:24.363[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-03-14 10:33:26.745[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-03-14 10:33:26.745[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-03-14 10:33:26.745[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-03-14 10:33:26.745[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-03-14 10:34:23.973[TIP][ES2+]Starting
2025-03-14 10:34:23.989[TIP]Checking db connect...
2025-03-14 10:34:24.024[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-03-14 10:34:24.024[TIP][ES2+][GO_TEST][es2plus]Start success, used 51 milliseconds
2025-03-14 10:34:24.024[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-03-14 10:34:25.127[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-03-14 10:34:25.127[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-03-14 10:34:25.128[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-03-14 10:34:25.128[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-03-14 10:39:25.839[TIP][ES2+]Starting
2025-03-14 10:39:25.861[TIP]Checking db connect...
2025-03-14 10:39:25.902[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-03-14 10:39:25.903[TIP][ES2+][GO_TEST][es2plus]Start success, used 64 milliseconds
2025-03-14 10:39:25.904[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-03-14 10:39:27.019[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-03-14 10:39:27.019[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-03-14 10:39:27.019[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-03-14 10:39:27.019[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-03-14 10:46:41.406[TIP][ES2+]Starting
2025-03-14 10:46:41.421[TIP]Checking db connect...
2025-03-14 10:46:41.469[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-03-14 10:46:41.470[TIP][ES2+][GO_TEST][es2plus]Start success, used 63 milliseconds
2025-03-14 10:46:41.470[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-03-14 10:46:42.570[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-03-14 10:46:42.570[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-03-14 10:46:42.570[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-03-14 10:46:42.570[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-03-14 10:47:49.328[TIP]Checking db connect...
2025-03-14 10:47:49.367[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-03-14 10:47:49.367[TIP][ES2+][GO_TEST][es2plus]Start success, used 65 milliseconds
2025-03-14 10:47:49.367[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-03-14 10:47:50.469[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-03-14 10:47:50.469[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-03-14 10:47:50.469[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-03-14 10:47:50.469[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-03-14 10:49:24.662[ERROR][&{<nil> <nil> <nil> /es2plus/order/expire 127.0.0.1 POST   127.0.0.1 0  application/json;charset=UTF8 0xc0001565a0 <nil> 0xc0001ce000 /es2plus/order/expire  /es2plus/order/expire 2 0xc000156000 <nil> <nil> 0xc000204760 false 0xc0001841a0 0xc000184160 [123 34 102 105 110 97 108 80 114 111 102 105 108 101 83 116 97 116 117 115 73 110 100 105 99 97 116 111 114 34 58 34 65 118 97 105 108 97 98 108 101 34 44 34 104 101 97 100 101 114 34 58 123 34 102 117 110 99 116 105 111 110 67 97 108 108 73 100 101 110 116 105 102 105 101 114 34 58 34 67 111 110 102 105 114 109 79 114 100 101 114 34 44 34 102 117 110 99 116 105 111 110 82 101 113 117 101 115 116 101 114 73 100 101 110 116 105 102 105 101 114 34 58 34 102 51 99 53 50 55 57 53 45 49 97 53 51 45 52 48 97 54 45 97 51 53 97 45 98 102 49 54 101 101 102 100 101 57 101 100 34 125 44 34 105 99 99 105 100 34 58 34 56 57 56 54 49 49 50 50 50 53 51 57 57 50 53 51 51 56 48 51 34 125] [] 1741920514568 9003 0 0}]Lock wait timeout exceeded; try restarting transaction
     k/db.(*connector).handleErrorPacket(E:/go_workplace/k/db/mysql.go:1794)
     k/db.(*connector).readResultSetHeaderPacket(E:/go_workplace/k/db/mysql.go:2388)
     k/db.(*connector).exec(E:/go_workplace/k/db/mysql.go:867)
     k/db.(*connector).Exec(E:/go_workplace/k/db/mysql.go:1193)
     k/db.(*DA).Exec(E:/go_workplace/k/db/da.go:1003)
     k/db.(*DA).Update(E:/go_workplace/k/db/da.go:676)
     esim.es2/src/bl/manager.(*DownloadOrderManager).UpdateState(E:/go_workplace/esim.es2/src/bl/manager/DownloadOrderManager.go:162)
     esim.es2/src/bl.(*OrderHandler).ExpireOrder(E:/go_workplace/esim.es2/src/bl/OrderHandler.go:215)
     esim.es2/src/ui.(*OrderEvent).Expire(E:/go_workplace/esim.es2/src/ui/OrderEvent.go:36)
     reflect.Value.call(C:/Go/src/reflect/value.go:596)
2025-03-14 10:49:53.215[TIP]Checking db connect...
2025-03-14 10:49:53.253[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-03-14 10:49:53.253[TIP][ES2+][GO_TEST][es2plus]Start success, used 55 milliseconds
2025-03-14 10:49:53.253[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-03-14 10:49:54.359[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-03-14 10:49:54.359[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-03-14 10:49:54.359[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-03-14 10:49:54.359[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-03-14 10:51:01.567[TIP]Checking db connect...
2025-03-14 10:51:01.602[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-03-14 10:51:01.603[TIP][ES2+][GO_TEST][es2plus]Start success, used 50 milliseconds
2025-03-14 10:51:01.603[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-03-14 10:51:02.693[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-03-14 10:51:02.693[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-03-14 10:51:02.693[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-03-14 10:51:02.693[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-03-21 10:39:01.122[TIP]Checking db connect...
2025-03-21 10:39:01.159[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-03-21 10:39:01.159[TIP]----------------------------------------------
2025-03-21 10:39:01.159[TIP]ES2+ v1.0.1(k1.0.6)
2025-03-21 10:39:01.159[TIP]----------------------------------------------
2025-03-21 10:39:01.669[TIP][当前版本]Init
2025-03-21 10:39:01.670[TIP]发现需要升级版本1.0.0
2025-03-21 10:39:01.670[TIP]1. 初始版本
2025-03-21 10:39:02.169[TIP]执行版本1.0.0升级程序...
2025-03-21 10:39:02.170[TIP]版本1.0.0升级完成
2025-03-21 10:39:02.170[TIP][ES2+][GO_TEST][es2plus]Start success, used 1,066 milliseconds
2025-03-21 10:39:02.171[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-03-21 10:39:03.255[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-03-21 10:39:03.255[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-03-21 10:39:03.255[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-03-21 10:39:03.256[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-03-21 10:39:03.256[TIP][id][50][context][admin][protocol][http][ip][127.0.0.1][port][9008]GroupInstance
2025-03-21 19:11:34.811[TIP][ES2+]Starting
2025-03-21 19:11:34.828[TIP]Checking db connect...
2025-03-21 19:11:35.060[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-03-21 19:11:35.061[TIP]----------------------------------------------
2025-03-21 19:11:35.061[TIP]ES2+ v1.0.1(k1.0.6)
2025-03-21 19:11:35.061[TIP]----------------------------------------------
2025-03-21 19:11:35.564[TIP][ES2+][1.0.1][升级时间]2025-03-21 10:39:02
2025-03-21 19:11:35.564[TIP]当前已经是最新版本
2025-03-21 19:11:35.564[TIP][ES2+][GO_TEST][es2plus]Start success, used 752 milliseconds
2025-03-21 19:11:35.564[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-03-21 19:11:36.685[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-03-21 19:11:36.686[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-03-21 19:11:36.686[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-03-21 19:11:36.686[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-03-21 19:11:36.686[TIP][id][50][context][admin][protocol][http][ip][127.0.0.1][port][9008]GroupInstance
2025-03-21 19:15:28.843[TIP][ES2+]Starting
2025-03-21 19:15:28.861[TIP]Checking db connect...
2025-03-21 19:15:28.899[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-03-21 19:15:28.899[TIP]----------------------------------------------
2025-03-21 19:15:28.899[TIP]ES2+ v1.0.1(k1.0.6)
2025-03-21 19:15:28.899[TIP]----------------------------------------------
2025-03-21 19:15:29.410[TIP][ES2+][1.0.1][升级时间]2025-03-21 10:39:02
2025-03-21 19:15:29.411[TIP]当前已经是最新版本
2025-03-21 19:15:29.411[TIP][ES2+][GO_TEST][es2plus]Start success, used 567 milliseconds
2025-03-21 19:15:29.411[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-03-21 19:15:30.490[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-03-21 19:15:30.490[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-03-21 19:15:30.490[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-03-21 19:15:30.491[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-03-21 19:15:30.491[TIP][id][50][context][admin][protocol][http][ip][127.0.0.1][port][9008]GroupInstance
2025-03-31 11:25:00.271[TIP]Checking db connect...
2025-03-31 11:25:00.307[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-03-31 11:25:00.308[TIP]----------------------------------------------
2025-03-31 11:25:00.308[TIP]ES2+ v1.0.1(k1.0.6)
2025-03-31 11:25:00.308[TIP]----------------------------------------------
2025-03-31 11:25:00.823[TIP][ES2+][1.0.1][升级时间]2025-03-21 10:39:02
2025-03-31 11:25:00.824[TIP]当前已经是最新版本
2025-03-31 11:25:00.824[TIP][ES2+][GO_TEST][es2plus]Start success, used 573 milliseconds
2025-03-31 11:25:00.824[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-03-31 11:25:01.897[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-03-31 11:25:01.897[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-03-31 11:25:01.897[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-03-31 11:25:01.897[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-03-31 11:25:01.897[TIP][id][50][context][admin][protocol][http][ip][127.0.0.1][port][9008]GroupInstance
2025-03-31 11:31:56.325[ERROR][&{<nil> <nil> <nil> /es2plus/order/batchPreOccupy 127.0.0.1 POST   127.0.0.1 0  application/json;charset=UTF8 0xc0003360f0 <nil> 0xc000308120 /es2plus/order/batchpreoccupy  /es2plus/order/batchpreoccupy 2 0xc0001a2000 <nil> <nil> 0xc00009a680 false 0xc00031ea20 0xc00031e9e0 [123 34 104 101 97 100 101 114 34 58 123 34 102 117 110 99 116 105 111 110 67 97 108 108 73 100 101 110 116 105 102 105 101 114 34 58 34 67 111 110 102 105 114 109 79 114 100 101 114 34 44 34 102 117 110 99 116 105 111 110 82 101 113 117 101 115 116 101 114 73 100 101 110 116 105 102 105 101 114 34 58 34 49 57 50 57 100 97 53 99 45 98 51 50 101 45 52 55 49 101 45 97 53 97 102 45 49 48 97 99 97 51 48 101 52 52 101 56 34 125 44 34 112 114 111 102 105 108 101 84 121 112 101 34 58 34 56 57 48 51 52 48 49 49 48 50 50 51 50 48 48 48 48 48 48 48 48 48 48 48 48 48 50 56 55 56 54 53 34 44 34 113 117 97 110 116 105 116 121 34 58 49 48 125] [] 1743391916323 9003 0 0}]runtime error: invalid memory address or nil pointer dereference
     esim.es2/src/bl.(*OrderHandler).BatchPreOccupy.func1(E:/go_workplace/esim.es2/src/bl/OrderHandler.go:323)
     panic(C:/Go/src/runtime/panic.go:770)
     esim.es2/src/bl.(*OrderHandler).BatchPreOccupy(E:/go_workplace/esim.es2/src/bl/OrderHandler.go:335)
     esim.es2/src/ui.(*OrderEvent).BatchPreOccupy(E:/go_workplace/esim.es2/src/ui/OrderEvent.go:56)
     reflect.Value.call(C:/Go/src/reflect/value.go:596)
     reflect.Value.Call(C:/Go/src/reflect/value.go:380)
     k/app.(*Server).executeMethod(E:/go_workplace/k/app/server.go:246)
     k/app.(*appHandler).procEvent(E:/go_workplace/k/app/handler.go:531)
     k/app.(*Server).serveHttp.func2(E:/go_workplace/k/app/server.go:555)
     created by k/app.(E:/go_workplace/k/app/server.go:549)
2025-03-31 11:34:51.736[TIP]Checking db connect...
2025-03-31 11:34:51.833[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-03-31 11:34:51.833[TIP]----------------------------------------------
2025-03-31 11:34:51.833[TIP]ES2+ v1.0.1(k1.0.6)
2025-03-31 11:34:51.833[TIP]----------------------------------------------
2025-03-31 11:34:52.334[TIP][ES2+][1.0.1][升级时间]2025-03-21 10:39:02
2025-03-31 11:34:52.335[TIP]当前已经是最新版本
2025-03-31 11:34:52.335[TIP][ES2+][GO_TEST][es2plus]Start success, used 617 milliseconds
2025-03-31 11:34:52.335[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-03-31 11:34:53.450[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-03-31 11:34:53.450[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-03-31 11:34:53.450[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-03-31 11:34:53.450[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-03-31 11:34:53.450[TIP][id][50][context][admin][protocol][http][ip][127.0.0.1][port][9008]GroupInstance
2025-03-31 11:34:58.321[ERROR][&{<nil> <nil> <nil> /es2plus/order/batchPreOccupy 127.0.0.1 POST   127.0.0.1 0  application/json;charset=UTF8 0xc0003360f0 <nil> 0xc0001c6120 /es2plus/order/batchpreoccupy  /es2plus/order/batchpreoccupy 2 0xc000158000 <nil> <nil> 0xc00017e800 false 0xc00017eec0 0xc00031e9c0 [123 34 104 101 97 100 101 114 34 58 123 34 102 117 110 99 116 105 111 110 67 97 108 108 73 100 101 110 116 105 102 105 101 114 34 58 34 67 111 110 102 105 114 109 79 114 100 101 114 34 44 34 102 117 110 99 116 105 111 110 82 101 113 117 101 115 116 101 114 73 100 101 110 116 105 102 105 101 114 34 58 34 48 50 98 53 100 97 54 49 45 49 101 102 57 45 52 49 100 98 45 57 98 99 50 45 99 100 102 54 98 98 98 99 52 101 48 100 34 125 44 34 112 114 111 102 105 108 101 84 121 112 101 34 58 34 56 57 48 51 52 48 49 49 48 50 50 51 50 48 48 48 48 48 48 48 48 48 48 48 48 48 50 56 55 56 54 53 34 44 34 113 117 97 110 116 105 116 121 34 58 49 48 125] [] 1743392098320 9003 0 0}]runtime error: invalid memory address or nil pointer dereference
     esim.es2/src/bl.(*OrderHandler).BatchPreOccupy.func1(E:/go_workplace/esim.es2/src/bl/OrderHandler.go:323)
     panic(C:/Go/src/runtime/panic.go:770)
     esim.es2/src/bl.(*OrderHandler).BatchPreOccupy(E:/go_workplace/esim.es2/src/bl/OrderHandler.go:335)
     esim.es2/src/ui.(*OrderEvent).BatchPreOccupy(E:/go_workplace/esim.es2/src/ui/OrderEvent.go:56)
     reflect.Value.call(C:/Go/src/reflect/value.go:596)
     reflect.Value.Call(C:/Go/src/reflect/value.go:380)
     k/app.(*Server).executeMethod(E:/go_workplace/k/app/server.go:246)
     k/app.(*appHandler).procEvent(E:/go_workplace/k/app/handler.go:531)
     k/app.(*Server).serveHttp.func2(E:/go_workplace/k/app/server.go:555)
     created by k/app.(E:/go_workplace/k/app/server.go:549)
2025-03-31 11:36:04.592[ERROR][&{<nil> <nil> <nil> /es2plus/order/batchPreOccupy 127.0.0.1 POST   127.0.0.1 0  application/json;charset=UTF8 0xc0003360f0 <nil> 0xc0001c6240 /es2plus/order/batchpreoccupy  /es2plus/order/batchpreoccupy 2 0xc000158000 <nil> <nil> 0xc00017e800 false 0xc0001843a0 0xc000184360 [123 34 104 101 97 100 101 114 34 58 123 34 102 117 110 99 116 105 111 110 67 97 108 108 73 100 101 110 116 105 102 105 101 114 34 58 34 67 111 110 102 105 114 109 79 114 100 101 114 34 44 34 102 117 110 99 116 105 111 110 82 101 113 117 101 115 116 101 114 73 100 101 110 116 105 102 105 101 114 34 58 34 52 55 102 52 54 56 101 57 45 52 101 102 99 45 52 102 55 57 45 56 100 100 102 45 55 98 53 57 99 97 54 57 102 48 53 52 34 125 44 34 112 114 111 102 105 108 101 84 121 112 101 34 58 34 56 57 48 51 52 48 49 49 48 50 50 51 50 48 48 48 48 48 48 48 48 48 48 48 48 48 50 56 55 56 54 53 34 44 34 113 117 97 110 116 105 116 121 34 58 49 48 125] [] 1743392164592 9003 0 0}]runtime error: invalid memory address or nil pointer dereference
     esim.es2/src/bl.(*OrderHandler).BatchPreOccupy.func1(E:/go_workplace/esim.es2/src/bl/OrderHandler.go:323)
     panic(C:/Go/src/runtime/panic.go:770)
     esim.es2/src/bl.(*OrderHandler).BatchPreOccupy(E:/go_workplace/esim.es2/src/bl/OrderHandler.go:335)
     esim.es2/src/ui.(*OrderEvent).BatchPreOccupy(E:/go_workplace/esim.es2/src/ui/OrderEvent.go:56)
     reflect.Value.call(C:/Go/src/reflect/value.go:596)
     reflect.Value.Call(C:/Go/src/reflect/value.go:380)
     k/app.(*Server).executeMethod(E:/go_workplace/k/app/server.go:246)
     k/app.(*appHandler).procEvent(E:/go_workplace/k/app/handler.go:531)
     k/app.(*Server).serveHttp.func2(E:/go_workplace/k/app/server.go:555)
     created by k/app.(E:/go_workplace/k/app/server.go:549)
2025-03-31 11:36:52.148[ERROR][&{<nil> <nil> <nil> /es2plus/order/batchPreOccupy 127.0.0.1 POST   127.0.0.1 0  application/json;charset=UTF8 0xc0003360f0 <nil> 0xc0001c6480 /es2plus/order/batchpreoccupy  /es2plus/order/batchpreoccupy 2 0xc000158000 <nil> <nil> 0xc00017e800 false 0xc000184620 0xc0001845c0 [123 34 104 101 97 100 101 114 34 58 123 34 102 117 110 99 116 105 111 110 67 97 108 108 73 100 101 110 116 105 102 105 101 114 34 58 34 67 111 110 102 105 114 109 79 114 100 101 114 34 44 34 102 117 110 99 116 105 111 110 82 101 113 117 101 115 116 101 114 73 100 101 110 116 105 102 105 101 114 34 58 34 51 102 97 102 48 56 99 55 45 100 101 99 51 45 52 52 98 98 45 56 100 50 101 45 57 49 97 50 102 101 49 49 48 49 57 98 34 125 44 34 112 114 111 102 105 108 101 84 121 112 101 34 58 34 56 57 48 51 52 48 49 49 48 50 50 51 50 48 48 48 48 48 48 48 48 48 48 48 48 48 50 56 55 56 54 53 34 44 34 113 117 97 110 116 105 116 121 34 58 49 48 125] [] 1743392212147 9003 0 0}]runtime error: invalid memory address or nil pointer dereference
     esim.es2/src/bl.(*OrderHandler).BatchPreOccupy.func1(E:/go_workplace/esim.es2/src/bl/OrderHandler.go:323)
     panic(C:/Go/src/runtime/panic.go:770)
     esim.es2/src/bl.(*OrderHandler).BatchPreOccupy(E:/go_workplace/esim.es2/src/bl/OrderHandler.go:335)
     esim.es2/src/ui.(*OrderEvent).BatchPreOccupy(E:/go_workplace/esim.es2/src/ui/OrderEvent.go:56)
     reflect.Value.call(C:/Go/src/reflect/value.go:596)
     reflect.Value.Call(C:/Go/src/reflect/value.go:380)
     k/app.(*Server).executeMethod(E:/go_workplace/k/app/server.go:246)
     k/app.(*appHandler).procEvent(E:/go_workplace/k/app/handler.go:531)
     k/app.(*Server).serveHttp.func2(E:/go_workplace/k/app/server.go:555)
     created by k/app.(E:/go_workplace/k/app/server.go:549)
2025-03-31 11:38:30.879[TIP]Checking db connect...
2025-03-31 11:38:30.916[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-03-31 11:38:30.916[TIP]----------------------------------------------
2025-03-31 11:38:30.916[TIP]ES2+ v1.0.1(k1.0.6)
2025-03-31 11:38:30.916[TIP]----------------------------------------------
2025-03-31 11:38:31.423[TIP][ES2+][1.0.1][升级时间]2025-03-21 10:39:02
2025-03-31 11:38:31.424[TIP]当前已经是最新版本
2025-03-31 11:38:31.424[TIP][ES2+][GO_TEST][es2plus]Start success, used 562 milliseconds
2025-03-31 11:38:31.424[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-03-31 11:38:32.562[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-03-31 11:38:32.563[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-03-31 11:38:32.563[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-03-31 11:38:32.563[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-03-31 11:38:32.563[TIP][id][50][context][admin][protocol][http][ip][127.0.0.1][port][9008]GroupInstance
2025-03-31 14:28:53.776[TIP][UPDATE rsp_download_order_t SET state=?,update_time=1743402533742,update_by=44 WHERE operatorId=? and matching_id=? and mode=?]Unknown column 'operatorId' in 'where clause'
2025-03-31 14:28:53.777[TIP][8][18][E5AM8-T84WI-Y2CQB-19DII]5
2025-03-31 14:28:53.791[TIP][UPDATE rsp_download_order_t SET state=?,update_time=1743402533776,update_by=44 WHERE operatorId=? and matching_id=? and mode=?]Unknown column 'operatorId' in 'where clause'
2025-03-31 14:28:53.792[TIP][8][18][VJ5M8-5VGWI-YCLQ3-1A6IM]5
2025-03-31 14:28:53.819[TIP][UPDATE rsp_download_order_t SET state=?,update_time=1743402533791,update_by=44 WHERE operatorId=? and matching_id=? and mode=?]Unknown column 'operatorId' in 'where clause'
2025-03-31 14:28:53.820[TIP][8][18][OVPM8-YKVWI-YQDWK-1B3IM]5
2025-03-31 14:30:40.943[TIP][UPDATE rsp_download_order_t SET state=?,update_time=1743402640907,update_by=44 WHERE operatorId=? and matching_id=? and mode=?]Unknown column 'operatorId' in 'where clause'
2025-03-31 14:30:40.943[TIP][8][18][E5AM8-T84WI-Y2CQB-19DII]5
2025-03-31 14:30:40.984[TIP][UPDATE rsp_download_order_t SET state=?,update_time=1743402640943,update_by=44 WHERE operatorId=? and matching_id=? and mode=?]Unknown column 'operatorId' in 'where clause'
2025-03-31 14:30:40.985[TIP][8][18][VJ5M8-5VGWI-YCLQ3-1A6IM]5
2025-03-31 14:30:41.023[TIP][UPDATE rsp_download_order_t SET state=?,update_time=1743402640984,update_by=44 WHERE operatorId=? and matching_id=? and mode=?]Unknown column 'operatorId' in 'where clause'
2025-03-31 14:30:41.023[TIP][8][18][OVPM8-YKVWI-YQDWK-1B3IM]5
2025-03-31 14:31:33.199[TIP]Checking db connect...
2025-03-31 14:31:33.345[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-03-31 14:31:33.345[TIP]----------------------------------------------
2025-03-31 14:31:33.345[TIP]ES2+ v1.0.1(k1.0.6)
2025-03-31 14:31:33.345[TIP]----------------------------------------------
2025-03-31 14:31:33.853[TIP][ES2+][1.0.1][升级时间]2025-03-21 10:39:02
2025-03-31 14:31:33.853[TIP]当前已经是最新版本
2025-03-31 14:31:33.853[TIP][ES2+][GO_TEST][es2plus]Start success, used 671 milliseconds
2025-03-31 14:31:33.853[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-03-31 14:31:34.997[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-03-31 14:31:34.998[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-03-31 14:31:34.998[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-03-31 14:31:34.998[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-03-31 14:31:34.998[TIP][id][50][context][admin][protocol][http][ip][127.0.0.1][port][9008]GroupInstance
2025-04-07 15:36:45.211[TIP]Checking db connect...
2025-04-07 15:36:45.409[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-04-07 15:36:45.410[TIP][Generate Db entity starting]d:
2025-04-07 15:36:45.825[TIP]Generate Db entity finished
2025-04-07 15:36:45.825[TIP]----------------------------------------------
2025-04-07 15:36:45.826[TIP]ES2+ v1.0.1(k1.0.6)
2025-04-07 15:36:45.826[TIP]----------------------------------------------
2025-04-07 15:36:46.337[TIP][ES2+][1.0.1][升级时间]2025-03-21 10:39:02
2025-04-07 15:36:46.338[TIP]当前已经是最新版本
2025-04-07 15:36:46.338[TIP][ES2+][GO_TEST][es2plus]Start success, used 1,146 milliseconds
2025-04-07 15:36:46.338[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-04-07 15:36:47.352[TIP][RegisterInstance][0]Post "http://127.0.0.1:9000/InstanceEvent/registerInstance": dial tcp 127.0.0.1:9000: connectex: No connection could be made because the target machine actively refused it.
2025-04-07 15:36:47.352[TIP]Register Instance Fail
2025-04-07 15:36:47.352[TIP][ES2+]System will shutdown now...
2025-04-07 15:36:47.352[TIP][下线前Session数据(共0项)保存完成！]
2025-04-07 15:36:47.352[TIP][ConnectExit][0.0.0.0:9003]http: Server closed
2025-04-07 15:36:47.352[TIP][ListenPort]AllClosed
2025-04-07 15:38:27.478[TIP][ES2+]Starting
2025-04-07 15:38:27.496[TIP][从保存的Session数据(共0项)恢复完成！]
2025-04-07 15:38:27.496[TIP]Checking db connect...
2025-04-07 15:38:27.536[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-04-07 15:38:27.536[TIP][Generate Db entity starting]d:
2025-04-07 15:38:27.930[TIP]Generate Db entity finished
2025-04-07 15:38:27.931[TIP]----------------------------------------------
2025-04-07 15:38:27.931[TIP]ES2+ v1.0.1(k1.0.6)
2025-04-07 15:38:27.931[TIP]----------------------------------------------
2025-04-07 15:38:28.434[TIP][ES2+][1.0.1][升级时间]2025-03-21 10:39:02
2025-04-07 15:38:28.435[TIP]当前已经是最新版本
2025-04-07 15:38:28.435[TIP][ES2+][GO_TEST][es2plus]Start success, used 956 milliseconds
2025-04-07 15:38:28.436[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-04-07 15:38:29.448[TIP][RegisterInstance][0]Post "http://127.0.0.1:9000/InstanceEvent/registerInstance": dial tcp 127.0.0.1:9000: connectex: No connection could be made because the target machine actively refused it.
2025-04-07 15:38:29.448[TIP]Register Instance Fail
2025-04-07 15:38:29.448[TIP][ES2+]System will shutdown now...
2025-04-07 15:38:29.451[TIP][下线前Session数据(共0项)保存完成！]
2025-04-07 15:38:29.451[TIP][ConnectExit][0.0.0.0:9003]http: Server closed
2025-04-07 15:38:29.451[TIP][ListenPort]AllClosed
2025-04-07 16:44:43.279[TIP][从保存的Session数据(共0项)恢复完成！]
2025-04-07 16:44:43.280[TIP]Checking db connect...
2025-04-07 16:44:43.401[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-04-07 16:44:43.402[TIP][Generate Db entity starting]d:
2025-04-07 16:44:44.831[TIP]Generate Db entity finished
2025-04-07 16:44:44.831[TIP]----------------------------------------------
2025-04-07 16:44:44.831[TIP]ES2+ v1.0.1(k1.0.6)
2025-04-07 16:44:44.832[TIP]----------------------------------------------
2025-04-07 16:44:45.344[TIP][ES2+][1.0.1][升级时间]2025-03-21 10:39:02
2025-04-07 16:44:45.344[TIP]当前已经是最新版本
2025-04-07 16:44:45.345[TIP][ES2+][GO_TEST][es2plus]Start success, used 2,083 milliseconds
2025-04-07 16:44:45.345[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-04-07 16:44:46.360[TIP][RegisterInstance][0]Post "http://127.0.0.1:9000/InstanceEvent/registerInstance": dial tcp 127.0.0.1:9000: connectex: No connection could be made because the target machine actively refused it.
2025-04-07 16:44:46.360[TIP]Register Instance Fail
2025-04-07 16:44:46.360[TIP][ES2+]System will shutdown now...
2025-04-07 16:44:46.360[TIP][下线前Session数据(共0项)保存完成！]
2025-04-07 16:44:46.360[TIP][ConnectExit][0.0.0.0:9003]http: Server closed
2025-04-07 16:44:46.361[TIP][ListenPort]AllClosed
2025-04-07 16:45:05.401[TIP][从保存的Session数据(共0项)恢复完成！]
2025-04-07 16:45:05.401[TIP]Checking db connect...
2025-04-07 16:45:05.446[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-04-07 16:45:05.446[TIP][Generate Db entity starting]d:
2025-04-07 16:45:05.853[TIP]Generate Db entity finished
2025-04-07 16:45:05.853[TIP]----------------------------------------------
2025-04-07 16:45:05.854[TIP]ES2+ v1.0.1(k1.0.6)
2025-04-07 16:45:05.854[TIP]----------------------------------------------
2025-04-07 16:45:06.358[TIP][ES2+][1.0.1][升级时间]2025-03-21 10:39:02
2025-04-07 16:45:06.359[TIP]当前已经是最新版本
2025-04-07 16:45:06.359[TIP][ES2+][GO_TEST][es2plus]Start success, used 977 milliseconds
2025-04-07 16:45:06.359[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-04-07 16:45:07.376[TIP][RegisterInstance][0]Post "http://127.0.0.1:9000/InstanceEvent/registerInstance": dial tcp 127.0.0.1:9000: connectex: No connection could be made because the target machine actively refused it.
2025-04-07 16:45:07.376[TIP]Register Instance Fail
2025-04-07 16:45:07.376[TIP][ES2+]System will shutdown now...
2025-04-07 16:45:07.376[TIP][下线前Session数据(共0项)保存完成！]
2025-04-07 16:45:07.376[TIP][ConnectExit][0.0.0.0:9003]http: Server closed
2025-04-07 16:45:07.377[TIP][ListenPort]AllClosed
2025-04-07 17:04:21.543[TIP][ES2+]Starting
2025-04-07 17:04:21.559[TIP][从保存的Session数据(共0项)恢复完成！]
2025-04-07 17:04:21.559[TIP]Checking db connect...
2025-04-07 17:04:21.598[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-04-07 17:04:21.599[TIP][Generate Db entity starting]d:
2025-04-07 17:04:23.176[TIP]Generate Db entity finished
2025-04-07 17:04:23.176[TIP]----------------------------------------------
2025-04-07 17:04:23.176[TIP]ES2+ v1.0.1(k1.0.6)
2025-04-07 17:04:23.176[TIP]----------------------------------------------
2025-04-07 17:04:23.686[TIP][ES2+][1.0.1][升级时间]2025-03-21 10:39:02
2025-04-07 17:04:23.686[TIP]当前已经是最新版本
2025-04-07 17:04:23.686[TIP][ES2+][GO_TEST][es2plus]Start success, used 2,142 milliseconds
2025-04-07 17:04:23.686[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-04-07 17:04:24.887[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-04-07 17:04:24.887[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-04-07 17:04:24.887[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-04-07 17:04:24.888[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-04-07 17:04:24.888[TIP][id][50][context][admin][protocol][http][ip][127.0.0.1][port][9008]GroupInstance
2025-04-07 17:52:56.452[TIP][ES2+]Starting
2025-04-07 17:52:56.483[TIP]Checking db connect...
2025-04-07 17:52:56.583[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-04-07 17:52:56.583[TIP][Generate Db entity starting]d:
2025-04-07 17:52:56.999[TIP]Generate Db entity finished
2025-04-07 17:52:56.999[TIP]----------------------------------------------
2025-04-07 17:52:56.999[TIP]ES2+ v1.0.1(k1.0.6)
2025-04-07 17:52:56.999[TIP]----------------------------------------------
2025-04-07 17:52:57.514[TIP][ES2+][1.0.1][升级时间]2025-03-21 10:39:02
2025-04-07 17:52:57.514[TIP]当前已经是最新版本
2025-04-07 17:52:57.514[TIP][ES2+][GO_TEST][es2plus]Start success, used 1,061 milliseconds
2025-04-07 17:52:57.514[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-04-07 17:52:59.647[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-04-07 17:52:59.648[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-04-07 17:52:59.648[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-04-07 17:52:59.648[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-04-07 17:52:59.648[TIP][id][50][context][admin][protocol][http][ip][127.0.0.1][port][9008]GroupInstance
2025-04-07 18:04:44.842[TIP][ES2+]Starting
2025-04-07 18:04:44.858[TIP]Checking db connect...
2025-04-07 18:04:45.139[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-04-07 18:04:45.139[TIP][Generate Db entity starting]d:
2025-04-07 18:04:48.690[TIP]Generate Db entity finished
2025-04-07 18:04:48.690[TIP]----------------------------------------------
2025-04-07 18:04:48.690[TIP]ES2+ v1.0.1(k1.0.6)
2025-04-07 18:04:48.690[TIP]----------------------------------------------
2025-04-07 18:04:49.203[TIP][ES2+][1.0.1][升级时间]2025-03-21 10:39:02
2025-04-07 18:04:49.203[TIP]当前已经是最新版本
2025-04-07 18:04:49.203[TIP][ES2+][GO_TEST][es2plus]Start success, used 4,361 milliseconds
2025-04-07 18:04:49.203[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-04-07 18:04:50.335[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-04-07 18:04:50.335[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-04-07 18:04:50.336[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-04-07 18:04:50.336[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-04-07 18:04:50.336[TIP][id][50][context][admin][protocol][http][ip][127.0.0.1][port][9008]GroupInstance
2025-04-07 18:05:44.866[TIP]SELECT * FROM rsp_download_order_t WHERE expired_time>? or (status=9 and update_time>?) limit ?
2025-04-07 18:05:44.866[TIP][1744106744858][1744106744858]1000
2025-04-07 18:05:44.866[TIP]SELECT * FROM rsp_download_order_t WHERE expired_time>? or (status=9 and update_time>?) limit ?
2025-04-07 18:05:44.866[ERROR][doDownloadOrder]Unknown column 'status' in 'where clause'
     k/db.(*connector).handleErrorPacket(E:/go_workplace/k/db/mysql.go:1794)
     k/db.(*connector).readResultSetHeaderPacket(E:/go_workplace/k/db/mysql.go:2388)
     k/db.(*connector).Query(E:/go_workplace/k/db/mysql.go:1222)
     k/db.(*DA).Select(E:/go_workplace/k/db/da.go:142)
     k/db.SelectList[...](E:/go_workplace/k/db/da.go:235)
     esim.es2/src/bl/manager.(*DownloadOrderScheduledManager).doDownloadOrder(E:/go_workplace/esim.es2/src/bl/manager/DownloadOrderScheduledManager.go:44)
     esim.es2/src/bl/manager.(*DownloadOrderScheduledManager).Init.func1(E:/go_workplace/esim.es2/src/bl/manager/DownloadOrderScheduledManager.go:26)
     created by k/app.(E:/go_workplace/k/app/server.go:393)
     
2025-04-07 18:06:11.363[TIP]Checking db connect...
2025-04-07 18:06:11.405[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-04-07 18:06:11.406[TIP][Generate Db entity starting]d:
2025-04-07 18:06:11.890[TIP]Generate Db entity finished
2025-04-07 18:06:11.890[TIP]----------------------------------------------
2025-04-07 18:06:11.890[TIP]ES2+ v1.0.1(k1.0.6)
2025-04-07 18:06:11.890[TIP]----------------------------------------------
2025-04-07 18:06:12.403[TIP][ES2+][1.0.1][升级时间]2025-03-21 10:39:02
2025-04-07 18:06:12.403[TIP]当前已经是最新版本
2025-04-07 18:06:12.403[TIP][ES2+][GO_TEST][es2plus]Start success, used 1,058 milliseconds
2025-04-07 18:06:12.404[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-04-07 18:06:13.511[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-04-07 18:06:13.511[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-04-07 18:06:13.511[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-04-07 18:06:13.511[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-04-07 18:06:13.511[TIP][id][50][context][admin][protocol][http][ip][127.0.0.1][port][9008]GroupInstance
2025-04-08 09:14:27.105[TIP]Checking db connect...
2025-04-08 09:14:27.177[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-04-08 09:14:27.179[TIP][Generate Db entity starting]d:
2025-04-08 09:14:27.678[TIP]Generate Db entity finished
2025-04-08 09:14:27.678[TIP]----------------------------------------------
2025-04-08 09:14:27.679[TIP]ES2+ v1.0.1(k1.0.6)
2025-04-08 09:14:27.679[TIP]----------------------------------------------
2025-04-08 09:14:28.188[TIP][ES2+][1.0.1][升级时间]2025-03-21 10:39:02
2025-04-08 09:14:28.189[TIP]当前已经是最新版本
2025-04-08 09:14:28.197[TIP][ES2+][GO_TEST][es2plus]Start success, used 1,105 milliseconds
2025-04-08 09:14:28.221[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-04-08 09:14:29.221[TIP][RegisterInstance][0]Post "http://127.0.0.1:9000/InstanceEvent/registerInstance": dial tcp 127.0.0.1:9000: connectex: No connection could be made because the target machine actively refused it.
2025-04-08 09:14:29.223[TIP]Register Instance Fail
2025-04-08 09:14:29.224[TIP][ES2+]System will shutdown now...
2025-04-08 09:14:29.225[TIP][下线前Session数据(共0项)保存完成！]
2025-04-08 09:14:29.225[TIP][ConnectExit][0.0.0.0:9003]http: Server closed
2025-04-08 09:14:29.225[TIP][ListenPort]AllClosed
2025-04-08 09:15:44.512[TIP][从保存的Session数据(共0项)恢复完成！]
2025-04-08 09:15:44.513[TIP]Checking db connect...
2025-04-08 09:15:44.571[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-04-08 09:15:44.571[TIP][Generate Db entity starting]d:
2025-04-08 09:15:44.915[TIP]Generate Db entity finished
2025-04-08 09:15:44.915[TIP]----------------------------------------------
2025-04-08 09:15:44.915[TIP]ES2+ v1.0.1(k1.0.6)
2025-04-08 09:15:44.916[TIP]----------------------------------------------
2025-04-08 09:15:45.430[TIP][ES2+][1.0.1][升级时间]2025-03-21 10:39:02
2025-04-08 09:15:45.431[TIP]当前已经是最新版本
2025-04-08 09:15:45.431[TIP][ES2+][GO_TEST][es2plus]Start success, used 938 milliseconds
2025-04-08 09:15:45.431[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-04-08 09:15:46.533[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-04-08 09:15:46.533[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-04-08 09:15:46.533[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-04-08 09:15:46.533[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-04-08 09:15:46.533[TIP][id][50][context][admin][protocol][http][ip][127.0.0.1][port][9008]GroupInstance
2025-04-10 10:04:41.865[TIP]Checking db connect...
2025-04-10 10:04:41.902[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-04-10 10:04:41.902[TIP][Generate Db entity starting]d:
2025-04-10 10:04:42.300[TIP]Generate Db entity finished
2025-04-10 10:04:42.300[TIP]----------------------------------------------
2025-04-10 10:04:42.300[TIP]ES2+ v1.0.1(k1.0.6)
2025-04-10 10:04:42.300[TIP]----------------------------------------------
2025-04-10 10:04:42.813[TIP][ES2+][1.0.1][升级时间]2025-03-21 10:39:02
2025-04-10 10:04:42.814[TIP]当前已经是最新版本
2025-04-10 10:04:42.814[TIP][ES2+][GO_TEST][es2plus]Start success, used 970 milliseconds
2025-04-10 10:04:42.814[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-04-10 10:04:43.898[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-04-10 10:04:43.898[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-04-10 10:04:43.898[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-04-10 10:04:43.898[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-04-10 10:04:43.898[TIP][id][50][context][admin][protocol][http][ip][127.0.0.1][port][9008]GroupInstance
2025-04-10 10:05:41.897[TIP]Checking db connect...
2025-04-10 10:05:41.929[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-04-10 10:05:41.929[TIP][Generate Db entity starting]d:
2025-04-10 10:05:42.333[TIP]Generate Db entity finished
2025-04-10 10:05:42.334[TIP]----------------------------------------------
2025-04-10 10:05:42.334[TIP]ES2+ v1.0.1(k1.0.6)
2025-04-10 10:05:42.334[TIP]----------------------------------------------
2025-04-10 10:05:42.838[TIP][ES2+][1.0.1][升级时间]2025-03-21 10:39:02
2025-04-10 10:05:42.839[TIP]当前已经是最新版本
2025-04-10 10:05:42.839[TIP][ES2+][GO_TEST][es2plus]Start success, used 958 milliseconds
2025-04-10 10:05:42.839[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-04-10 10:05:43.911[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-04-10 10:05:43.911[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-04-10 10:05:43.911[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-04-10 10:05:43.911[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-04-10 10:05:43.911[TIP][id][50][context][admin][protocol][http][ip][127.0.0.1][port][9008]GroupInstance
2025-04-10 10:06:41.944[ERROR][doDownloadOrder]runtime error: invalid memory address or nil pointer dereference
     esim.es2/src/bl/manager.(*DownloadOrderScheduledManager).doDownloadOrder.func1(E:/go_workplace/esim.es2/src/bl/manager/DownloadOrderScheduledManager.go:36)
     panic(C:/Go/src/runtime/panic.go:770)
     esim.es2/src/bl/manager.(*DownloadOrderScheduledManager).doDownloadOrder(E:/go_workplace/esim.es2/src/bl/manager/DownloadOrderScheduledManager.go:64)
     esim.es2/src/bl/manager.(*DownloadOrderScheduledManager).Init.func1(E:/go_workplace/esim.es2/src/bl/manager/DownloadOrderScheduledManager.go:26)
     created by k/app.(E:/go_workplace/k/app/server.go:393)
     
2025-04-10 10:07:41.998[ERROR][doDownloadOrder]runtime error: invalid memory address or nil pointer dereference
     esim.es2/src/bl/manager.(*DownloadOrderScheduledManager).doDownloadOrder.func1(E:/go_workplace/esim.es2/src/bl/manager/DownloadOrderScheduledManager.go:36)
     panic(C:/Go/src/runtime/panic.go:770)
     esim.es2/src/bl/manager.(*DownloadOrderScheduledManager).doDownloadOrder(E:/go_workplace/esim.es2/src/bl/manager/DownloadOrderScheduledManager.go:64)
     esim.es2/src/bl/manager.(*DownloadOrderScheduledManager).Init.func1(E:/go_workplace/esim.es2/src/bl/manager/DownloadOrderScheduledManager.go:26)
     created by k/app.(E:/go_workplace/k/app/server.go:393)
     
2025-04-10 10:08:13.564[TIP]Checking db connect...
2025-04-10 10:08:13.601[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-04-10 10:08:13.601[TIP][Generate Db entity starting]d:
2025-04-10 10:08:13.950[TIP]Generate Db entity finished
2025-04-10 10:08:13.950[TIP]----------------------------------------------
2025-04-10 10:08:13.950[TIP]ES2+ v1.0.1(k1.0.6)
2025-04-10 10:08:13.951[TIP]----------------------------------------------
2025-04-10 10:08:14.457[TIP][ES2+][1.0.1][升级时间]2025-03-21 10:39:02
2025-04-10 10:08:14.458[TIP]当前已经是最新版本
2025-04-10 10:08:14.458[TIP][ES2+][GO_TEST][es2plus]Start success, used 912 milliseconds
2025-04-10 10:08:14.458[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-04-10 10:08:15.577[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-04-10 10:08:15.577[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-04-10 10:08:15.577[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-04-10 10:08:15.578[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-04-10 10:08:15.578[TIP][id][50][context][admin][protocol][http][ip][127.0.0.1][port][9008]GroupInstance
2025-04-10 10:10:31.989[TIP]Checking db connect...
2025-04-10 10:10:32.026[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-04-10 10:10:32.026[TIP][Generate Db entity starting]d:
2025-04-10 10:10:32.429[TIP]Generate Db entity finished
2025-04-10 10:10:32.429[TIP]----------------------------------------------
2025-04-10 10:10:32.429[TIP]ES2+ v1.0.1(k1.0.6)
2025-04-10 10:10:32.429[TIP]----------------------------------------------
2025-04-10 10:10:32.936[TIP][ES2+][1.0.1][升级时间]2025-03-21 10:39:02
2025-04-10 10:10:32.937[TIP]当前已经是最新版本
2025-04-10 10:10:32.937[TIP][ES2+][GO_TEST][es2plus]Start success, used 965 milliseconds
2025-04-10 10:10:32.937[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-04-10 10:10:34.057[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-04-10 10:10:34.057[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-04-10 10:10:34.058[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-04-10 10:10:34.058[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-04-10 10:10:34.058[TIP][id][50][context][admin][protocol][http][ip][127.0.0.1][port][9008]GroupInstance
2025-04-10 10:12:56.054[TIP][ES2+]Starting
2025-04-10 10:12:56.071[TIP]Checking db connect...
2025-04-10 10:12:56.106[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-04-10 10:12:56.106[TIP][Generate Db entity starting]d:
2025-04-10 10:12:56.460[TIP]Generate Db entity finished
2025-04-10 10:12:56.461[TIP]----------------------------------------------
2025-04-10 10:12:56.461[TIP]ES2+ v1.0.1(k1.0.6)
2025-04-10 10:12:56.461[TIP]----------------------------------------------
2025-04-10 10:12:56.966[TIP][ES2+][1.0.1][升级时间]2025-03-21 10:39:02
2025-04-10 10:12:56.966[TIP]当前已经是最新版本
2025-04-10 10:12:56.966[TIP][ES2+][GO_TEST][es2plus]Start success, used 911 milliseconds
2025-04-10 10:12:56.966[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-04-10 10:12:58.115[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-04-10 10:12:58.115[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-04-10 10:12:58.115[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-04-10 10:12:58.116[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-04-10 10:12:58.116[TIP][id][50][context][admin][protocol][http][ip][127.0.0.1][port][9008]GroupInstance
2025-04-10 10:25:42.000[TIP]Checking db connect...
2025-04-10 10:25:42.086[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-04-10 10:25:42.087[TIP][Generate Db entity starting]d:
2025-04-10 10:25:42.465[TIP]Generate Db entity finished
2025-04-10 10:25:42.465[TIP]----------------------------------------------
2025-04-10 10:25:42.465[TIP]ES2+ v1.0.1(k1.0.6)
2025-04-10 10:25:42.465[TIP]----------------------------------------------
2025-04-10 10:25:42.974[TIP][ES2+][1.0.1][升级时间]2025-03-21 10:39:02
2025-04-10 10:25:42.975[TIP]当前已经是最新版本
2025-04-10 10:25:42.975[TIP][ES2+][GO_TEST][es2plus]Start success, used 991 milliseconds
2025-04-10 10:25:42.975[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-04-10 10:25:44.126[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-04-10 10:25:44.126[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-04-10 10:25:44.126[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-04-10 10:25:44.126[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-04-10 10:25:44.126[TIP][id][50][context][admin][protocol][http][ip][127.0.0.1][port][9008]GroupInstance
2025-04-10 10:26:42.040[ERROR][doDownloadOrder]runtime error: invalid memory address or nil pointer dereference
     esim.es2/src/bl/manager.(*DownloadOrderScheduledManager).doDownloadOrder.func1(E:/go_workplace/esim.es2/src/bl/manager/DownloadOrderScheduledManager.go:36)
     panic(C:/Go/src/runtime/panic.go:770)
     esim.es2/src/bl/manager.(*DownloadOrderScheduledManager).doDownloadOrder(E:/go_workplace/esim.es2/src/bl/manager/DownloadOrderScheduledManager.go:66)
     esim.es2/src/bl/manager.(*DownloadOrderScheduledManager).Init.func1(E:/go_workplace/esim.es2/src/bl/manager/DownloadOrderScheduledManager.go:26)
     created by k/app.(E:/go_workplace/k/app/server.go:393)
     
2025-04-10 10:27:42.075[ERROR][doDownloadOrder]runtime error: invalid memory address or nil pointer dereference
     esim.es2/src/bl/manager.(*DownloadOrderScheduledManager).doDownloadOrder.func1(E:/go_workplace/esim.es2/src/bl/manager/DownloadOrderScheduledManager.go:36)
     panic(C:/Go/src/runtime/panic.go:770)
     esim.es2/src/bl/manager.(*DownloadOrderScheduledManager).doDownloadOrder(E:/go_workplace/esim.es2/src/bl/manager/DownloadOrderScheduledManager.go:66)
     esim.es2/src/bl/manager.(*DownloadOrderScheduledManager).Init.func1(E:/go_workplace/esim.es2/src/bl/manager/DownloadOrderScheduledManager.go:26)
     created by k/app.(E:/go_workplace/k/app/server.go:393)
     
2025-04-10 10:33:41.016[TIP]Checking db connect...
2025-04-10 10:33:41.053[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-04-10 10:33:41.054[TIP][Generate Db entity starting]d:
2025-04-10 10:33:41.470[TIP]Generate Db entity finished
2025-04-10 10:33:41.470[TIP]----------------------------------------------
2025-04-10 10:33:41.470[TIP]ES2+ v1.0.1(k1.0.6)
2025-04-10 10:33:41.470[TIP]----------------------------------------------
2025-04-10 10:33:41.984[TIP][ES2+][1.0.1][升级时间]2025-03-21 10:39:02
2025-04-10 10:33:41.984[TIP]当前已经是最新版本
2025-04-10 10:33:41.984[TIP][ES2+][GO_TEST][es2plus]Start success, used 993 milliseconds
2025-04-10 10:33:41.985[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-04-10 10:33:43.139[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-04-10 10:33:43.140[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-04-10 10:33:43.140[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-04-10 10:33:43.140[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-04-10 10:33:43.140[TIP][id][50][context][admin][protocol][http][ip][127.0.0.1][port][9008]GroupInstance
2025-04-10 10:36:35.274[TIP]Checking db connect...
2025-04-10 10:36:35.367[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-04-10 10:36:35.367[TIP][Generate Db entity starting]d:
2025-04-10 10:36:35.815[TIP]Generate Db entity finished
2025-04-10 10:36:35.815[TIP]----------------------------------------------
2025-04-10 10:36:35.816[TIP]ES2+ v1.0.1(k1.0.6)
2025-04-10 10:36:35.816[TIP]----------------------------------------------
2025-04-10 10:36:36.328[TIP][ES2+][1.0.1][升级时间]2025-03-21 10:39:02
2025-04-10 10:36:36.328[TIP]当前已经是最新版本
2025-04-10 10:36:36.329[TIP][ES2+][GO_TEST][es2plus]Start success, used 1,072 milliseconds
2025-04-10 10:36:36.330[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-04-10 10:36:37.455[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-04-10 10:36:37.455[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-04-10 10:36:37.455[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-04-10 10:36:37.456[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-04-10 10:36:37.456[TIP][id][50][context][admin][protocol][http][ip][127.0.0.1][port][9008]GroupInstance
2025-04-10 19:48:50.102[TIP]Checking db connect...
2025-04-10 19:48:50.142[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-04-10 19:48:50.143[TIP][Generate Db entity starting]d:
2025-04-10 19:48:50.549[TIP]Generate Db entity finished
2025-04-10 19:48:50.549[TIP]----------------------------------------------
2025-04-10 19:48:50.549[TIP]ES2+ v1.0.1(k1.0.6)
2025-04-10 19:48:50.549[TIP]----------------------------------------------
2025-04-10 19:48:51.059[TIP][ES2+][1.0.1][升级时间]2025-03-21 10:39:02
2025-04-10 19:48:51.059[TIP]当前已经是最新版本
2025-04-10 19:48:51.059[TIP][ES2+][GO_TEST][es2plus]Start success, used 973 milliseconds
2025-04-10 19:48:51.059[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-04-10 19:48:52.073[TIP][RegisterInstance][0]Post "http://127.0.0.1:9000/InstanceEvent/registerInstance": dial tcp 127.0.0.1:9000: connectex: No connection could be made because the target machine actively refused it.
2025-04-10 19:48:52.073[TIP]Register Instance Fail
2025-04-10 19:48:52.073[TIP][ES2+]System will shutdown now...
2025-04-10 19:48:52.073[TIP][下线前Session数据(共0项)保存完成！]
2025-04-10 19:48:52.073[TIP][ConnectExit][0.0.0.0:9003]http: Server closed
2025-04-10 19:48:52.073[TIP][ListenPort]AllClosed
2025-04-11 16:17:44.026[TIP][从保存的Session数据(共0项)恢复完成！]
2025-04-11 16:17:44.026[TIP]Checking db connect...
2025-04-11 16:17:44.063[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-04-11 16:17:44.063[TIP]----------------------------------------------
2025-04-11 16:17:44.064[TIP]ES2+ v1.0.1(k1.0.6)
2025-04-11 16:17:44.064[TIP]----------------------------------------------
2025-04-11 16:17:44.566[TIP][ES2+][1.0.1][升级时间]2025-03-21 10:39:02
2025-04-11 16:17:44.566[TIP]当前已经是最新版本
2025-04-11 16:17:44.567[TIP][ES2+][GO_TEST][es2plus]Start success, used 562 milliseconds
2025-04-11 16:17:44.567[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-04-11 16:17:45.671[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-04-11 16:17:45.671[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-04-11 16:17:45.671[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-04-11 16:17:45.671[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-04-11 16:17:45.671[TIP][id][50][context][admin][protocol][http][ip][127.0.0.1][port][9008]GroupInstance
2025-04-11 16:18:44.099[TIP][update rsp_download_order_t set locked_by=44 where download_id in(91,92,93,96,97,98,99,103,104,105,106,107,108,110,112,113,115,116,117,118,120,121,123,142,143,144,156,157,158,159,160,161,162,163,170,171,172,173,174,175,176,177,178,179)]statement(update rsp_download_order_t set locked_by=44 where download_id in(91,92,93,96,97,98,99,103,104,105,106,107,108,110,112,113,115,116,117,118,120,121,123,142,143,144,156,157,158,159,160,161,162,163,170,171,172,173,174,175,176,177,178,179)) is invalid, params(0) and values(1) count not equal
2025-04-11 16:18:44.099[TIP]91,92,93,96,97,98,99,103,104,105,106,107,108,110,112,113,115,116,117,118,120,121,123,142,143,144,156,157,158,159,160,161,162,163,170,171,172,173,174,175,176,177,178,179
2025-04-11 16:19:40.545[TIP]Checking db connect...
2025-04-11 16:19:40.581[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_dp, User=dp
2025-04-11 16:19:40.582[TIP]----------------------------------------------
2025-04-11 16:19:40.582[TIP]ES2+ v1.0.1(k1.0.6)
2025-04-11 16:19:40.582[TIP]----------------------------------------------
2025-04-11 16:19:41.096[TIP][ES2+][1.0.1][升级时间]2025-03-21 10:39:02
2025-04-11 16:19:41.096[TIP]当前已经是最新版本
2025-04-11 16:19:41.096[TIP][ES2+][GO_TEST][es2plus]Start success, used 575 milliseconds
2025-04-11 16:19:41.097[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-04-11 16:19:42.188[TIP][id][42][context][][protocol][http][ip][127.0.0.1][port][9000]GroupInstance
2025-04-11 16:19:42.188[TIP][id][43][context][es9plus][protocol][http][ip][127.0.0.1][port][9001]GroupInstance
2025-04-11 16:19:42.188[TIP]register with instanceId[44], group[GO_TEST], ip[127.0.0.1] success!
2025-04-11 16:19:42.188[TIP][id][47][context][transfer][protocol][http][ip][127.0.0.1][port][9004]GroupInstance
2025-04-11 16:19:42.188[TIP][id][50][context][admin][protocol][http][ip][127.0.0.1][port][9008]GroupInstance
2025-04-24 15:27:18.367[TIP]Checking db connect...
2025-04-24 15:27:18.464[ERROR]Unknown database 'smdp_dp'
     k/db.(*connector).handleErrorPacket(E:/go_workplace/k/db/mysql.go:1811)
     k/db.(*connector).readAuthResult(E:/go_workplace/k/db/mysql.go:2451)
     k/db.(*connector).handleAuthResult(E:/go_workplace/k/db/mysql.go:2226)
     k/db.(*connector).dial(E:/go_workplace/k/db/mysql.go:814)
     k/db.(*MySql).Connect(E:/go_workplace/k/db/mysql.go:750)
     k/db.(*MySql).InitPool.func1(E:/go_workplace/k/db/mysql.go:381)
     k/cache.(*PoolUnit[...]).get(E:/go_workplace/k/cache/pool.go:166)
     k/cache.(*Pool[...]).Get(E:/go_workplace/k/cache/pool.go:102)
     k/db.(*DA).CheckDb(E:/go_workplace/k/db/da.go:1003)
     k/app.(*App).checkFeature(E:/go_workplace/k/app/app.go:264)
2025-04-24 15:27:18.465[TIP][Db connect check fail]Please reconfigure in web...
2025-04-24 15:27:18.466[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-05-13 10:59:44.407[TIP]Checking db connect...
2025-05-13 10:59:44.527[ERROR]Unknown database 'smdp_dp'
     k/db.(*connector).handleErrorPacket(E:/go_workplace/k/db/mysql.go:1811)
     k/db.(*connector).readAuthResult(E:/go_workplace/k/db/mysql.go:2451)
     k/db.(*connector).handleAuthResult(E:/go_workplace/k/db/mysql.go:2226)
     k/db.(*connector).dial(E:/go_workplace/k/db/mysql.go:814)
     k/db.(*MySql).Connect(E:/go_workplace/k/db/mysql.go:750)
     k/db.(*MySql).InitPool.func1(E:/go_workplace/k/db/mysql.go:381)
     k/cache.(*PoolUnit[...]).get(E:/go_workplace/k/cache/pool.go:166)
     k/cache.(*Pool[...]).Get(E:/go_workplace/k/cache/pool.go:102)
     k/db.(*DA).CheckDb(E:/go_workplace/k/db/da.go:1003)
     k/app.(*App).checkFeature(E:/go_workplace/k/app/app.go:264)
2025-05-13 10:59:44.527[TIP][Db connect check fail]Please reconfigure in web...
2025-05-13 10:59:44.527[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-05-13 11:00:09.932[TIP]Checking db connect...
2025-05-13 11:00:09.966[ERROR]Unknown database 'smdp_dp'
     k/db.(*connector).handleErrorPacket(E:/go_workplace/k/db/mysql.go:1811)
     k/db.(*connector).readAuthResult(E:/go_workplace/k/db/mysql.go:2451)
     k/db.(*connector).handleAuthResult(E:/go_workplace/k/db/mysql.go:2226)
     k/db.(*connector).dial(E:/go_workplace/k/db/mysql.go:814)
     k/db.(*MySql).Connect(E:/go_workplace/k/db/mysql.go:750)
     k/db.(*MySql).InitPool.func1(E:/go_workplace/k/db/mysql.go:381)
     k/cache.(*PoolUnit[...]).get(E:/go_workplace/k/cache/pool.go:166)
     k/cache.(*Pool[...]).Get(E:/go_workplace/k/cache/pool.go:102)
     k/db.(*DA).CheckDb(E:/go_workplace/k/db/da.go:1003)
     k/app.(*App).checkFeature(E:/go_workplace/k/app/app.go:264)
2025-05-13 11:00:09.966[TIP][Db connect check fail]Please reconfigure in web...
2025-05-13 11:00:09.966[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-05-13 11:00:18.541[TIP]Checking db connect...
2025-05-13 11:00:18.598[ERROR]Unknown database 'smdp_dp'
     k/db.(*connector).handleErrorPacket(E:/go_workplace/k/db/mysql.go:1811)
     k/db.(*connector).readAuthResult(E:/go_workplace/k/db/mysql.go:2451)
     k/db.(*connector).handleAuthResult(E:/go_workplace/k/db/mysql.go:2226)
     k/db.(*connector).dial(E:/go_workplace/k/db/mysql.go:814)
     k/db.(*MySql).Connect(E:/go_workplace/k/db/mysql.go:750)
     k/db.(*MySql).InitPool.func1(E:/go_workplace/k/db/mysql.go:381)
     k/cache.(*PoolUnit[...]).get(E:/go_workplace/k/cache/pool.go:166)
     k/cache.(*Pool[...]).Get(E:/go_workplace/k/cache/pool.go:102)
     k/db.(*DA).CheckDb(E:/go_workplace/k/db/da.go:1003)
     k/app.(*App).checkFeature(E:/go_workplace/k/app/app.go:264)
2025-05-13 11:00:18.599[TIP][Db connect check fail]Please reconfigure in web...
2025-05-13 11:00:18.599[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-06-16 10:27:22.234[TIP][ES2+]Starting
2025-06-16 10:27:22.249[TIP][Read db properties fail]open E:\go_workplace\esim.es2\.db: The system cannot find the file specified.
2025-06-16 10:27:22.249[TIP]Check database param fail
2025-06-16 10:27:22.249[TIP]Please input database host ip:
2025-06-16 10:28:49.220[TIP]EOF
2025-06-16 10:32:08.270[TIP][ES2+]Starting
2025-06-16 10:32:08.285[TIP]Checking db connect...
2025-06-16 10:32:08.360[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_test, User=dp
2025-06-16 10:32:08.360[TIP][Generate Db entity starting]E:\\go_workplace\\esim.common\\src\\entity\\dp
2025-06-16 10:32:09.027[TIP]Generate Db entity finished
2025-06-16 10:32:09.027[TIP]----------------------------------------------
2025-06-16 10:32:09.028[TIP]ES2+ v1.0.2(k1.0.6)
2025-06-16 10:32:09.028[TIP]----------------------------------------------
2025-06-16 10:32:09.540[TIP][ES2+][1.0.2][升级时间]2025-05-15 21:54:24
2025-06-16 10:32:09.540[TIP]当前已经是最新版本
2025-06-16 10:32:09.540[TIP][ES2+][GO_TEST][es2plus]Start success, used 1,270 milliseconds
2025-06-16 10:32:09.540[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-06-16 10:32:10.689[TIP][Instance][42][G][GO_TEST][http][127.0.0.1][9000][http][][0][C][hub][N][][W][1][D][hub-local-test]Group
2025-06-16 10:32:10.689[TIP][Instance][43][G][GO_TEST][http][127.0.0.1][9001][http][][0][C][es9plus][N][][W][1][D][es9-00]Group
2025-06-16 10:32:10.691[TIP][Instance][44][G][GO_TEST][http][127.0.0.1][9003][http][][0][C][es2plus][N][][W][1][D][es9-00]Register
2025-06-16 10:32:10.691[TIP][Instance][47][G][GO_TEST][http][127.0.0.1][9004][http][][0][C][transfer][N][][W][1][D][DP.Transfer]Group
2025-06-16 10:32:10.691[TIP][Instance][50][G][GO_TEST][http][127.0.0.1][9008][http][][0][C][admin][N][][W][1][D][smdp-admin-api]Group
2025-06-16 10:32:10.691[TIP][Instance.Post][Offline][47][transfer][DP.Transfer]http://127.0.0.1:9004/transfer/InstanceEvent/broadcast
2025-06-16 10:32:10.692[TIP][RetryInstance.Empty][Instance][47][transfer][DP.Transfer][URI]InstanceEvent/broadcast
2025-06-16 10:32:10.693[TIP][Instance.Post][Offline][43][es9plus][es9-00]http://127.0.0.1:9001/es9plus/InstanceEvent/broadcast
2025-06-16 10:32:10.694[TIP][RetryInstance.Empty][Instance][43][es9plus][es9-00][URI]InstanceEvent/broadcast
2025-06-16 10:32:10.694[TIP][Instance.Post][Offline][50][admin][smdp-admin-api]http://127.0.0.1:9008/admin/InstanceEvent/broadcast
2025-06-16 10:32:10.696[TIP][RetryInstance.Empty][Instance][50][admin][smdp-admin-api][URI]InstanceEvent/broadcast
2025-06-16 10:34:43.791[TIP]Checking db connect...
2025-06-16 10:34:43.865[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_test, User=dp
2025-06-16 10:34:43.866[TIP]----------------------------------------------
2025-06-16 10:34:43.866[TIP]ES2+ v1.0.2(k1.0.6)
2025-06-16 10:34:43.866[TIP]----------------------------------------------
2025-06-16 10:34:44.381[TIP][ES2+][1.0.2][升级时间]2025-05-15 21:54:24
2025-06-16 10:34:44.382[TIP]当前已经是最新版本
2025-06-16 10:34:44.382[TIP][ES2+][GO_TEST][es2plus]Start success, used 608 milliseconds
2025-06-16 10:34:44.382[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-06-16 10:34:45.614[TIP][Instance][42][G][GO_TEST][http][127.0.0.1][9000][http][][0][C][hub][N][][W][1][D][hub-local-test]Group
2025-06-16 10:34:45.614[TIP][Instance][43][G][GO_TEST][http][127.0.0.1][9001][http][][0][C][es9plus][N][][W][1][D][es9-00]Group
2025-06-16 10:34:45.614[TIP][Instance][44][G][GO_TEST][http][127.0.0.1][9003][http][][0][C][es2plus][N][][W][1][D][es9-00]Register
2025-06-16 10:34:45.614[TIP][Instance][47][G][GO_TEST][http][127.0.0.1][9004][http][][0][C][transfer][N][][W][1][D][DP.Transfer]Group
2025-06-16 10:34:45.614[TIP][Instance][50][G][GO_TEST][http][127.0.0.1][9008][http][][0][C][admin][N][][W][1][D][smdp-admin-api]Group
2025-06-16 10:34:45.615[TIP][Instance.Post][Offline][43][es9plus][es9-00]http://127.0.0.1:9001/es9plus/InstanceEvent/broadcast
2025-06-16 10:34:45.616[TIP][RetryInstance.Empty][Instance][43][es9plus][es9-00][URI]InstanceEvent/broadcast
2025-06-16 10:34:45.616[TIP][Instance.Post][Offline][50][admin][smdp-admin-api]http://127.0.0.1:9008/admin/InstanceEvent/broadcast
2025-06-16 10:34:45.616[TIP][RetryInstance.Empty][Instance][50][admin][smdp-admin-api][URI]InstanceEvent/broadcast
2025-06-16 10:34:45.616[TIP][Instance.Post][Offline][47][transfer][DP.Transfer]http://127.0.0.1:9004/transfer/InstanceEvent/broadcast
2025-06-16 10:34:45.616[TIP][RetryInstance.Empty][Instance][47][transfer][DP.Transfer][URI]InstanceEvent/broadcast
2025-06-16 10:35:13.979[TIP]Checking db connect...
2025-06-16 10:35:14.075[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_test, User=dp
2025-06-16 10:35:14.075[TIP]----------------------------------------------
2025-06-16 10:35:14.075[TIP]ES2+ v1.0.2(k1.0.6)
2025-06-16 10:35:14.075[TIP]----------------------------------------------
2025-06-16 10:35:14.579[TIP][ES2+][1.0.2][升级时间]2025-05-15 21:54:24
2025-06-16 10:35:14.579[TIP]当前已经是最新版本
2025-06-16 10:35:14.580[TIP][ES2+][GO_TEST][es2plus]Start success, used 619 milliseconds
2025-06-16 10:35:14.580[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-06-16 10:35:15.713[TIP][Instance][42][G][GO_TEST][http][127.0.0.1][9000][http][][0][C][hub][N][][W][1][D][hub-local-test]Group
2025-06-16 10:35:15.713[TIP][Instance][43][G][GO_TEST][http][127.0.0.1][9001][http][][0][C][es9plus][N][][W][1][D][es9-00]Group
2025-06-16 10:35:15.713[TIP][Instance][44][G][GO_TEST][http][127.0.0.1][9003][http][][0][C][es2plus][N][][W][1][D][es9-00]Register
2025-06-16 10:35:15.713[TIP][Instance][47][G][GO_TEST][http][127.0.0.1][9004][http][][0][C][transfer][N][][W][1][D][DP.Transfer]Group
2025-06-16 10:35:15.714[TIP][Instance][50][G][GO_TEST][http][127.0.0.1][9008][http][][0][C][admin][N][][W][1][D][smdp-admin-api]Group
2025-06-16 10:35:15.714[TIP][Instance.Post][Offline][50][admin][smdp-admin-api]http://127.0.0.1:9008/admin/InstanceEvent/broadcast
2025-06-16 10:35:15.714[TIP][RetryInstance.Empty][Instance][50][admin][smdp-admin-api][URI]InstanceEvent/broadcast
2025-06-16 10:35:15.714[TIP][Instance.Post][Offline][43][es9plus][es9-00]http://127.0.0.1:9001/es9plus/InstanceEvent/broadcast
2025-06-16 10:35:15.714[TIP][RetryInstance.Empty][Instance][43][es9plus][es9-00][URI]InstanceEvent/broadcast
2025-06-16 10:35:15.714[TIP][Instance.Post][Offline][47][transfer][DP.Transfer]http://127.0.0.1:9004/transfer/InstanceEvent/broadcast
2025-06-16 10:35:15.715[TIP][RetryInstance.Empty][Instance][47][transfer][DP.Transfer][URI]InstanceEvent/broadcast
2025-06-16 10:36:14.057[ERROR][DownloadOrderReport]MySQL(1264).Query.Error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '=0x1430b20 GROUP BY tenant_id,type_id,state,local_state,mode' at line 1SELECT tenant_id as tenantId,local_state as localState,state as state,mode,type_id as typeId,COUNT(1) as num,0 as category FROM rsp_download_order_t where create_time &lt;=0x1430b20 GROUP BY tenant_id,type_id,state,local_state,mode

     k/db.(*connector).handleErrorPacket(E:/go_workplace/k/db/mysql.go:1811)
     k/db.(*connector).readResultSetHeaderPacket(E:/go_workplace/k/db/mysql.go:2405)
     k/db.(*connector).Query(E:/go_workplace/k/db/mysql.go:1229)
     k/db.(*DA).Select(E:/go_workplace/k/db/da.go:146)
     esim.es2/src/bl/manager.(*DownloadOrderReportManager).doDownloadOrderReport(E:/go_workplace/esim.es2/src/bl/manager/DownloadOrderReportManager.go:65)
     esim.es2/src/bl/manager.(*DownloadOrderReportManager).Init.func1(E:/go_workplace/esim.es2/src/bl/manager/DownloadOrderReportManager.go:34)
     created by k/app.(E:/go_workplace/k/app/server.go:368)
     
2025-06-16 10:36:57.936[TIP]Checking db connect...
2025-06-16 10:36:58.024[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_test, User=dp
2025-06-16 10:36:58.025[TIP]----------------------------------------------
2025-06-16 10:36:58.025[TIP]ES2+ v1.0.2(k1.0.6)
2025-06-16 10:36:58.025[TIP]----------------------------------------------
2025-06-16 10:36:58.539[TIP][ES2+][1.0.2][升级时间]2025-05-15 21:54:24
2025-06-16 10:36:58.539[TIP]当前已经是最新版本
2025-06-16 10:36:58.539[TIP][ES2+][GO_TEST][es2plus]Start success, used 620 milliseconds
2025-06-16 10:36:58.539[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-06-16 10:36:59.673[TIP][Instance][42][G][GO_TEST][http][127.0.0.1][9000][http][][0][C][hub][N][][W][1][D][hub-local-test]Group
2025-06-16 10:36:59.673[TIP][Instance][43][G][GO_TEST][http][127.0.0.1][9001][http][][0][C][es9plus][N][][W][1][D][es9-00]Group
2025-06-16 10:36:59.673[TIP][Instance][44][G][GO_TEST][http][127.0.0.1][9003][http][][0][C][es2plus][N][][W][1][D][es9-00]Register
2025-06-16 10:36:59.673[TIP][Instance][47][G][GO_TEST][http][127.0.0.1][9004][http][][0][C][transfer][N][][W][1][D][DP.Transfer]Group
2025-06-16 10:36:59.673[TIP][Instance][50][G][GO_TEST][http][127.0.0.1][9008][http][][0][C][admin][N][][W][1][D][smdp-admin-api]Group
2025-06-16 10:36:59.673[TIP][Instance.Post][Offline][50][admin][smdp-admin-api]http://127.0.0.1:9008/admin/InstanceEvent/broadcast
2025-06-16 10:36:59.673[TIP][RetryInstance.Empty][Instance][50][admin][smdp-admin-api][URI]InstanceEvent/broadcast
2025-06-16 10:36:59.674[TIP][Instance.Post][Offline][43][es9plus][es9-00]http://127.0.0.1:9001/es9plus/InstanceEvent/broadcast
2025-06-16 10:36:59.674[TIP][RetryInstance.Empty][Instance][43][es9plus][es9-00][URI]InstanceEvent/broadcast
2025-06-16 10:36:59.674[TIP][Instance.Post][Offline][47][transfer][DP.Transfer]http://127.0.0.1:9004/transfer/InstanceEvent/broadcast
2025-06-16 10:36:59.674[TIP][RetryInstance.Empty][Instance][47][transfer][DP.Transfer][URI]InstanceEvent/broadcast
2025-06-16 10:38:47.988[TIP]Checking db connect...
2025-06-16 10:38:48.071[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_test, User=dp
2025-06-16 10:38:48.071[TIP]----------------------------------------------
2025-06-16 10:38:48.072[TIP]ES2+ v1.0.2(k1.0.6)
2025-06-16 10:38:48.072[TIP]----------------------------------------------
2025-06-16 10:38:48.573[TIP][ES2+][1.0.2][升级时间]2025-05-15 21:54:24
2025-06-16 10:38:48.573[TIP]当前已经是最新版本
2025-06-16 10:38:48.573[TIP][ES2+][GO_TEST][es2plus]Start success, used 605 milliseconds
2025-06-16 10:38:48.574[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-06-16 10:38:49.681[TIP][Instance][42][G][GO_TEST][http][127.0.0.1][9000][http][][0][C][hub][N][][W][1][D][hub-local-test]Group
2025-06-16 10:38:49.681[TIP][Instance][43][G][GO_TEST][http][127.0.0.1][9001][http][][0][C][es9plus][N][][W][1][D][es9-00]Group
2025-06-16 10:38:49.682[TIP][Instance][44][G][GO_TEST][http][127.0.0.1][9003][http][][0][C][es2plus][N][][W][1][D][es9-00]Register
2025-06-16 10:38:49.682[TIP][Instance][47][G][GO_TEST][http][127.0.0.1][9004][http][][0][C][transfer][N][][W][1][D][DP.Transfer]Group
2025-06-16 10:38:49.682[TIP][Instance][50][G][GO_TEST][http][127.0.0.1][9008][http][][0][C][admin][N][][W][1][D][smdp-admin-api]Group
2025-06-16 10:38:49.682[TIP][Instance.Post][Offline][43][es9plus][es9-00]http://127.0.0.1:9001/es9plus/InstanceEvent/broadcast
2025-06-16 10:38:49.682[TIP][RetryInstance.Empty][Instance][43][es9plus][es9-00][URI]InstanceEvent/broadcast
2025-06-16 10:38:49.682[TIP][Instance.Post][Offline][50][admin][smdp-admin-api]http://127.0.0.1:9008/admin/InstanceEvent/broadcast
2025-06-16 10:38:49.683[TIP][RetryInstance.Empty][Instance][50][admin][smdp-admin-api][URI]InstanceEvent/broadcast
2025-06-16 10:38:49.683[TIP][Instance.Post][Offline][47][transfer][DP.Transfer]http://127.0.0.1:9004/transfer/InstanceEvent/broadcast
2025-06-16 10:38:49.683[TIP][RetryInstance.Empty][Instance][47][transfer][DP.Transfer][URI]InstanceEvent/broadcast
2025-06-16 10:44:31.902[TIP][ES2+]Starting
2025-06-16 10:44:31.919[TIP]Checking db connect...
2025-06-16 10:44:32.006[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_test, User=dp
2025-06-16 10:44:32.007[TIP][Generate Db entity starting]E:\\go_workplace\\esim.common\\src\\entity\\dp
2025-06-16 10:44:32.660[TIP]Generate Db entity finished
2025-06-16 10:44:32.660[TIP]----------------------------------------------
2025-06-16 10:44:32.660[TIP]ES2+ v1.0.2(k1.0.6)
2025-06-16 10:44:32.660[TIP]----------------------------------------------
2025-06-16 10:44:33.169[TIP][ES2+][1.0.2][升级时间]2025-05-15 21:54:24
2025-06-16 10:44:33.169[TIP]当前已经是最新版本
2025-06-16 10:44:33.169[TIP][ES2+][GO_TEST][es2plus]Start success, used 1,267 milliseconds
2025-06-16 10:44:33.169[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-06-16 10:44:34.340[TIP][Instance][42][G][GO_TEST][http][127.0.0.1][9000][http][][0][C][hub][N][][W][1][D][hub-local-test]Group
2025-06-16 10:44:34.343[TIP][Instance][43][G][GO_TEST][http][127.0.0.1][9001][http][][0][C][es9plus][N][][W][1][D][es9-00]Group
2025-06-16 10:44:34.343[TIP][Instance][44][G][GO_TEST][http][127.0.0.1][9003][http][][0][C][es2plus][N][][W][1][D][es9-00]Register
2025-06-16 10:44:34.343[TIP][Instance][47][G][GO_TEST][http][127.0.0.1][9004][http][][0][C][transfer][N][][W][1][D][DP.Transfer]Group
2025-06-16 10:44:34.343[TIP][Instance][50][G][GO_TEST][http][127.0.0.1][9008][http][][0][C][admin][N][][W][1][D][smdp-admin-api]Group
2025-06-16 10:44:34.344[TIP][Instance.Post][Offline][43][es9plus][es9-00]http://127.0.0.1:9001/es9plus/InstanceEvent/broadcast
2025-06-16 10:44:34.344[TIP][RetryInstance.Empty][Instance][43][es9plus][es9-00][URI]InstanceEvent/broadcast
2025-06-16 10:44:34.344[TIP][Instance.Post][Offline][50][admin][smdp-admin-api]http://127.0.0.1:9008/admin/InstanceEvent/broadcast
2025-06-16 10:44:34.344[TIP][RetryInstance.Empty][Instance][50][admin][smdp-admin-api][URI]InstanceEvent/broadcast
2025-06-16 10:44:34.344[TIP][Instance.Post][Offline][47][transfer][DP.Transfer]http://127.0.0.1:9004/transfer/InstanceEvent/broadcast
2025-06-16 10:44:34.344[TIP][RetryInstance.Empty][Instance][47][transfer][DP.Transfer][URI]InstanceEvent/broadcast
2025-06-16 10:50:34.459[TIP]Checking db connect...
2025-06-16 10:50:34.539[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_test, User=dp
2025-06-16 10:50:34.539[TIP][Generate Db entity starting]E:\\go_workplace\\esim.common\\src\\entity\\dp
2025-06-16 10:50:35.244[TIP]Generate Db entity finished
2025-06-16 10:50:35.244[TIP]----------------------------------------------
2025-06-16 10:50:35.244[TIP]ES2+ v1.0.2(k1.0.6)
2025-06-16 10:50:35.244[TIP]----------------------------------------------
2025-06-16 10:50:35.750[TIP][ES2+][1.0.2][升级时间]2025-05-15 21:54:24
2025-06-16 10:50:35.750[TIP]当前已经是最新版本
2025-06-16 10:50:35.750[TIP][ES2+][GO_TEST][es2plus]Start success, used 1,308 milliseconds
2025-06-16 10:50:35.750[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-06-16 10:50:36.906[TIP][Instance][42][G][GO_TEST][http][127.0.0.1][9000][http][][0][C][hub][N][][W][1][D][hub-local-test]Group
2025-06-16 10:50:36.907[TIP][Instance][43][G][GO_TEST][http][127.0.0.1][9001][http][][0][C][es9plus][N][][W][1][D][es9-00]Group
2025-06-16 10:50:36.907[TIP][Instance][44][G][GO_TEST][http][127.0.0.1][9003][http][][0][C][es2plus][N][][W][1][D][es9-00]Register
2025-06-16 10:50:36.907[TIP][Instance][47][G][GO_TEST][http][127.0.0.1][9004][http][][0][C][transfer][N][][W][1][D][DP.Transfer]Group
2025-06-16 10:50:36.907[TIP][Instance][50][G][GO_TEST][http][127.0.0.1][9008][http][][0][C][admin][N][][W][1][D][smdp-admin-api]Group
2025-06-16 10:50:36.908[TIP][Instance.Post][Offline][43][es9plus][es9-00]http://127.0.0.1:9001/es9plus/InstanceEvent/broadcast
2025-06-16 10:50:36.908[TIP][RetryInstance.Empty][Instance][43][es9plus][es9-00][URI]InstanceEvent/broadcast
2025-06-16 10:50:36.908[TIP][Instance.Post][Offline][50][admin][smdp-admin-api]http://127.0.0.1:9008/admin/InstanceEvent/broadcast
2025-06-16 10:50:36.909[TIP][RetryInstance.Empty][Instance][50][admin][smdp-admin-api][URI]InstanceEvent/broadcast
2025-06-16 10:50:36.909[TIP][Instance.Post][Offline][47][transfer][DP.Transfer]http://127.0.0.1:9004/transfer/InstanceEvent/broadcast
2025-06-16 10:50:36.909[TIP][RetryInstance.Empty][Instance][47][transfer][DP.Transfer][URI]InstanceEvent/broadcast
2025-06-16 10:51:04.109[TIP]Checking db connect...
2025-06-16 10:51:04.176[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_test, User=dp
2025-06-16 10:51:04.177[TIP]----------------------------------------------
2025-06-16 10:51:04.177[TIP]ES2+ v1.0.2(k1.0.6)
2025-06-16 10:51:04.177[TIP]----------------------------------------------
2025-06-16 10:51:04.677[TIP][ES2+][1.0.2][升级时间]2025-05-15 21:54:24
2025-06-16 10:51:04.677[TIP]当前已经是最新版本
2025-06-16 10:51:04.678[TIP][ES2+][GO_TEST][es2plus]Start success, used 585 milliseconds
2025-06-16 10:51:04.678[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-06-16 10:51:05.872[TIP][Instance][42][G][GO_TEST][http][127.0.0.1][9000][http][][0][C][hub][N][][W][1][D][hub-local-test]Group
2025-06-16 10:51:05.873[TIP][Instance][43][G][GO_TEST][http][127.0.0.1][9001][http][][0][C][es9plus][N][][W][1][D][es9-00]Group
2025-06-16 10:51:05.873[TIP][Instance][44][G][GO_TEST][http][127.0.0.1][9003][http][][0][C][es2plus][N][][W][1][D][es9-00]Register
2025-06-16 10:51:05.873[TIP][Instance][47][G][GO_TEST][http][127.0.0.1][9004][http][][0][C][transfer][N][][W][1][D][DP.Transfer]Group
2025-06-16 10:51:05.873[TIP][Instance][50][G][GO_TEST][http][127.0.0.1][9008][http][][0][C][admin][N][][W][1][D][smdp-admin-api]Group
2025-06-16 10:51:05.873[TIP][Instance.Post][Offline][43][es9plus][es9-00]http://127.0.0.1:9001/es9plus/InstanceEvent/broadcast
2025-06-16 10:51:05.873[TIP][RetryInstance.Empty][Instance][43][es9plus][es9-00][URI]InstanceEvent/broadcast
2025-06-16 10:51:05.874[TIP][Instance.Post][Offline][50][admin][smdp-admin-api]http://127.0.0.1:9008/admin/InstanceEvent/broadcast
2025-06-16 10:51:05.874[TIP][RetryInstance.Empty][Instance][50][admin][smdp-admin-api][URI]InstanceEvent/broadcast
2025-06-16 10:51:05.874[TIP][Instance.Post][Offline][47][transfer][DP.Transfer]http://127.0.0.1:9004/transfer/InstanceEvent/broadcast
2025-06-16 10:51:05.874[TIP][RetryInstance.Empty][Instance][47][transfer][DP.Transfer][URI]InstanceEvent/broadcast
2025-06-16 10:58:25.100[TIP]Checking db connect...
2025-06-16 10:58:25.175[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_test, User=dp
2025-06-16 10:58:25.175[TIP]----------------------------------------------
2025-06-16 10:58:25.175[TIP]ES2+ v1.0.2(k1.0.6)
2025-06-16 10:58:25.175[TIP]----------------------------------------------
2025-06-16 10:58:25.689[TIP][ES2+][1.0.2][升级时间]2025-05-15 21:54:24
2025-06-16 10:58:25.690[TIP]当前已经是最新版本
2025-06-16 10:58:25.690[TIP][ES2+][GO_TEST][es2plus]Start success, used 608 milliseconds
2025-06-16 10:58:25.690[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-06-16 10:58:26.938[TIP][Instance][42][G][GO_TEST][http][127.0.0.1][9000][http][][0][C][hub][N][][W][1][D][hub-local-test]Group
2025-06-16 10:58:26.939[TIP][Instance][43][G][GO_TEST][http][127.0.0.1][9001][http][][0][C][es9plus][N][][W][1][D][es9-00]Group
2025-06-16 10:58:26.939[TIP][Instance][44][G][GO_TEST][http][127.0.0.1][9003][http][][0][C][es2plus][N][][W][1][D][es9-00]Register
2025-06-16 10:58:26.939[TIP][Instance][47][G][GO_TEST][http][127.0.0.1][9004][http][][0][C][transfer][N][][W][1][D][DP.Transfer]Group
2025-06-16 10:58:26.939[TIP][Instance][50][G][GO_TEST][http][127.0.0.1][9008][http][][0][C][admin][N][][W][1][D][smdp-admin-api]Group
2025-06-16 10:58:26.939[TIP][Instance.Post][Offline][50][admin][smdp-admin-api]http://127.0.0.1:9008/admin/InstanceEvent/broadcast
2025-06-16 10:58:26.939[TIP][RetryInstance.Empty][Instance][50][admin][smdp-admin-api][URI]InstanceEvent/broadcast
2025-06-16 10:58:26.939[TIP][Instance.Post][Offline][43][es9plus][es9-00]http://127.0.0.1:9001/es9plus/InstanceEvent/broadcast
2025-06-16 10:58:26.939[TIP][RetryInstance.Empty][Instance][43][es9plus][es9-00][URI]InstanceEvent/broadcast
2025-06-16 10:58:26.940[TIP][Instance.Post][Offline][47][transfer][DP.Transfer]http://127.0.0.1:9004/transfer/InstanceEvent/broadcast
2025-06-16 10:58:26.940[TIP][RetryInstance.Empty][Instance][47][transfer][DP.Transfer][URI]InstanceEvent/broadcast
2025-06-16 10:58:42.489[TIP]Checking db connect...
2025-06-16 10:58:42.575[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_test, User=dp
2025-06-16 10:58:42.575[TIP]----------------------------------------------
2025-06-16 10:58:42.575[TIP]ES2+ v1.0.2(k1.0.6)
2025-06-16 10:58:42.575[TIP]----------------------------------------------
2025-06-16 10:58:43.081[TIP][ES2+][1.0.2][升级时间]2025-05-15 21:54:24
2025-06-16 10:58:43.081[TIP]当前已经是最新版本
2025-06-16 10:58:43.082[TIP][ES2+][GO_TEST][es2plus]Start success, used 609 milliseconds
2025-06-16 10:58:43.082[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-06-16 10:58:44.202[TIP][Instance][42][G][GO_TEST][http][127.0.0.1][9000][http][][0][C][hub][N][][W][1][D][hub-local-test]Group
2025-06-16 10:58:44.203[TIP][Instance][43][G][GO_TEST][http][127.0.0.1][9001][http][][0][C][es9plus][N][][W][1][D][es9-00]Group
2025-06-16 10:58:44.203[TIP][Instance][44][G][GO_TEST][http][127.0.0.1][9003][http][][0][C][es2plus][N][][W][1][D][es9-00]Register
2025-06-16 10:58:44.203[TIP][Instance][47][G][GO_TEST][http][127.0.0.1][9004][http][][0][C][transfer][N][][W][1][D][DP.Transfer]Group
2025-06-16 10:58:44.203[TIP][Instance][50][G][GO_TEST][http][127.0.0.1][9008][http][][0][C][admin][N][][W][1][D][smdp-admin-api]Group
2025-06-16 10:58:44.203[TIP][Instance.Post][Offline][50][admin][smdp-admin-api]http://127.0.0.1:9008/admin/InstanceEvent/broadcast
2025-06-16 10:58:44.204[TIP][Instance.Post][Offline][43][es9plus][es9-00]http://127.0.0.1:9001/es9plus/InstanceEvent/broadcast
2025-06-16 10:58:44.204[TIP][RetryInstance.Empty][Instance][50][admin][smdp-admin-api][URI]InstanceEvent/broadcast
2025-06-16 10:58:44.204[TIP][RetryInstance.Empty][Instance][43][es9plus][es9-00][URI]InstanceEvent/broadcast
2025-06-16 10:58:44.204[TIP][Instance.Post][Offline][47][transfer][DP.Transfer]http://127.0.0.1:9004/transfer/InstanceEvent/broadcast
2025-06-16 10:58:44.205[TIP][RetryInstance.Empty][Instance][47][transfer][DP.Transfer][URI]InstanceEvent/broadcast
2025-06-16 10:59:00.653[TIP][ES2+]Starting
2025-06-16 10:59:00.671[TIP]Checking db connect...
2025-06-16 10:59:00.750[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_test, User=dp
2025-06-16 10:59:00.750[TIP]----------------------------------------------
2025-06-16 10:59:00.750[TIP]ES2+ v1.0.2(k1.0.6)
2025-06-16 10:59:00.751[TIP]----------------------------------------------
2025-06-16 10:59:01.251[TIP][ES2+][1.0.2][升级时间]2025-05-15 21:54:24
2025-06-16 10:59:01.251[TIP]当前已经是最新版本
2025-06-16 10:59:01.251[TIP][ES2+][GO_TEST][es2plus]Start success, used 597 milliseconds
2025-06-16 10:59:01.251[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-06-16 10:59:02.474[TIP][Instance][42][G][GO_TEST][http][127.0.0.1][9000][http][][0][C][hub][N][][W][1][D][hub-local-test]Group
2025-06-16 10:59:02.474[TIP][Instance][43][G][GO_TEST][http][127.0.0.1][9001][http][][0][C][es9plus][N][][W][1][D][es9-00]Group
2025-06-16 10:59:02.474[TIP][Instance][44][G][GO_TEST][http][127.0.0.1][9003][http][][0][C][es2plus][N][][W][1][D][es9-00]Register
2025-06-16 10:59:02.475[TIP][Instance][47][G][GO_TEST][http][127.0.0.1][9004][http][][0][C][transfer][N][][W][1][D][DP.Transfer]Group
2025-06-16 10:59:02.475[TIP][Instance][50][G][GO_TEST][http][127.0.0.1][9008][http][][0][C][admin][N][][W][1][D][smdp-admin-api]Group
2025-06-16 10:59:02.475[TIP][Instance.Post][Offline][47][transfer][DP.Transfer]http://127.0.0.1:9004/transfer/InstanceEvent/broadcast
2025-06-16 10:59:02.475[TIP][RetryInstance.Empty][Instance][47][transfer][DP.Transfer][URI]InstanceEvent/broadcast
2025-06-16 10:59:02.476[TIP][Instance.Post][Offline][50][admin][smdp-admin-api]http://127.0.0.1:9008/admin/InstanceEvent/broadcast
2025-06-16 10:59:02.476[TIP][RetryInstance.Empty][Instance][50][admin][smdp-admin-api][URI]InstanceEvent/broadcast
2025-06-16 10:59:02.476[TIP][Instance.Post][Offline][43][es9plus][es9-00]http://127.0.0.1:9001/es9plus/InstanceEvent/broadcast
2025-06-16 10:59:02.476[TIP][RetryInstance.Empty][Instance][43][es9plus][es9-00][URI]InstanceEvent/broadcast
2025-06-16 11:04:36.645[TIP]Checking db connect...
2025-06-16 11:04:36.729[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_test, User=dp
2025-06-16 11:04:36.729[TIP]----------------------------------------------
2025-06-16 11:04:36.730[TIP]ES2+ v1.0.2(k1.0.6)
2025-06-16 11:04:36.730[TIP]----------------------------------------------
2025-06-16 11:04:37.244[TIP][ES2+][1.0.2][升级时间]2025-05-15 21:54:24
2025-06-16 11:04:37.244[TIP]当前已经是最新版本
2025-06-16 11:04:37.245[TIP][ES2+][GO_TEST][es2plus]Start success, used 624 milliseconds
2025-06-16 11:04:37.245[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-06-16 11:04:38.433[TIP][Instance][42][G][GO_TEST][http][127.0.0.1][9000][http][][0][C][hub][N][][W][1][D][hub-local-test]Group
2025-06-16 11:04:38.433[TIP][Instance][43][G][GO_TEST][http][127.0.0.1][9001][http][][0][C][es9plus][N][][W][1][D][es9-00]Group
2025-06-16 11:04:38.433[TIP][Instance][44][G][GO_TEST][http][127.0.0.1][9003][http][][0][C][es2plus][N][][W][1][D][es9-00]Register
2025-06-16 11:04:38.433[TIP][Instance][47][G][GO_TEST][http][127.0.0.1][9004][http][][0][C][transfer][N][][W][1][D][DP.Transfer]Group
2025-06-16 11:04:38.433[TIP][Instance][50][G][GO_TEST][http][127.0.0.1][9008][http][][0][C][admin][N][][W][1][D][smdp-admin-api]Group
2025-06-16 11:04:38.433[TIP][Instance.Post][Offline][43][es9plus][es9-00]http://127.0.0.1:9001/es9plus/InstanceEvent/broadcast
2025-06-16 11:04:38.434[TIP][RetryInstance.Empty][Instance][43][es9plus][es9-00][URI]InstanceEvent/broadcast
2025-06-16 11:04:38.434[TIP][Instance.Post][Offline][50][admin][smdp-admin-api]http://127.0.0.1:9008/admin/InstanceEvent/broadcast
2025-06-16 11:04:38.434[TIP][RetryInstance.Empty][Instance][50][admin][smdp-admin-api][URI]InstanceEvent/broadcast
2025-06-16 11:04:38.434[TIP][Instance.Post][Offline][47][transfer][DP.Transfer]http://127.0.0.1:9004/transfer/InstanceEvent/broadcast
2025-06-16 11:04:38.434[TIP][RetryInstance.Empty][Instance][47][transfer][DP.Transfer][URI]InstanceEvent/broadcast
2025-06-16 11:06:50.288[TIP][ES2+]Starting
2025-06-16 11:06:50.303[TIP]Checking db connect...
2025-06-16 11:06:50.441[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_test, User=dp
2025-06-16 11:06:50.441[TIP]----------------------------------------------
2025-06-16 11:06:50.441[TIP]ES2+ v1.0.2(k1.0.6)
2025-06-16 11:06:50.441[TIP]----------------------------------------------
2025-06-16 11:06:50.955[TIP][ES2+][1.0.2][升级时间]2025-05-15 21:54:24
2025-06-16 11:06:50.956[TIP]当前已经是最新版本
2025-06-16 11:06:50.956[TIP][ES2+][GO_TEST][es2plus]Start success, used 668 milliseconds
2025-06-16 11:06:50.957[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-06-16 11:06:52.014[TIP][Instance][42][G][GO_TEST][http][127.0.0.1][9000][http][][0][C][hub][N][][W][1][D][hub-local-test]Group
2025-06-16 11:06:52.014[TIP][Instance][43][G][GO_TEST][http][127.0.0.1][9001][http][][0][C][es9plus][N][][W][1][D][es9-00]Group
2025-06-16 11:06:52.014[TIP][Instance][44][G][GO_TEST][http][127.0.0.1][9003][http][][0][C][es2plus][N][][W][1][D][es9-00]Register
2025-06-16 11:06:52.014[TIP][Instance][47][G][GO_TEST][http][127.0.0.1][9004][http][][0][C][transfer][N][][W][1][D][DP.Transfer]Group
2025-06-16 11:06:52.014[TIP][Instance][50][G][GO_TEST][http][127.0.0.1][9008][http][][0][C][admin][N][][W][1][D][smdp-admin-api]Group
2025-06-16 11:06:52.014[TIP][Instance.Post][Offline][50][admin][smdp-admin-api]http://127.0.0.1:9008/admin/InstanceEvent/broadcast
2025-06-16 11:06:52.014[TIP][RetryInstance.Empty][Instance][50][admin][smdp-admin-api][URI]InstanceEvent/broadcast
2025-06-16 11:06:52.015[TIP][Instance.Post][Offline][43][es9plus][es9-00]http://127.0.0.1:9001/es9plus/InstanceEvent/broadcast
2025-06-16 11:06:52.015[TIP][RetryInstance.Empty][Instance][43][es9plus][es9-00][URI]InstanceEvent/broadcast
2025-06-16 11:06:52.015[TIP][Instance.Post][Offline][47][transfer][DP.Transfer]http://127.0.0.1:9004/transfer/InstanceEvent/broadcast
2025-06-16 11:06:52.016[TIP][RetryInstance.Empty][Instance][47][transfer][DP.Transfer][URI]InstanceEvent/broadcast
2025-06-16 11:07:09.997[TIP][ES2+]Starting
2025-06-16 11:07:10.014[TIP]Checking db connect...
2025-06-16 11:07:10.106[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_test, User=dp
2025-06-16 11:07:10.106[TIP]----------------------------------------------
2025-06-16 11:07:10.106[TIP]ES2+ v1.0.2(k1.0.6)
2025-06-16 11:07:10.107[TIP]----------------------------------------------
2025-06-16 11:07:10.610[TIP][ES2+][1.0.2][升级时间]2025-05-15 21:54:24
2025-06-16 11:07:10.610[TIP]当前已经是最新版本
2025-06-16 11:07:10.610[TIP][ES2+][GO_TEST][es2plus]Start success, used 613 milliseconds
2025-06-16 11:07:10.610[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-06-16 11:07:11.718[TIP][Instance][42][G][GO_TEST][http][127.0.0.1][9000][http][][0][C][hub][N][][W][1][D][hub-local-test]Group
2025-06-16 11:07:11.718[TIP][Instance][43][G][GO_TEST][http][127.0.0.1][9001][http][][0][C][es9plus][N][][W][1][D][es9-00]Group
2025-06-16 11:07:11.718[TIP][Instance][44][G][GO_TEST][http][127.0.0.1][9003][http][][0][C][es2plus][N][][W][1][D][es9-00]Register
2025-06-16 11:07:11.719[TIP][Instance][47][G][GO_TEST][http][127.0.0.1][9004][http][][0][C][transfer][N][][W][1][D][DP.Transfer]Group
2025-06-16 11:07:11.719[TIP][Instance][50][G][GO_TEST][http][127.0.0.1][9008][http][][0][C][admin][N][][W][1][D][smdp-admin-api]Group
2025-06-16 11:07:11.719[TIP][Instance.Post][Offline][43][es9plus][es9-00]http://127.0.0.1:9001/es9plus/InstanceEvent/broadcast
2025-06-16 11:07:11.719[TIP][RetryInstance.Empty][Instance][43][es9plus][es9-00][URI]InstanceEvent/broadcast
2025-06-16 11:07:11.719[TIP][Instance.Post][Offline][47][transfer][DP.Transfer]http://127.0.0.1:9004/transfer/InstanceEvent/broadcast
2025-06-16 11:07:11.719[TIP][RetryInstance.Empty][Instance][47][transfer][DP.Transfer][URI]InstanceEvent/broadcast
2025-06-16 11:07:11.720[TIP][Instance.Post][Offline][50][admin][smdp-admin-api]http://127.0.0.1:9008/admin/InstanceEvent/broadcast
2025-06-16 11:07:11.720[TIP][RetryInstance.Empty][Instance][50][admin][smdp-admin-api][URI]InstanceEvent/broadcast
2025-06-16 11:23:50.706[TIP]Checking db connect...
2025-06-16 11:23:50.856[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_test, User=dp
2025-06-16 11:23:50.856[TIP]----------------------------------------------
2025-06-16 11:23:50.857[TIP]ES2+ v1.0.2(k1.0.6)
2025-06-16 11:23:50.857[TIP]----------------------------------------------
2025-06-16 11:23:51.360[TIP][ES2+][1.0.2][升级时间]2025-05-15 21:54:24
2025-06-16 11:23:51.360[TIP]当前已经是最新版本
2025-06-16 11:23:51.360[TIP][ES2+][GO_TEST][es2plus]Start success, used 677 milliseconds
2025-06-16 11:23:51.361[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-06-16 11:23:52.505[TIP][Instance][42][G][GO_TEST][http][127.0.0.1][9000][http][][0][C][hub][N][][W][1][D][hub-local-test]Group
2025-06-16 11:23:52.505[TIP][Instance][43][G][GO_TEST][http][127.0.0.1][9001][http][][0][C][es9plus][N][][W][1][D][es9-00]Group
2025-06-16 11:23:52.505[TIP][Instance][44][G][GO_TEST][http][127.0.0.1][9003][http][][0][C][es2plus][N][][W][1][D][es9-00]Register
2025-06-16 11:23:52.505[TIP][Instance][47][G][GO_TEST][http][127.0.0.1][9004][http][][0][C][transfer][N][][W][1][D][DP.Transfer]Group
2025-06-16 11:23:52.506[TIP][Instance][50][G][GO_TEST][http][127.0.0.1][9008][http][][0][C][admin][N][][W][1][D][smdp-admin-api]Group
2025-06-16 11:23:52.506[TIP][Instance.Post][Offline][43][es9plus][es9-00]http://127.0.0.1:9001/es9plus/InstanceEvent/broadcast
2025-06-16 11:23:52.506[TIP][RetryInstance.Empty][Instance][43][es9plus][es9-00][URI]InstanceEvent/broadcast
2025-06-16 11:23:52.506[TIP][Instance.Post][Offline][50][admin][smdp-admin-api]http://127.0.0.1:9008/admin/InstanceEvent/broadcast
2025-06-16 11:23:52.506[TIP][RetryInstance.Empty][Instance][50][admin][smdp-admin-api][URI]InstanceEvent/broadcast
2025-06-16 11:23:52.506[TIP][Instance.Post][Offline][47][transfer][DP.Transfer]http://127.0.0.1:9004/transfer/InstanceEvent/broadcast
2025-06-16 11:23:52.506[TIP][RetryInstance.Empty][Instance][47][transfer][DP.Transfer][URI]InstanceEvent/broadcast
2025-06-16 12:04:30.719[TIP]Checking db connect...
2025-06-16 12:04:30.815[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_test, User=dp
2025-06-16 12:04:30.815[TIP][Generate Db entity starting]E:\\go_workplace\\esim.common\\src\\entity\\dp
2025-06-16 12:04:31.540[TIP]Generate Db entity finished
2025-06-16 12:04:31.540[TIP]----------------------------------------------
2025-06-16 12:04:31.540[TIP]ES2+ v1.0.2(k1.0.6)
2025-06-16 12:04:31.540[TIP]----------------------------------------------
2025-06-16 12:04:32.055[TIP][ES2+][1.0.2][升级时间]2025-05-15 21:54:24
2025-06-16 12:04:32.055[TIP]当前已经是最新版本
2025-06-16 12:04:32.056[TIP][ES2+][GO_TEST][es2plus]Start success, used 1,353 milliseconds
2025-06-16 12:04:32.056[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-06-16 12:04:33.220[TIP][Instance][42][G][GO_TEST][http][127.0.0.1][9000][http][][0][C][hub][N][][W][1][D][hub-local-test]Group
2025-06-16 12:04:33.220[TIP][Instance][43][G][GO_TEST][http][127.0.0.1][9001][http][][0][C][es9plus][N][][W][1][D][es9-00]Group
2025-06-16 12:04:33.220[TIP][Instance][44][G][GO_TEST][http][127.0.0.1][9003][http][][0][C][es2plus][N][][W][1][D][es9-00]Register
2025-06-16 12:04:33.221[TIP][Instance][47][G][GO_TEST][http][127.0.0.1][9004][http][][0][C][transfer][N][][W][1][D][DP.Transfer]Group
2025-06-16 12:04:33.221[TIP][Instance][50][G][GO_TEST][http][127.0.0.1][9008][http][][0][C][admin][N][][W][1][D][smdp-admin-api]Group
2025-06-16 12:04:33.224[TIP][Instance.Post][Offline][47][transfer][DP.Transfer]http://127.0.0.1:9004/transfer/InstanceEvent/broadcast
2025-06-16 12:04:33.224[TIP][RetryInstance.Empty][Instance][47][transfer][DP.Transfer][URI]InstanceEvent/broadcast
2025-06-16 12:04:33.224[TIP][Instance.Post][Offline][50][admin][smdp-admin-api]http://127.0.0.1:9008/admin/InstanceEvent/broadcast
2025-06-16 12:04:33.224[TIP][RetryInstance.Empty][Instance][50][admin][smdp-admin-api][URI]InstanceEvent/broadcast
2025-06-16 12:04:33.224[TIP][Instance.Post][Offline][43][es9plus][es9-00]http://127.0.0.1:9001/es9plus/InstanceEvent/broadcast
2025-06-16 12:04:33.224[TIP][RetryInstance.Empty][Instance][43][es9plus][es9-00][URI]InstanceEvent/broadcast
2025-06-16 12:07:25.338[TIP][ES2+]Starting
2025-06-16 12:07:25.356[TIP]Checking db connect...
2025-06-16 12:07:25.452[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_test, User=dp
2025-06-16 12:07:25.452[TIP][Generate Db entity starting]E:\\go_workplace\\esim.common\\src\\entity\\dp
2025-06-16 12:07:26.158[TIP]Generate Db entity finished
2025-06-16 12:07:26.158[TIP]----------------------------------------------
2025-06-16 12:07:26.159[TIP]ES2+ v1.0.2(k1.0.6)
2025-06-16 12:07:26.159[TIP]----------------------------------------------
2025-06-16 12:07:26.667[TIP][ES2+][1.0.2][升级时间]2025-05-15 21:54:24
2025-06-16 12:07:26.667[TIP]当前已经是最新版本
2025-06-16 12:07:26.667[TIP][ES2+][GO_TEST][es2plus]Start success, used 1,329 milliseconds
2025-06-16 12:07:26.667[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-06-16 12:07:27.786[TIP][Instance][42][G][GO_TEST][http][127.0.0.1][9000][http][][0][C][hub][N][][W][1][D][hub-local-test]Group
2025-06-16 12:07:27.786[TIP][Instance][43][G][GO_TEST][http][127.0.0.1][9001][http][][0][C][es9plus][N][][W][1][D][es9-00]Group
2025-06-16 12:07:27.786[TIP][Instance][44][G][GO_TEST][http][127.0.0.1][9003][http][][0][C][es2plus][N][][W][1][D][es9-00]Register
2025-06-16 12:07:27.786[TIP][Instance][47][G][GO_TEST][http][127.0.0.1][9004][http][][0][C][transfer][N][][W][1][D][DP.Transfer]Group
2025-06-16 12:07:27.786[TIP][Instance][50][G][GO_TEST][http][127.0.0.1][9008][http][][0][C][admin][N][][W][1][D][smdp-admin-api]Group
2025-06-16 12:07:27.787[TIP][Instance.Post][Offline][47][transfer][DP.Transfer]http://127.0.0.1:9004/transfer/InstanceEvent/broadcast
2025-06-16 12:07:27.788[TIP][RetryInstance.Empty][Instance][47][transfer][DP.Transfer][URI]InstanceEvent/broadcast
2025-06-16 12:07:27.790[TIP][Instance.Post][Offline][43][es9plus][es9-00]http://127.0.0.1:9001/es9plus/InstanceEvent/broadcast
2025-06-16 12:07:27.791[TIP][RetryInstance.Empty][Instance][43][es9plus][es9-00][URI]InstanceEvent/broadcast
2025-06-16 12:07:27.791[TIP][Instance.Post][Offline][50][admin][smdp-admin-api]http://127.0.0.1:9008/admin/InstanceEvent/broadcast
2025-06-16 12:07:27.791[TIP][RetryInstance.Empty][Instance][50][admin][smdp-admin-api][URI]InstanceEvent/broadcast
2025-06-16 12:10:54.859[TIP][ES2+]Starting
2025-06-16 12:10:54.876[TIP]Checking db connect...
2025-06-16 12:10:55.093[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_test, User=dp
2025-06-16 12:10:55.093[TIP][Generate Db entity starting]E:\\go_workplace\\esim.common\\src\\entity\\dp
2025-06-16 12:10:55.821[TIP]Generate Db entity finished
2025-06-16 12:10:55.821[TIP]----------------------------------------------
2025-06-16 12:10:55.821[TIP]ES2+ v1.0.2(k1.0.6)
2025-06-16 12:10:55.821[TIP]----------------------------------------------
2025-06-16 12:10:56.323[TIP][ES2+][1.0.2][升级时间]2025-05-15 21:54:24
2025-06-16 12:10:56.324[TIP]当前已经是最新版本
2025-06-16 12:10:56.324[TIP][ES2+][GO_TEST][es2plus]Start success, used 1,464 milliseconds
2025-06-16 12:10:56.324[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-06-16 12:10:57.488[TIP][Instance][42][G][GO_TEST][http][127.0.0.1][9000][http][][0][C][hub][N][][W][1][D][hub-local-test]Group
2025-06-16 12:10:57.488[TIP][Instance][43][G][GO_TEST][http][127.0.0.1][9001][http][][0][C][es9plus][N][][W][1][D][es9-00]Group
2025-06-16 12:10:57.488[TIP][Instance][44][G][GO_TEST][http][127.0.0.1][9003][http][][0][C][es2plus][N][][W][1][D][es9-00]Register
2025-06-16 12:10:57.488[TIP][Instance][47][G][GO_TEST][http][127.0.0.1][9004][http][][0][C][transfer][N][][W][1][D][DP.Transfer]Group
2025-06-16 12:10:57.489[TIP][Instance][50][G][GO_TEST][http][127.0.0.1][9008][http][][0][C][admin][N][][W][1][D][smdp-admin-api]Group
2025-06-16 12:10:57.489[TIP][Instance.Post][Offline][43][es9plus][es9-00]http://127.0.0.1:9001/es9plus/InstanceEvent/broadcast
2025-06-16 12:10:57.491[TIP][RetryInstance.Empty][Instance][43][es9plus][es9-00][URI]InstanceEvent/broadcast
2025-06-16 12:10:57.491[TIP][Instance.Post][Offline][47][transfer][DP.Transfer]http://127.0.0.1:9004/transfer/InstanceEvent/broadcast
2025-06-16 12:10:57.491[TIP][RetryInstance.Empty][Instance][47][transfer][DP.Transfer][URI]InstanceEvent/broadcast
2025-06-16 12:10:57.491[TIP][Instance.Post][Offline][50][admin][smdp-admin-api]http://127.0.0.1:9008/admin/InstanceEvent/broadcast
2025-06-16 12:10:57.491[TIP][RetryInstance.Empty][Instance][50][admin][smdp-admin-api][URI]InstanceEvent/broadcast
2025-06-16 12:14:01.272[TIP]Checking db connect...
2025-06-16 12:14:01.361[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_test, User=dp
2025-06-16 12:14:01.361[TIP][Generate Db entity starting]E:\\go_workplace\\esim.common\\src\\entity\\dp
2025-06-16 12:14:02.225[TIP]Generate Db entity finished
2025-06-16 12:14:02.225[TIP]----------------------------------------------
2025-06-16 12:14:02.225[TIP]ES2+ v1.0.2(k1.0.6)
2025-06-16 12:14:02.225[TIP]----------------------------------------------
2025-06-16 12:14:02.736[TIP][ES2+][1.0.2][升级时间]2025-05-15 21:54:24
2025-06-16 12:14:02.736[TIP]当前已经是最新版本
2025-06-16 12:14:02.737[TIP][ES2+][GO_TEST][es2plus]Start success, used 1,484 milliseconds
2025-06-16 12:14:02.737[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-06-16 12:14:03.969[TIP][Instance][42][G][GO_TEST][http][127.0.0.1][9000][http][][0][C][hub][N][][W][1][D][hub-local-test]Group
2025-06-16 12:14:03.969[TIP][Instance][43][G][GO_TEST][http][127.0.0.1][9001][http][][0][C][es9plus][N][][W][1][D][es9-00]Group
2025-06-16 12:14:03.969[TIP][Instance][44][G][GO_TEST][http][127.0.0.1][9003][http][][0][C][es2plus][N][][W][1][D][es9-00]Register
2025-06-16 12:14:03.969[TIP][Instance][47][G][GO_TEST][http][127.0.0.1][9004][http][][0][C][transfer][N][][W][1][D][DP.Transfer]Group
2025-06-16 12:14:03.971[TIP][Instance][50][G][GO_TEST][http][127.0.0.1][9008][http][][0][C][admin][N][][W][1][D][smdp-admin-api]Group
2025-06-16 12:14:03.972[TIP][Instance.Post][Offline][47][transfer][DP.Transfer]http://127.0.0.1:9004/transfer/InstanceEvent/broadcast
2025-06-16 12:14:03.972[TIP][RetryInstance.Empty][Instance][47][transfer][DP.Transfer][URI]InstanceEvent/broadcast
2025-06-16 12:14:03.972[TIP][Instance.Post][Offline][50][admin][smdp-admin-api]http://127.0.0.1:9008/admin/InstanceEvent/broadcast
2025-06-16 12:14:03.972[TIP][RetryInstance.Empty][Instance][50][admin][smdp-admin-api][URI]InstanceEvent/broadcast
2025-06-16 12:14:03.972[TIP][Instance.Post][Offline][43][es9plus][es9-00]http://127.0.0.1:9001/es9plus/InstanceEvent/broadcast
2025-06-16 12:14:03.973[TIP][RetryInstance.Empty][Instance][43][es9plus][es9-00][URI]InstanceEvent/broadcast
2025-06-16 12:28:24.508[TIP]Checking db connect...
2025-06-16 12:28:24.623[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_test, User=dp
2025-06-16 12:28:24.623[TIP]----------------------------------------------
2025-06-16 12:28:24.623[TIP]ES2+ v1.0.2(k1.0.6)
2025-06-16 12:28:24.623[TIP]----------------------------------------------
2025-06-16 12:28:25.130[TIP][ES2+][1.0.2][升级时间]2025-05-15 21:54:24
2025-06-16 12:28:25.130[TIP]当前已经是最新版本
2025-06-16 12:28:25.130[TIP][ES2+][GO_TEST][es2plus]Start success, used 641 milliseconds
2025-06-16 12:28:25.130[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-06-16 12:28:26.295[TIP][Instance][42][G][GO_TEST][http][127.0.0.1][9000][http][][0][C][hub][N][][W][1][D][hub-local-test]Group
2025-06-16 12:28:26.296[TIP][Instance][43][G][GO_TEST][http][127.0.0.1][9001][http][][0][C][es9plus][N][][W][1][D][es9-00]Group
2025-06-16 12:28:26.296[TIP][Instance][44][G][GO_TEST][http][127.0.0.1][9003][http][][0][C][es2plus][N][][W][1][D][es9-00]Register
2025-06-16 12:28:26.296[TIP][Instance][47][G][GO_TEST][http][127.0.0.1][9004][http][][0][C][transfer][N][][W][1][D][DP.Transfer]Group
2025-06-16 12:28:26.296[TIP][Instance][50][G][GO_TEST][http][127.0.0.1][9008][http][][0][C][admin][N][][W][1][D][smdp-admin-api]Group
2025-06-16 12:28:26.296[TIP][Instance.Post][Offline][50][admin][smdp-admin-api]http://127.0.0.1:9008/admin/InstanceEvent/broadcast
2025-06-16 12:28:26.296[TIP][RetryInstance.Empty][Instance][50][admin][smdp-admin-api][URI]InstanceEvent/broadcast
2025-06-16 12:28:26.297[TIP][Instance.Post][Offline][47][transfer][DP.Transfer]http://127.0.0.1:9004/transfer/InstanceEvent/broadcast
2025-06-16 12:28:26.297[TIP][RetryInstance.Empty][Instance][47][transfer][DP.Transfer][URI]InstanceEvent/broadcast
2025-06-16 12:28:26.297[TIP][Instance.Post][Offline][43][es9plus][es9-00]http://127.0.0.1:9001/es9plus/InstanceEvent/broadcast
2025-06-16 12:28:26.297[TIP][RetryInstance.Empty][Instance][43][es9plus][es9-00][URI]InstanceEvent/broadcast
2025-06-16 12:31:16.440[TIP]Checking db connect...
2025-06-16 12:31:16.555[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_test, User=dp
2025-06-16 12:31:16.555[TIP]----------------------------------------------
2025-06-16 12:31:16.555[TIP]ES2+ v1.0.2(k1.0.6)
2025-06-16 12:31:16.556[TIP]----------------------------------------------
2025-06-16 12:31:17.064[TIP][ES2+][1.0.2][升级时间]2025-05-15 21:54:24
2025-06-16 12:31:17.064[TIP]当前已经是最新版本
2025-06-16 12:31:17.064[TIP][ES2+][GO_TEST][es2plus]Start success, used 646 milliseconds
2025-06-16 12:31:17.064[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-06-16 12:31:18.199[TIP][Instance][42][G][GO_TEST][http][127.0.0.1][9000][http][][0][C][hub][N][][W][1][D][hub-local-test]Group
2025-06-16 12:31:18.200[TIP][Instance][43][G][GO_TEST][http][127.0.0.1][9001][http][][0][C][es9plus][N][][W][1][D][es9-00]Group
2025-06-16 12:31:18.200[TIP][Instance][44][G][GO_TEST][http][127.0.0.1][9003][http][][0][C][es2plus][N][][W][1][D][es9-00]Register
2025-06-16 12:31:18.200[TIP][Instance][47][G][GO_TEST][http][127.0.0.1][9004][http][][0][C][transfer][N][][W][1][D][DP.Transfer]Group
2025-06-16 12:31:18.201[TIP][Instance][50][G][GO_TEST][http][127.0.0.1][9008][http][][0][C][admin][N][][W][1][D][smdp-admin-api]Group
2025-06-16 12:31:18.201[TIP][Instance.Post][Offline][50][admin][smdp-admin-api]http://127.0.0.1:9008/admin/InstanceEvent/broadcast
2025-06-16 12:31:18.201[TIP][RetryInstance.Empty][Instance][50][admin][smdp-admin-api][URI]InstanceEvent/broadcast
2025-06-16 12:31:18.201[TIP][Instance.Post][Offline][43][es9plus][es9-00]http://127.0.0.1:9001/es9plus/InstanceEvent/broadcast
2025-06-16 12:31:18.202[TIP][RetryInstance.Empty][Instance][43][es9plus][es9-00][URI]InstanceEvent/broadcast
2025-06-16 12:31:18.202[TIP][Instance.Post][Offline][47][transfer][DP.Transfer]http://127.0.0.1:9004/transfer/InstanceEvent/broadcast
2025-06-16 12:31:18.202[TIP][RetryInstance.Empty][Instance][47][transfer][DP.Transfer][URI]InstanceEvent/broadcast
2025-06-16 12:34:33.841[TIP][ES2+]Starting
2025-06-16 12:34:33.855[TIP]Checking db connect...
2025-06-16 12:34:33.975[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_test, User=dp
2025-06-16 12:34:33.975[TIP]----------------------------------------------
2025-06-16 12:34:33.975[TIP]ES2+ v1.0.2(k1.0.6)
2025-06-16 12:34:33.975[TIP]----------------------------------------------
2025-06-16 12:34:34.488[TIP][ES2+][1.0.2][升级时间]2025-05-15 21:54:24
2025-06-16 12:34:34.489[TIP]当前已经是最新版本
2025-06-16 12:34:34.489[TIP][ES2+][GO_TEST][es2plus]Start success, used 648 milliseconds
2025-06-16 12:34:34.489[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-06-16 12:34:36.119[TIP][Instance][42][G][GO_TEST][http][127.0.0.1][9000][http][][0][C][hub][N][][W][1][D][hub-local-test]Group
2025-06-16 12:34:36.119[TIP][Instance][43][G][GO_TEST][http][127.0.0.1][9001][http][][0][C][es9plus][N][][W][1][D][es9-00]Group
2025-06-16 12:34:36.119[TIP][Instance][44][G][GO_TEST][http][127.0.0.1][9003][http][][0][C][es2plus][N][][W][1][D][es9-00]Register
2025-06-16 12:34:36.119[TIP][Instance][47][G][GO_TEST][http][127.0.0.1][9004][http][][0][C][transfer][N][][W][1][D][DP.Transfer]Group
2025-06-16 12:34:36.119[TIP][Instance][50][G][GO_TEST][http][127.0.0.1][9008][http][][0][C][admin][N][][W][1][D][smdp-admin-api]Group
2025-06-16 12:34:36.120[TIP][Instance.Post][Offline][50][admin][smdp-admin-api]http://127.0.0.1:9008/admin/InstanceEvent/broadcast
2025-06-16 12:34:36.120[TIP][RetryInstance.Empty][Instance][50][admin][smdp-admin-api][URI]InstanceEvent/broadcast
2025-06-16 12:34:36.120[TIP][Instance.Post][Offline][43][es9plus][es9-00]http://127.0.0.1:9001/es9plus/InstanceEvent/broadcast
2025-06-16 12:34:36.120[TIP][RetryInstance.Empty][Instance][43][es9plus][es9-00][URI]InstanceEvent/broadcast
2025-06-16 12:34:36.120[TIP][Instance.Post][Offline][47][transfer][DP.Transfer]http://127.0.0.1:9004/transfer/InstanceEvent/broadcast
2025-06-16 12:34:36.120[TIP][RetryInstance.Empty][Instance][47][transfer][DP.Transfer][URI]InstanceEvent/broadcast
2025-06-16 14:08:21.062[TIP]Checking db connect...
2025-06-16 14:08:21.287[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_test, User=dp
2025-06-16 14:08:21.288[TIP]----------------------------------------------
2025-06-16 14:08:21.288[TIP]ES2+ v1.0.2(k1.0.6)
2025-06-16 14:08:21.288[TIP]----------------------------------------------
2025-06-16 14:08:21.794[TIP][ES2+][1.0.2][升级时间]2025-05-15 21:54:24
2025-06-16 14:08:21.794[TIP]当前已经是最新版本
2025-06-16 14:08:21.794[TIP][ES2+][GO_TEST][es2plus]Start success, used 748 milliseconds
2025-06-16 14:08:21.794[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-06-16 14:08:22.991[TIP][Instance][42][G][GO_TEST][http][127.0.0.1][9000][http][][0][C][hub][N][][W][1][D][hub-local-test]Group
2025-06-16 14:08:22.991[TIP][Instance][43][G][GO_TEST][http][127.0.0.1][9001][http][][0][C][es9plus][N][][W][1][D][es9-00]Group
2025-06-16 14:08:22.992[TIP][Instance][44][G][GO_TEST][http][127.0.0.1][9003][http][][0][C][es2plus][N][][W][1][D][es9-00]Register
2025-06-16 14:08:22.992[TIP][Instance][47][G][GO_TEST][http][127.0.0.1][9004][http][][0][C][transfer][N][][W][1][D][DP.Transfer]Group
2025-06-16 14:08:22.992[TIP][Instance][50][G][GO_TEST][http][127.0.0.1][9008][http][][0][C][admin][N][][W][1][D][smdp-admin-api]Group
2025-06-16 14:08:22.993[TIP][Instance.Post][Offline][47][transfer][DP.Transfer]http://127.0.0.1:9004/transfer/InstanceEvent/broadcast
2025-06-16 14:08:22.993[TIP][RetryInstance.Empty][Instance][47][transfer][DP.Transfer][URI]InstanceEvent/broadcast
2025-06-16 14:08:22.993[TIP][Instance.Post][Offline][43][es9plus][es9-00]http://127.0.0.1:9001/es9plus/InstanceEvent/broadcast
2025-06-16 14:08:22.993[TIP][RetryInstance.Empty][Instance][43][es9plus][es9-00][URI]InstanceEvent/broadcast
2025-06-16 14:08:22.993[TIP][Instance.Post][Offline][50][admin][smdp-admin-api]http://127.0.0.1:9008/admin/InstanceEvent/broadcast
2025-06-16 14:08:22.993[TIP][RetryInstance.Empty][Instance][50][admin][smdp-admin-api][URI]InstanceEvent/broadcast
2025-06-16 14:12:33.519[TIP]Checking db connect...
2025-06-16 14:12:33.634[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_test, User=dp
2025-06-16 14:12:33.634[TIP]----------------------------------------------
2025-06-16 14:12:33.634[TIP]ES2+ v1.0.2(k1.0.6)
2025-06-16 14:12:33.634[TIP]----------------------------------------------
2025-06-16 14:12:34.138[TIP][ES2+][1.0.2][升级时间]2025-05-15 21:54:24
2025-06-16 14:12:34.139[TIP]当前已经是最新版本
2025-06-16 14:12:34.139[TIP][ES2+][GO_TEST][es2plus]Start success, used 636 milliseconds
2025-06-16 14:12:34.139[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-06-16 14:12:35.341[TIP][Instance][42][G][GO_TEST][http][127.0.0.1][9000][http][][0][C][hub][N][][W][1][D][hub-local-test]Group
2025-06-16 14:12:35.342[TIP][Instance][43][G][GO_TEST][http][127.0.0.1][9001][http][][0][C][es9plus][N][][W][1][D][es9-00]Group
2025-06-16 14:12:35.342[TIP][Instance][44][G][GO_TEST][http][127.0.0.1][9003][http][][0][C][es2plus][N][][W][1][D][es9-00]Register
2025-06-16 14:12:35.342[TIP][Instance][47][G][GO_TEST][http][127.0.0.1][9004][http][][0][C][transfer][N][][W][1][D][DP.Transfer]Group
2025-06-16 14:12:35.342[TIP][Instance][50][G][GO_TEST][http][127.0.0.1][9008][http][][0][C][admin][N][][W][1][D][smdp-admin-api]Group
2025-06-16 14:12:35.342[TIP][Instance.Post][Offline][50][admin][smdp-admin-api]http://127.0.0.1:9008/admin/InstanceEvent/broadcast
2025-06-16 14:12:35.343[TIP][RetryInstance.Empty][Instance][50][admin][smdp-admin-api][URI]InstanceEvent/broadcast
2025-06-16 14:12:35.343[TIP][Instance.Post][Offline][43][es9plus][es9-00]http://127.0.0.1:9001/es9plus/InstanceEvent/broadcast
2025-06-16 14:12:35.343[TIP][RetryInstance.Empty][Instance][43][es9plus][es9-00][URI]InstanceEvent/broadcast
2025-06-16 14:12:35.343[TIP][Instance.Post][Offline][47][transfer][DP.Transfer]http://127.0.0.1:9004/transfer/InstanceEvent/broadcast
2025-06-16 14:12:35.343[TIP][RetryInstance.Empty][Instance][47][transfer][DP.Transfer][URI]InstanceEvent/broadcast
2025-06-16 14:14:20.032[TIP]Checking db connect...
2025-06-16 14:14:20.121[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_test, User=dp
2025-06-16 14:14:20.121[TIP]----------------------------------------------
2025-06-16 14:14:20.121[TIP]ES2+ v1.0.2(k1.0.6)
2025-06-16 14:14:20.121[TIP]----------------------------------------------
2025-06-16 14:14:20.635[TIP][ES2+][1.0.2][升级时间]2025-05-15 21:54:24
2025-06-16 14:14:20.635[TIP]当前已经是最新版本
2025-06-16 14:14:20.636[TIP][ES2+][GO_TEST][es2plus]Start success, used 621 milliseconds
2025-06-16 14:14:20.636[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-06-16 14:14:21.691[TIP][Instance][42][G][GO_TEST][http][127.0.0.1][9000][http][][0][C][hub][N][][W][1][D][hub-local-test]Group
2025-06-16 14:14:21.692[TIP][Instance][43][G][GO_TEST][http][127.0.0.1][9001][http][][0][C][es9plus][N][][W][1][D][es9-00]Group
2025-06-16 14:14:21.692[TIP][Instance][44][G][GO_TEST][http][127.0.0.1][9003][http][][0][C][es2plus][N][][W][1][D][es9-00]Register
2025-06-16 14:14:21.693[TIP][Instance][47][G][GO_TEST][http][127.0.0.1][9004][http][][0][C][transfer][N][][W][1][D][DP.Transfer]Group
2025-06-16 14:14:21.693[TIP][Instance][50][G][GO_TEST][http][127.0.0.1][9008][http][][0][C][admin][N][][W][1][D][smdp-admin-api]Group
2025-06-16 14:14:21.693[TIP][Instance.Post][Offline][50][admin][smdp-admin-api]http://127.0.0.1:9008/admin/InstanceEvent/broadcast
2025-06-16 14:14:21.693[TIP][RetryInstance.Empty][Instance][50][admin][smdp-admin-api][URI]InstanceEvent/broadcast
2025-06-16 14:14:21.693[TIP][Instance.Post][Offline][43][es9plus][es9-00]http://127.0.0.1:9001/es9plus/InstanceEvent/broadcast
2025-06-16 14:14:21.693[TIP][RetryInstance.Empty][Instance][43][es9plus][es9-00][URI]InstanceEvent/broadcast
2025-06-16 14:14:21.693[TIP][Instance.Post][Offline][47][transfer][DP.Transfer]http://127.0.0.1:9004/transfer/InstanceEvent/broadcast
2025-06-16 14:14:21.693[TIP][RetryInstance.Empty][Instance][47][transfer][DP.Transfer][URI]InstanceEvent/broadcast
2025-06-17 16:48:39.492[TIP][ES2+]Starting
2025-06-17 16:48:39.510[TIP]Checking db connect...
2025-06-17 16:48:39.587[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_test, User=dp
2025-06-17 16:48:39.588[TIP]----------------------------------------------
2025-06-17 16:48:39.588[TIP]ES2+ v1.0.2(k1.0.6)
2025-06-17 16:48:39.588[TIP]----------------------------------------------
2025-06-17 16:48:40.094[TIP][ES2+][1.0.2][升级时间]2025-05-15 21:54:24
2025-06-17 16:48:40.095[TIP]当前已经是最新版本
2025-06-17 16:48:40.095[TIP][ES2+][GO_TEST][es2plus]Start success, used 602 milliseconds
2025-06-17 16:48:40.095[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-06-17 16:48:41.154[TIP][Instance][42][G][GO_TEST][http][127.0.0.1][9000][http][][0][C][hub][N][][W][1][D][hub-local-test]Group
2025-06-17 16:48:41.155[TIP][Instance][43][G][GO_TEST][http][127.0.0.1][9001][http][][0][C][es9plus][N][][W][1][D][es9-00]Group
2025-06-17 16:48:41.155[TIP][Instance][44][G][GO_TEST][http][127.0.0.1][9003][http][][0][C][es2plus][N][][W][1][D][es9-00]Register
2025-06-17 16:48:41.155[TIP][Instance][47][G][GO_TEST][http][127.0.0.1][9004][http][][0][C][transfer][N][][W][1][D][DP.Transfer]Group
2025-06-17 16:48:41.155[TIP][Instance][50][G][GO_TEST][http][127.0.0.1][9008][http][][0][C][admin][N][][W][1][D][smdp-admin-api]Group
2025-06-17 16:48:41.155[TIP][Instance.Post][Offline][50][admin][smdp-admin-api]http://127.0.0.1:9008/admin/InstanceEvent/broadcast
2025-06-17 16:48:41.155[TIP][RetryInstance.Empty][Instance][50][admin][smdp-admin-api][URI]InstanceEvent/broadcast
2025-06-17 16:48:41.156[TIP][Instance.Post][Offline][43][es9plus][es9-00]http://127.0.0.1:9001/es9plus/InstanceEvent/broadcast
2025-06-17 16:48:41.156[TIP][RetryInstance.Empty][Instance][43][es9plus][es9-00][URI]InstanceEvent/broadcast
2025-06-17 16:48:41.156[TIP][Instance.Post][Offline][47][transfer][DP.Transfer]http://127.0.0.1:9004/transfer/InstanceEvent/broadcast
2025-06-17 16:48:41.156[TIP][RetryInstance.Empty][Instance][47][transfer][DP.Transfer][URI]InstanceEvent/broadcast
2025-06-18 10:10:34.714[TIP]Checking db connect...
2025-06-18 10:10:34.791[TIP][DB connect check success]Host=dp.ezhayan.com, Port=9998, Db=smdp_test, User=dp
2025-06-18 10:10:34.791[TIP]----------------------------------------------
2025-06-18 10:10:34.791[TIP]ES2+ v1.0.2(k1.0.6)
2025-06-18 10:10:34.792[TIP]----------------------------------------------
2025-06-18 10:10:35.305[TIP][ES2+][1.0.2][升级时间]2025-05-15 21:54:24
2025-06-18 10:10:35.306[TIP]当前已经是最新版本
2025-06-18 10:10:35.306[TIP][ES2+][GO_TEST][es2plus]Start success, used 610 milliseconds
2025-06-18 10:10:35.306[TIP][ListenAddress][InstancePort]0.0.0.0:9003
2025-06-18 10:10:36.547[TIP][Instance][42][G][GO_TEST][http][127.0.0.1][9000][http][][0][C][hub][N][][W][1][D][hub-local-test]Group
2025-06-18 10:10:36.547[TIP][Instance][43][G][GO_TEST][http][127.0.0.1][9001][http][][0][C][es9plus][N][][W][1][D][es9-00]Group
2025-06-18 10:10:36.547[TIP][Instance][44][G][GO_TEST][http][127.0.0.1][9003][http][][0][C][es2plus][N][][W][1][D][es9-00]Register
2025-06-18 10:10:36.548[TIP][Instance][47][G][GO_TEST][http][127.0.0.1][9004][http][][0][C][transfer][N][][W][1][D][DP.Transfer]Group
2025-06-18 10:10:36.548[TIP][Instance][50][G][GO_TEST][http][127.0.0.1][9008][http][][0][C][admin][N][][W][1][D][smdp-admin-api]Group
2025-06-18 10:10:36.548[TIP][Instance.Post][Offline][50][admin][smdp-admin-api]http://127.0.0.1:9008/admin/InstanceEvent/broadcast
2025-06-18 10:10:36.548[TIP][RetryInstance.Empty][Instance][50][admin][smdp-admin-api][URI]InstanceEvent/broadcast
2025-06-18 10:10:36.549[TIP][Instance.Post][Offline][43][es9plus][es9-00]http://127.0.0.1:9001/es9plus/InstanceEvent/broadcast
2025-06-18 10:10:36.549[TIP][RetryInstance.Empty][Instance][43][es9plus][es9-00][URI]InstanceEvent/broadcast
2025-06-18 10:10:36.549[TIP][Instance.Post][Offline][47][transfer][DP.Transfer]http://127.0.0.1:9004/transfer/InstanceEvent/broadcast
2025-06-18 10:10:36.549[TIP][RetryInstance.Empty][Instance][47][transfer][DP.Transfer][URI]InstanceEvent/broadcast
