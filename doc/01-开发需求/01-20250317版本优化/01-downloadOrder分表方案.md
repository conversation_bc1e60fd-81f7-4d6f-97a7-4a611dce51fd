# DownloadOrder分区方案
## 1. 评估
DownloadOrder是SM-DP+业务表，平台在实现方式上，目前已经做到了按运营商的数据库实例分区，但是同一运营商，根据下载量评估:

| 每日下载 | 30天量  | 3个月量  | 6个月量  | 1年量    |
|------|-------|-------|-------|--------|
| 1W   | 30W   | 90W   | 180W  | 360W   |
| 5W   | 150W  | 450W  | 900W  | 1800W  |
| 50W  | 1500W | 4500W | 9000W | 18000W |

从上述评估，可以看出DownloadOrder表如果不做分区，后续将存在严重的性能瓶颈。

## 2. 分区方式
分区第一原则是生产效率优先，所以考虑把DownloadOrder生产中的数据和历史数据做拆分，同时考虑同一数据库实例可能多个运营商共存，所以增加运营商作为条件，最终分区的方式为依据以下字段：
1. expired_time 
ConfirmOrder或ReleaseProfile环节根据ProfileType设置值
2. state，DownloadOrder被删除之后的xxx时间内移动，xxx作为配置项，单位为天
3. tenant_id作为历史表的后缀
4. YearMonth=from_unixtime(create_time,'%Y%m')作为历史表后缀
用于区分不同运营商

最后分区之后的表名为：
1. rsp_download_order_TENANT_ID_YearMonth_h用于存储历史订单数据
2. rsp_download_order_t用于存储生产中的数据

## 3. 实现方式
### 3.1 在ES2中，设置定时任务，进行周期循环自动处理
参考代码：
xx.A.RegisterTimeCycleCallback("ProcessDownloadOrder", func() {
xx.doMoveHistory()
})
1. 定义原子布尔值用于唯一执行判断，atomic.Bool
2. 根据分区方式1和2查询满足条件的数据，每次最多xxx条。
3. 查询出的数据逐条处理：
    * 判断operate表是否存在，如果不存在则创建历史表（download_id不自增）
    * 开启事务
    * 删除生产表中对应ID的数据
    * 插入历史表中对应ID的数据
    * 提交事务
### 3.2 在Admin后台中，处理DownloadOrder查询
1. 增加标识，查询生产数据还是历史数据
2. 选择运营商
3. 历史数据查询按年月查询

### 3.3 开发实施
| 责任人    | 发布日期       | 发布版本 |
|--------|------------|------|
| Ken.Li | 2025-03-25 | ?    |

