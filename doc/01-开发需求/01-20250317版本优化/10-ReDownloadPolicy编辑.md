# ReDownloadPolicy编辑
## 需求
1. ProfileType上配置ReDownloadPolicy，DownloadOrder表上增加re_download_policy，取值从ProfileType上取
~~~sql
ALTER TABLE `rsp_download_order_t`
	ADD COLUMN `re_download_policy` INT(11) NULL DEFAULT '0' COMMENT '下载复用策略' AFTER `mode`;
~~~
2. download_order_max_success_count，配置最大允许成功下载次数，默认值为10（可参数配置）
3. is_default，是否为默认类型，同一个运营商，只允许存在一个

## 取值
1. 0:
* In Default SM-DP+, Activation Code and SM-DS modes, re-download is not allowed as per GSMA
specifications. 
* When receiving the INSTALLATION notification, the RSP platform will clear the Profile
content and retain the Profile record and metadata. 
* When receiving the DELETE notification, the RSP platform will update the order to ‘Removed’, and change the Profile to ‘Unavailable’.

2. 1:
* In Default SM-DP+, Activation Code and SM-DS modes, re-download is not allowed as per GSMA specifications. 
* When receiving the DELETE notification, the RSP platform will update the order to ‘Removed’, and change the Profile to ‘Unavailable’.

3. 2:
* In Default SM-DP+, Activation Code and SM-DS modes, re-download is not allowed as per GSMA specifications. 
* After receiving the DELETE notification, the RSP platform will update the order to ‘Removed’, and change the Profile to ‘Available’.

4. 3:
* Only re-download with the same EID is allowed. 
* Re-download does not rely on DELETE notification. 
* In the Default SM-DP+ and Activation Code modes, the Profile can be directly re-downloaded with the same EID regardless of the order status (‘Released’, ‘Downloaded’, or ‘Installed’). 
* If DELETE notification is received, Rule 4 will be executed.

5. 4:
* Only re-download with the same EID is allowed. 
* Re-download relies on DELETE notification. 
* In the Default SM-DP+ and Activation Code modes, after receiving DELETE notification, the RSP platform will reset the order to ‘Released’, the Profile can be re-downloaded with the same EID (in the SM-DS
  mode, when DELETE notification is received, the Rule 2 will be executed).

6. 5:
* Re-download with another EID is allowed. 
* Re-download relies on DELETE notification. In the Activation Code mode, after receiving the DELETE notification, the RSP platform will reset the order
  to ‘Released’, and unbinds the EID and Profile (only supported in the Activation Code mode, not supported in SM-DS and Default SM-DP+ modes).
## 开发
- Admin: Ken
- Es9+: Riken, 2025/03/26 23:12已经发布上线