# Device黑名单功能优化
## 1. 原因
Device作为终端用户设备在平台的数据保存，是需要和用户设备一一绑定的，目前设计为了考虑与MNO的黑名单功能，直接在表上增加了operator_id和tenant_id字段，这样不利于保持Device的独一性。
## 2. 表结构调整
1. dev_device_t表删除运营商相关及黑名单相关的字段。
   ~~~sql
        ALTER TABLE `dev_device_t`
	        DROP COLUMN `operator_id`,
	        DROP COLUMN `block_model`,
	        DROP COLUMN `block_category`,
	        DROP COLUMN `tenant_id`,
	        DROP COLUMN `create_name`,
	        DROP COLUMN `update_name`,
	        DROP INDEX `operator_id_eid`;
   ~~~

2. 新增dev_block_t表，记录运营商相关的墨名单配置
    ~~~
   CREATE TABLE `dev_block_t` (
   `do_id` BIGINT(20) NOT NULL,
   `device_id` BIGINT(20) NOT NULL DEFAULT '0',
   `operator_id` BIGINT(20) NOT NULL DEFAULT '0',
   `eid` VARCHAR(32) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
   `tac` VARCHAR(8) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
   `block_model` BIT(1) NULL DEFAULT NULL COMMENT '是否为禁用',
   `block_category` INT(11) NULL DEFAULT '0' COMMENT '禁用原因：0=EID，1=TAC',
   `tenant_id` VARCHAR(16) NULL DEFAULT '' COLLATE 'utf8mb4_general_ci',
   `create_by` INT(11) NULL DEFAULT NULL,
   `update_by` INT(11) NULL DEFAULT '0',
   `create_time` BIGINT(20) NULL DEFAULT '0',
   `update_time` BIGINT(20) NULL DEFAULT '0'
   )
   COLLATE='utf8mb4_general_ci'
   ENGINE=InnoDB
   ROW_FORMAT=DYNAMIC
   ;
    ~~~

## 3. 开发影响
1. 后台黑名单设置
2. Device查询，联合rsp_download_order_t过滤device_id查询
3. 增加设备黑名单列表、根据eid、tac查询，可以取消黑名单、加入黑名单
4. 接口

## 4. 开发人员
Ken

