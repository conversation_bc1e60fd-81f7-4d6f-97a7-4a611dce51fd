# Portal优化调整
## AccessLog
1. 筛选条件System与Instance做级联
2. Instance下拉选择按名称排序，ID放到最后面，格式如：ES2 01 01 (15)

## LPA Notification
1. 筛选条件增加：InstallErrorBppCommand，取值为：
   * initialiseSecureChannel(0),
   *  configureISDP(1),
   * storeMetadata(2),
   * storeMetadata2(3),
   * replaceSessionKeys(4),
   * loadProfileElements(5)

2. 筛选条件增加：InstallErrorReason，取值为：
   * incorrectInputValues(1),
   * invalidSignature(2),
   * invalidTransactionId(3),
   * unsupportedCrtValues(4),
   * unsupportedRemoteOperationType(5),
   * unsupportedProfileClass(6),
   * bspStructureError(7),
   * bspSecurityError(8),
   * installFailedDueToIccidAlreadyExistsOnEuicc(9),
   * installFailedDueToInsufficientMemoryForProfile(10),
   * installFailedDueToInterruption(11),
   * installFailedDueToPEProcessingError (12),
   * installFailedDueToDataMismatch(13),
   * testProfileInstallFailedDueToInvalidNaaKey(14),
   * pprNotAllowed(15),
   * enterpriseProfilesNotSupported(17), -- #SupportedForEnterpriseV3.0.0#
   * enterpriseRulesNotAllowed(18), -- #SupportedForEnterpriseV3.0.0#
   * enterpriseProfileNotAllowed(19), -- #SupportedForEnterpriseV3.0.0#
   * enterpriseOidMismatch(20), -- #SupportedForEnterpriseV3.0.0#
   * enterpriseRulesError(21), -- #SupportedForEnterpriseV3.0.0#
   * enterpriseProfilesOnly(22), -- #SupportedForEnterpriseV3.0.0#
   * lprNotSupported(23), -- #SupportedForLpaProxyV3.0.0#
   * unknownTlvInMetadata(26), -- #SupportedFromV3.0.0#
   * installFailedDueToUnknownError(127)

## AccessReport
1. 筛选条件System与Instance做级联
2. Instance下拉选择按名称排序，ID放到最后面，格式如：ES2 01 01 (15)

## EventManager
1. 列表支持多选删除
2. 筛选新增System查询

## Transaction
1. 筛选列表新增：TransactionId，通过Hex查询。参数传递到SQL时，在前面加一个0x