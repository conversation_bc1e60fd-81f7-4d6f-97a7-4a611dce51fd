# Portal LPA Notification
## 需求
菜单Monitor下增加LPA Notification，对应表rsp_lpa_notification_t，显示设备上报通知信息。
### 筛选
1. EID
2. ICCID
3. MatchingId

### 列
1. EID
2. Tenant
3. ICCID
4. MatchingId
5. SeqNumber
6. Category，取值：Install，Enable, Disable, Delete
6. InstallPpiResponse(显示TLV String)
7. InstallSuccessAid
8. InstallErrorBppCommandId，取值显示下面字符串
   ~~~cgo
   BppCommandId ::= INTEGER {
	initialiseSecureChannel(0),
	configureISDP(1),
	storeMetadata(2),
	storeMetadata2(3),
	replaceSessionKeys(4),
	loadProfileElements(5)
    }
    ~~~
9. InstallErrorReason，取值显示下面字符串
    ~~~cgo
    ErrorReason ::= INTEGER {
	incorrectInputValues(1),
	invalidSignature(2),
	invalidTransactionId(3),
	unsupportedCrtValues(4),
	unsupportedRemoteOperationType(5),
	unsupportedProfileClass(6),
	bspStructureError(7),
	bspSecurityError(8),
	installFailedDueToIccidAlreadyExistsOnEuicc(9),
	installFailedDueToInsufficientMemoryForProfile(10),
	installFailedDueToInterruption(11),
	installFailedDueToPEProcessingError (12),
	installFailedDueToDataMismatch(13),
	testProfileInstallFailedDueToInvalidNaaKey(14),
	pprNotAllowed(15),
	enterpriseProfilesNotSupported(17), -- #SupportedForEnterpriseV3.0.0#
	enterpriseRulesNotAllowed(18), -- #SupportedForEnterpriseV3.0.0#
	enterpriseProfileNotAllowed(19), -- #SupportedForEnterpriseV3.0.0#
	enterpriseOidMismatch(20), -- #SupportedForEnterpriseV3.0.0#
	enterpriseRulesError(21), -- #SupportedForEnterpriseV3.0.0#
	enterpriseProfilesOnly(22), -- #SupportedForEnterpriseV3.0.0#
	lprNotSupported(23), -- #SupportedForLpaProxyV3.0.0#
	unknownTlvInMetadata(26), -- #SupportedFromV3.0.0#
	installFailedDueToUnknownError(127)
    }
    ~~~