# Portal零星优化

## ProfileInventory
筛选条件少了Deleted，判断时需要为state=? or local_state=?

## DownloadOrder菜单优化
1. Download Order Report-->Report|统计报表
2. Download Order List-->Current Process|当前下载
3. Download Order History-->History|历史下载
当前下载和后续下载看客户反馈情况，可能需要合并到一起。

## Download Report优化
1. Device=concat(name,brand,model)
2. SGP22没有值
3. 筛选的State增加LocalState的三个值LocalEnabled,LocalDisabled,LocalDeleted，判断时download_order state=? or local_state=?
download_order我会增加local_state的冗余
4. 增加Detail按钮，功能和List里面一样。
5. 增加Model筛选

## Device
Device的列表三个字段分别取：
Brand-->brand
Name-->Name
Device Type-->Type-->model
已完成