# Portal DownloadOrder统计报表
## 需求
### 筛选条件
1. 同DownloadOrder列表
### 列表
1. MatchingId
2. EID
3. ICCID
4. ProfileType
5. State
6. Local State
7. CreateTime
8. DownloadTime
9. LocalEnableTime
10. DeleteTime
11. ExpiredTime
12. SuccessDownloadCount
13. AttemptCount
14. Device
15. SGP22
16. eUICC

### 逻辑
以DownloadOrder为主表
1. left join mno_profile_t，获取state和local_state
2. left join mno_profile_life_cycle_t，选择state=Downloaded的，按时间顺序排列的第一条作为DownloadTime
3. left join mno_profile_life_cycle_t，选择state=LocalEnabled，按时间顺序排列的第一条作为LocalEnableTime
4. left join mno_profile_life_cycle_t，选择state=Deleted，按时间顺序排列的第一条作为DeleteTime
5. left join dev_device_t on device_id，name,brand,model=Device, SGP22=lower_svn, eUICC=euicc_sas
排序：create_time desc

已完成