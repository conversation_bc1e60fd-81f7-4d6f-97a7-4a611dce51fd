# Portal Monitor Event按最新的表结构调整显示字段
## 需求
hub.log_event_t按如下调整了结构，请对应Portal的显示做相应的调整。权限放开，能查看详情。
~~~sql
ALTER TABLE `log_event_t`
CHANGE COLUMN `line` `no` INT(11) NULL DEFAULT NULL AFTER `clazz`,
CHANGE COLUMN `file` `msg` VARCHAR(1024) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci' AFTER `method`,
CHANGE COLUMN `description` `stack` TEXT NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci' AFTER `msg`,
DROP COLUMN `ip`,
DROP COLUMN `context`;
~~~
## 开发
少磊