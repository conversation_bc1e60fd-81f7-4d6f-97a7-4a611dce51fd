package main

import (
	"esim.common/src/bl"
	"esim.es2/src"
	"esim.es2/src/bl/manager"
	"esim.es2/src/ui"
	"k/app"
)

func main() {
	param := &app.Param{
		Name:    "ES2+",
		Context: "es2plus",
		Feature: app.FeatureDb | app.FeatureClient | app.FeatureVersion | app.FeatureLogEvent,
	}
	es2Param := &src.Es2Param{}
	param.SetClientParam(es2Param)
	param.VersionInfo = &src.Es2Version{}
	app.StartAppWithCallback(param, nil, func(a *app.App) {
		app.GetManager[*manager.DownloadOrderScheduledManager](a)
		app.GetManager[*manager.DownloadOrderReportManager](a)
		app.GetManager[*manager.ProfileReportManager](a)
		app.GetManager[*manager.ProfileStatisticManager](a)
		km := app.GetManager[*bl.KmsManager](a)
		km.Setup(es2Param.KmsApiKey, es2Param.KmsApiSecret, es2Param.KmsApiUrl, es2Param.KmsApiBackupUrl)
		a.RegisterEvent("/es2plus/", &ui.GsmaEvent{})
		a.RegisterEvent("/es2plus/profile", &ui.ProfileEvent{})
		a.RegisterEvent("/es2plus/profileType", &ui.ProfileTypeEvent{})
		a.RegisterEvent("/es2plus/order", &ui.OrderEvent{})
		a.RegisterEvent("/es2plus/device", &ui.DeviceEvent{})
	})
}
